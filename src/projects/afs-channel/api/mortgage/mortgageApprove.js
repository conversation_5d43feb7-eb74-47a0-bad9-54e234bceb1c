import axios from '@/libs/request/axios'

/**
 * 抵押撤销流程查询待办任务
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const queryTaskList = (params) => {
    return axios.request({
        url: '/case/onlineMortgage/queryTaskList',
        data: params,
        method: 'post'
    })
}

/**
 * 抵押撤销流程提交
 * @param {Object} params 流程参数
 * @returns {Promise}
 */
export const mortgageSubmit = (params) => {
    return axios.request({
        url: '/case/onlineMortgage/mortgageSubmit',
        data: params,
        method: 'post'
    })
}
