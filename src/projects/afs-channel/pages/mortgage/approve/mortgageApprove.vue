<template>
    <rui-page :defines="defines">
        <Card>
            <Row>
                <rui-query
                    :label-width="180"
                    ref="searchForm"
                    :query="queryData"
                    :query-rows="searchFormOptions"
                    :formData="searchData"
                >
                </rui-query>
            </Row>
            <Row>
                <rui-table
                    :defineId="'approveDefine'"
                    :showIndex="false"
                    :select="true"
                    @loadDatas="queryData"
                    @on-selection-change="changeSelect"
                    ref-query="searchForm"
                    ref="taskTable">
                  <template slot="toolBar">
                    <Button :loading="submitLoading" @click="taskSubmit('submit','通过')" type="success">通过</Button>
                    <Button :loading="submitLoading" @click="taskSubmit('back2dealer','退回')" type="warning">退回</Button>
                    <Button :loading="submitLoading" @click="taskSubmit('refuse','拒绝')" type="error">拒绝</Button>
                  </template>
                </rui-table>
            </Row>
        </Card>
    </rui-page>
</template>

<script>
import approveDefine from './defines/mortgageApproveDefine';
import {queryTaskList,mortgageSubmit} from '../../../api/mortgage/mortgageApprove';

export default {
    data() {
        return {
            packageId:this.afs.getPageParams(this).packageId,
            templateId:this.afs.getPageParams(this).templateId,
            taskNodeName: this.afs.getPageParams(this).taskName,
            userDefinedIndex: this.afs.getPageParams(this).userDefinedIndex,
            searchData:{},
            searchFormOptions: [
                {defineId: "approveDefine", span: 24, fields: ["applyNo", "mortgageType", "channelName", "ownerName", "vinNo", "agentName"]},
            ],
            selectList:[],
            selectCount:0,
            submitLoading:false,
        }
    },
    methods: {
        refresh() {
          this.$refs['taskTable'].reloadData();
          this.clearSelectAll();
        },
        clearSelectAll() {
          this.$refs.taskTable.getTable().selectAll(false);
        },
        changeSelect(e) {
          this.selectList = e;
          this.selectCount = e.length;
        },
        queryData(queryData) {
          queryData["condition"]["flowPackageId"] = this.packageId;
          queryData["condition"]["flowTemplateId"] = this.templateId;
          queryData["condition"]["taskNodeName"] = this.taskNodeName;
          queryData["condition"]["userDefinedIndex"] = this.userDefinedIndex;
          queryTaskList(queryData).then(res => {
            if (res.code === "0000") {
              let {records, total} = res.data;
              this.$refs.taskTable.updateTableData(records, total);
            }
          });
        },
        taskSubmit(type,desc) {
          if (!type) return;
          if (this.selectList.length<=0){
              this.$Message.error("请至少选择一条数据!");
              return;
          }
          this.$Modal.confirm({
            title: "确认",
            content: "您确定要" + desc + "这" + this.selectCount + "条数据吗?",
            onOk: () => {
              this.submitLoading = true;
              this.selectList.forEach(e => {
                e.operationType = type;
                e.operationRemark = desc;
              })
              mortgageSubmit(this.selectList).then(res => {
                if (res.code === "0000") {
                  this.$Message.success(res.data);
                  this.refresh();
                }
                this.submitLoading = false;
              }).catch((err) => {
                this.submitLoading = false;
              })
            }
          });
        },
    },
    mounted() {
        this.refresh();
    },
    computed: {
        defines() {
            return [
                {
                    id: 'approveDefine',
                    fields: approveDefine
                }
            ]
        }
    },
}
</script>

<style scoped>

</style>
