<template>
    <div class="mortgage-manage">
        <rui-page :defines="defines" @finished="pageInitFinished">
            <!-- 查询条件 -->
            <rui-query
                :label-width="140"
                ref="searchForm"
                :query="queryData"
                :query-rows="searchFormOptions"
            />

            <!-- 数据表格 -->
            <rui-table
                :defineId="'mortgageManageDefine'"
                :fixedRight="['action']"
                :showIndex="false"
                :select="false"
                :columns="tableColumns"
                :slots="[{key:'action',slot:'action'},{key:'applyNo',slot:'applyNo'}]"
                @loadDatas="queryData"
                ref-query="searchForm"
                ref="mortgageTable"
            >
                <template slot="toolBar">
                    <Button @click="handleAdd" type="primary" icon="md-add">新增抵押登记</Button>
                </template>
                <template slot="applyNo" slot-scope="{ row }">
                    <a style="color:#3086eb;cursor: pointer;" @click="handleView(row)">
                        {{row.applyNo}}
                    </a>
                </template>

                <template slot="action" slot-scope="{ row }">
                    <div>
                        <Button v-if="row.mortgageStatus ==='to_be_signed'" @click="handleSignRelation(row)"
                                size="small" type="primary" style="margin-right: 5px">签约
                        </Button>
                        <Button @click="attachment(row)" size="small" type="primary" style="margin-right: 5px">附件
                        </Button>
                        <Button v-if="row.mortgageStatus ==='mortgage_draft'" @click="handleEdit(row)" size="small"
                                type="primary" style="margin-right: 5px">编辑
                        </Button>
                        <Button @click="handleViewLog(row)" size="small" type="warning" style="margin-right: 5px">日志
                        </Button>
                        <Button
                            v-if="row.mortgageStatus ==='to_be_signed' || row.mortgageStatus ==='signing_completed' || row.mortgageStatus ==='mortgage_error' "
                            @click="handleRevoke(row)" size="small" type="error">撤销
                        </Button>
                    </div>
                </template>
            </rui-table>
        </rui-page>

        <!--  新增签约弹窗  -->
        <signRelation :isShowMessageModel="isShowMessageModel"
                          :signApplyNo="signApplyNo"
                          :mortgageType="mortgageType"
                          @closeMessage="closeMessage">
        </signRelation>

        <!-- 新增/编辑弹窗 -->
        <Modal
            :closable="false"
            :mask-closable="false"
            :title="modalTitle"
            :width="1000"
            v-model="modalVisible"
        >
            <Form ref="mortgageForm" :model="mortgageForm" :rules="formRules" :label-width="120">
                <Divider orientation="left">基本信息</Divider>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="抵押合同编号" prop="applyNo">
                            <Input v-model="mortgageForm.applyNo" :readonly="isView" placeholder="请输入抵押合同编号">
                                <Button slot="append" v-if="!isView" @click="handlePreCreate" type="primary">获取信息</Button>
                            </Input>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="抵押类型" prop="mortgageType">
                            <Select v-model="mortgageForm.mortgageType" :disabled="isView"
                                    placeholder="请选择抵押类型">
                                <Option v-for="item in dicDate.onlineMortgageType" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="车辆性质" prop="userType">
                            <Select v-model="mortgageForm.userType" :disabled="true"
                                    placeholder="请选择车辆性质">
                                <Option v-for="item in dicDate.onlineMortgageVehicleNature" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="抵押状态" prop="mortgageStatus">
                            <Select v-model="mortgageForm.mortgageStatus" :disabled="true"
                                    placeholder="请选择抵押状态">
                                <Option v-for="item in dicDate.onlineMortgageStatus" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="备注" prop="remark">
                            <Input v-model="mortgageForm.remark" :readonly="isView"
                                   type="textarea" :rows="3" placeholder="请输入备注"/>
                        </FormItem>
                    </Col>
                </Row>

                <Divider orientation="left">金融机构信息</Divider>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="金融机构" prop="financialInstitution">
                            <Input v-model="mortgageForm.financialInstitution" :readonly="true"
                                   placeholder="请输入金融机构"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="统一社会信用代码" prop="financialInstitutionUniqueCode">
                            <Input v-model="mortgageForm.financialInstitutionUniqueCode" :readonly="true"
                                   placeholder="请输入金融机构统一社会信用代码"/>
                        </FormItem>
                    </Col>
                </Row>

                <Divider orientation="left">所有人信息</Divider>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="所有人姓名" prop="ownerName">
                            <Input v-model="mortgageForm.ownerName" :readonly="true" placeholder="请输入所有人姓名"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="身份证明类型" prop="ownerIdType">
                            <Select v-model="mortgageForm.ownerIdType" :disabled="true"
                                    placeholder="请选择身份证明类型">
                                <Option v-for="item in dicDate.onlineMortgageIdType" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="身份证明号码" prop="ownerIdNo">
                            <Input v-model="mortgageForm.ownerIdNo" :readonly="true"
                                   placeholder="请输入身份证明号码"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="手机号码" prop="ownerPhone">
                            <Input v-model="mortgageForm.ownerPhone" :readonly="true" placeholder="请输入手机号码"/>
                        </FormItem>
                    </Col>
                </Row>

                <Divider orientation="left">车辆预上牌信息</Divider>
                <Row :gutter="16" style="margin-bottom: 10px;" v-if="!isView">
                    <Col span="24" style="text-align: right;">
                        <Button type="primary" @click="openPlaceModal" icon="md-search">选择预上牌地点</Button>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="车架号" prop="vinNo">
                            <Input v-model="mortgageForm.vinNo" :readonly="true" placeholder="请输入车架号"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="号牌种类" prop="plateType">
                            <Select v-model="mortgageForm.plateType" :disabled="true"
                                    placeholder="请选择号牌种类">
                                <Option v-for="item in dicDate.onlineMortgagePlateType" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="发证机关" prop="issuingAuthority">
                            <Input v-model="mortgageForm.issuingAuthority" :readonly="true"
                                   placeholder="请输入发证机关"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="预上牌信息" prop="plannedPlatePrefix">
                            <Input v-model="mortgageForm.plannedPlatePrefix" :readonly="true"
                                   placeholder="请输入预上牌信息"/>
                        </FormItem>
                    </Col>
                </Row>

                <Divider orientation="left">代理人信息（可选）</Divider>
                <Row :gutter="16" style="margin-bottom: 10px;" v-if="!isView">
                    <Col span="24" style="text-align: right;">
                        <Button type="primary" @click="openProxyModal" icon="md-search">选择代理人</Button>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="代理人姓名" prop="agentName">
                            <Input v-model="mortgageForm.agentName" :readonly="true" placeholder="请输入代理人姓名"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="身份证明类型" prop="agentIdType">
                            <Select v-model="mortgageForm.agentIdType" :disabled="true"
                                    placeholder="请选择身份证明类型">
                                <Option v-for="item in dicDate.onlineMortgageIdType" :value="item.value">
                                    {{ item.title }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Col>
                </Row>
                <Row :gutter="16">
                    <Col span="12">
                        <FormItem label="代理人身份证明号码" prop="agentIdNo">
                            <Input v-model="mortgageForm.agentIdNo" :readonly="true"
                                   placeholder="请输入代理人身份证明号码"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="代理人联系电话" prop="agentPhone">
                            <Input v-model="mortgageForm.agentPhone" :readonly="true"
                                   placeholder="请输入代理人联系电话"/>
                        </FormItem>
                    </Col>
                </Row>
            </Form>

            <div slot="footer">
                <Button @click="handleCancel">取消</Button>
                <Button v-if="!isView" @click="handleSubmit('save')" type="primary" :loading="submitLoading">保存</Button>
                <Button v-if="!isView" @click="handleSubmit('approve')" type="primary" :loading="submitLoading">提交</Button>
            </div>
        </Modal>

        <!-- 抵押代理人选择弹窗 -->
        <Modal
            v-model="proxyModalVisible"
            title="选择抵押代理人"
            width="900"
            :mask-closable="false"
        >
            <div class="proxy-modal-content">
                <Form :model="proxySearchForm" inline>
                    <FormItem>
                        <Input v-model="proxySearchForm.applyNo" placeholder="请输入申请编号/代理人姓名" style="width: 200px" />
                    </FormItem>
                    <FormItem>
                        <Button type="primary" @click="searchProxyList">查询</Button>
                    </FormItem>
                </Form>
                <Table :columns="proxyColumns" :data="proxyData" :loading="proxyLoading" @on-row-click="selectProxy">
                    <template slot-scope="{ row }" slot="action">
                        <Button type="primary" size="small" @click="selectProxy(row)">选择</Button>
                    </template>
                </Table>
                <div class="page-wrapper">
                    <Page
                        :total="proxyTotal"
                        :current="proxySearchForm.pageNumber"
                        :page-size="proxySearchForm.pageSize"
                        @on-change="changeProxyPage"
                        @on-page-size-change="changeProxyPageSize"
                        show-total
                        show-sizer
                    />
                </div>
            </div>
            <div slot="footer">
                <Button @click="proxyModalVisible = false">取消</Button>
            </div>
        </Modal>

        <Modal title="影像上传" v-model="userModalVisible" :mask-closable='false' :width="1200"  :styles="{top: '90px'}">
            <div>
                <file-operation v-if="userModalVisible" :uploadParam="uploadParam" :isInt="canUpload" :path="path"
                                :read-only="readonly"></file-operation>
            </div>
            <div slot="footer">
                <Button size="small" type="primary" style="margin-right: 5px" @click="initiateApplication">发起申请</Button>
                <Button type="text" @click="cancelUser">关闭</Button>
            </div>
        </Modal>

        <!-- 车辆、发证机关和预上牌信息选择弹窗 -->
        <Modal
            v-model="placeModalVisible"
            title="选择车辆配置信息"
            width="900"
            :mask-closable="false"
        >
            <div class="place-modal-content">
                <Form :model="placeSearchForm" inline>
                    <FormItem>
                        <Input v-model="placeSearchForm.city" placeholder="请输入城市" style="width: 150px" />
                    </FormItem>
                    <FormItem>
                        <Button type="primary" @click="searchPlaceList">查询</Button>
                    </FormItem>
                </Form>
                <Table :columns="placeColumns" :data="placeData" :loading="placeLoading" @on-row-click="selectPlace">
                    <template slot-scope="{ row }" slot="action">
                        <Button type="primary" size="small" @click="selectPlace(row)">选择</Button>
                    </template>
                </Table>
                <div class="page-wrapper">
                    <Page
                        :total="placeTotal"
                        :current="placeSearchForm.pageNumber"
                        :page-size="placeSearchForm.pageSize"
                        @on-change="changePlacePage"
                        @on-page-size-change="changePlacePageSize"
                        show-total
                        show-sizer
                    />
                </div>
            </div>
            <div slot="footer">
                <Button @click="placeModalVisible = false">取消</Button>
            </div>
        </Modal>

        <!-- 操作日志查看弹窗 -->
        <Modal
            v-model="logModalVisible"
            title="操作日志"
            width="800"
            :mask-closable="false"
        >
            <div class="log-modal-content">
                <div v-if="logLoading" class="log-loading">
                    <Spin size="large">
                        <Icon type="ios-loading" size=18 class="spin-icon-load"></Icon>
                        <div>加载中...</div>
                    </Spin>
                </div>
                <div v-else-if="logData.length === 0" class="log-empty">
                    <Icon type="ios-information-circle-outline" size="48" color="#c5c8ce"></Icon>
                    <p>暂无操作日志</p>
                </div>
                <div v-else class="log-timeline">
                    <Timeline>
                        <TimelineItem v-for="(item, index) in logData" :key="index" :color="getLogColor(index)">
                            <Icon type="ios-checkmark-circle" slot="dot" size="16"></Icon>
                            <div class="log-item">
                                <div class="log-content">{{ item.log }}</div>
                                <div class="log-time" v-if="item.createTime">
                                    <Icon type="ios-time-outline" size="14"></Icon>
                                    {{ formatTime(item.createTime) }}
                                </div>
                            </div>
                        </TimelineItem>
                    </Timeline>
                </div>
            </div>
            <div slot="footer">
                <Button @click="logModalVisible = false">关闭</Button>
            </div>
        </Modal>

        <!-- 撤销确认弹窗 -->
        <Modal
            v-model="revokeModalVisible"
            title="撤销确认"
            :closable="false"
            :mask-closable="false"
            :width="500"
        >
            <div style="padding: 20px 0;">
                <Alert type="warning" show-icon style="margin-bottom: 20px;">
                    <span slot="desc">
                        <p style="color: #ed4014; font-weight: bold; margin-bottom: 10px;">
                            请再次确认是否要撤销该抵押登记？
                        </p>
                        <p><strong>申请编号：</strong>{{ currentRevokeRow.applyNo }}</p>
                        <p><strong>抵押类型：</strong>{{ getMortgageTypeText(currentRevokeRow.mortgageType) }}</p>
                    </span>
                </Alert>

                <Form :model="revokeForm" :rules="revokeRules" ref="revokeForm" :label-width="100">
                    <FormItem label="撤销原因" prop="revokeReason">
                        <Input
                            v-model="revokeForm.revokeReason"
                            type="textarea"
                            :rows="4"
                            placeholder="请输入撤销原因说明（必填）"
                            :maxlength="500"
                            show-word-limit
                        />
                    </FormItem>
                </Form>
            </div>

            <div slot="footer">
                <Button @click="cancelRevoke">取消</Button>
                <Button
                    @click="confirmRevoke"
                    type="error"
                    :loading="revokeLoading"
                >
                    确定撤销
                </Button>
            </div>
        </Modal>
    </div>
</template>

<script>
import mortgageManageDefine from './defines/mortgageManageDefine'
import FileOperation from "../../../../basic/pages/image/upload-file/fileOperation.vue";
import {getByTypes} from "../../../api/affiliated/affiliated";
import {
    getMortgageList,
    saveOrUpdateMortgage,
    preCreateMortgage,
    queryApplyMortgageProxyPage,
    pageMortgageConfig,
    getMortgageLogList,
    mortgageStart,
    initiateApplication
} from '../../../api/mortgage-filing/mortgageManage'
import signRelation from "./signRelation.vue";
import {getFilterParams} from "_p/afs-apply/api/apply-report/personal/common";
import {getUploadList} from "_p/basic/api/image/image-upload";

export default {
    name: 'MortgageManage',
    components: {FileOperation, signRelation},
    computed: {
        defines() {
            return [
                {
                    id: "mortgageManageDefine",
                    fields: mortgageManageDefine
                }
            ];
        }
    },
    data() {
        return {
            // 字典
            dicKey:['onlineMortgageStatus','onlineMortgageType','onlineMortgageVehicleNature','onlineMortgageIdType','onlineMortgagePlateType'],
            dicDate:{},
            // 查询表单配置
            searchFormOptions: [
                {
                    defineId: "mortgageManageDefine",
                    span: 8,
                    fields: ["applyNo", "mortgageStatus","ownerName"]
                },
                {
                    defineId: "mortgageManageDefine",
                    span:8,
                    fields: ["vinNo","mortgageType","ownerIdNo",]
                },
                {
                    defineId: "mortgageManageDefine",
                    span: 8,
                    fields: ["agentIdNo", "agentName"]
                }
            ],
            // 表格列配置
            tableColumns: [
                "action","applyNo", "mortgageType","mortgageStatus",
                "ownerName", "ownerIdNo", "ownerPhone", "vinNo",
                "plannedPlatePrefix", "agentName", "agentPhone","agentIdNo","remark","failureReason"
            ],
            // 弹窗相关
            modalVisible: false,
            isShowMessageModel: false,
            openTabs: 'name2',
            path:'apply',
            readonly:false,
            canUpload:false,
            extInfo:{},
            uploadParam:{
                busiNo:"",
                belongNo:"",
                busiType: 'agentSign',
                busiData:{}
            },
            fileShow:false,
            applyNo: '',
            signApplyNo:'',
            mortgageType: '',
            modalTitle: '',
            userModalVisible: false,
            isView: false,
            submitLoading: false,

            // 抵押代理人选择弹窗相关
            proxyModalVisible: false,
            proxyLoading: false,
            proxyData: [],
            proxyTotal: 0,
            proxySearchForm: {
                applyNo: '',
                pageNumber: 1,
                pageSize: 10
            },
            userForm: {
                id: "",
                affiliatedName: '',
                socUniCrtCode: '',
                legalPersonName: '',
                legalPersonPhone: '',
                account: '',
                fileId: '',
                affiliatedUnitStatus: '',
                enterpriseThreeElementsResult: '',
                enterpriseCertification: '',
                enterpriseReverseCertification: '',
            },
            proxyColumns: [
                {
                    title: '申请编号',
                    key: 'applyNo',
                    width: 150
                },
                {
                    title: '姓名',
                    key: 'customerName',
                    width: 120
                },
                {
                    title: '证件号码',
                    key: 'certNo',
                    width: 180
                },
                {
                    title: '手机号',
                    key: 'telPhone',
                    width: 150
                },
                {
                    title: '操作',
                    slot: 'action',
                    width: 80,
                    align: 'center'
                }
            ],

            // 抵押配置选择弹窗相关
            placeModalVisible: false,
            placeLoading: false,
            placeData: [],
            placeTotal: 0,
            placeSearchForm: {
                issuingAuthority: '',
                city: '',
                platePrefix: '',
                pageNumber: 1,
                pageSize: 10
            },
            placeColumns: [
                {
                    title: '发证机关',
                    key: 'issuingAuthority',
                    width: 200
                },
                {
                    title: '城市',
                    key: 'city',
                    width: 120
                },
                {
                    title: '号牌头',
                    key: 'platePrefix',
                    width: 100
                },
                {
                    title: '操作',
                    slot: 'action',
                    width: 80,
                    align: 'center'
                }
            ],
            // 表单数据
            mortgageForm: {
                id: null,
                applyNo: '',
                mortgageType: '',
                signTime: null,
                userType: '',
                issuingTime: null,
                expirationDate: null,
                financialInstitution: '',
                financialInstitutionUniqueCode: '',
                ownerName: '',
                ownerIdType: '',
                ownerIdNo: '',
                ownerPhone: '',
                vinNo: '',
                plateType: '',
                issuingAuthority: '',
                plannedPlatePrefix: '',
                agentNo: '',
                agentName: '',
                agentIdType: '',
                agentIdNo: '',
                agentPhone: '',
                mortgageeAgentName: '',
                mortgageeAgentIdType: '',
                mortgageeAgentIdNo: '',
                mortgageeAgentPhone: '',
                mortgageStatus: '',
                remark: ''
            },
            // 表单验证规则
            formRules: {
                applyNo: [
                    {required: true, message: '请输入抵押合同编号', trigger: 'blur'}
                ],
                mortgageType: [
                    {required: true, message: '请选择抵押类型', trigger: 'change'}
                ],
                userType: [
                    {required: true, message: '请选择车辆性质', trigger: 'change'}
                ],
                financialInstitution: [
                    {required: true, message: '请输入金融机构', trigger: 'blur'}
                ],
                financialInstitutionUniqueCode: [
                    {
                        required: true,
                        message: '请输入正确的统一社会信用代码',
                        trigger: 'blur'
                    }
                ],
                ownerName: [
                    {required: true, message: '请输入所有人姓名', trigger: 'blur'}
                ],
                ownerIdType: [
                    {required: true, message: '请选择身份证明类型', trigger: 'change'}
                ],
                ownerIdNo: [
                    {required: true, message: '请输入身份证明号码', trigger: 'blur'},
                    {
                        required: true,
                        message: '请输入正确的身份证号码',
                        trigger: 'blur'
                    }
                ],
                ownerPhone: [
                    {required: true, message: '请输入手机号码', trigger: 'blur'},
                    {required: true, message: '请输入正确的手机号码', trigger: 'blur'}
                ],
                vinNo: [
                    {required: true, message: '请输入车架号', trigger: 'blur'},
                    {min: 17, max: 17, message: '车架号必须为17位', trigger: 'blur'}
                ],
                plateType: [
                    {required: true, message: '请输入号牌种类', trigger: 'blur'}
                ],
                issuingAuthority: [
                    {required: true, message: '请选择', trigger: 'blur'}
                ],
                plannedPlatePrefix: [
                    {required: true, message: '请选择', trigger: 'blur'}
                ],
                agentPhone: [
                    {message: '请输入正确的手机号码', trigger: 'blur'}
                ],
                mortgageeAgentPhone: [
                    {
                        required: true,
                        message: '请输入正确的手机号码',
                        trigger: 'blur'}
                ],
                mortgageStatus: [
                    {required: true, message: '请选择抵押状态', trigger: 'change'}
                ]
            },

            // 操作日志弹窗相关
            logModalVisible: false,
            logLoading: false,
            logData: [],
            currentLogApplyNo: '',
            currentLogType: '',

            // 撤销确认弹窗相关
            revokeModalVisible: false,
            revokeLoading: false,
            currentRevokeRow: {},
            revokeForm: {
                revokeReason: ''
            },
            revokeRules: {
                revokeReason: [
                    { required: true, message: '请输入撤销原因说明', trigger: 'blur' },
                    { min: 10, message: '撤销原因说明至少需要10个字符', trigger: 'blur' }
                ]
            }
        }
    },
    mounted() {
        // 页面挂载完成，但需要等待rui-page组件初始化完成
        console.log('页面已挂载，等待rui-page初始化完成...');
    },
    methods: {
        // rui-page组件初始化完成回调
        pageInitFinished() {
            // 页面组件初始化完成后，自动加载数据
            this.initLoadData();
            this.initDataDic();

            // 检查是否来自订单管理页面的抵押申请
            this.checkAutoCreateMortgage();
        },
        initDataDic() {
            getByTypes(this.dicKey).then(res => {
                if (res.code === '0000' && res.data) {
                    this.dicDate = res.data
                }
            });
        },
        // 初始化加载数据
        initLoadData() {
            console.log('开始初始化加载数据...');
            // 使用$nextTick确保DOM已更新
            this.$nextTick(() => {
                if (this.$refs.mortgageTable) {
                    console.log('表格组件已准备好，开始加载数据');
                    this.$refs.mortgageTable.reloadData();
                } else {
                    console.log('表格组件未准备好，延迟100ms后重试');
                    // 如果表格组件还没有准备好，延迟执行
                    setTimeout(() => {
                        this.initLoadData();
                    }, 100);
                }
            });
        },

        checkAutoCreateMortgage() {
            const routeParams = this.$route.params;

            // 检查是否来自订单管理页面且需要自动创建
            if (routeParams && routeParams.fromOrderMng && routeParams.autoCreate && routeParams.applyNo) {
                // 延迟执行，确保页面完全加载
                this.$nextTick(() => {
                    setTimeout(() => {
                        this.autoCreateMortgageFromOrder(routeParams.applyNo);
                    }, 500);
                });
            }
        },

        // 从订单管理自动创建抵押申请
        autoCreateMortgageFromOrder(applyNo) {
            // 直接调用新增方法
            this.handleAdd();

            // 等待弹窗打开后，自动填入申请编号并获取信息
            this.$nextTick(() => {
                setTimeout(() => {
                    if (this.modalVisible) {
                        // 填入申请编号
                        this.mortgageForm.applyNo = applyNo;
                        // 自动调用预创建方法获取基础信息
                        this.handlePreCreate();
                    }
                }, 300);
            });
        },

        // 刷新数据
        refreshData() {
            if (this.$refs.mortgageTable) {
                this.$refs.mortgageTable.reloadData();
            }
        },


        // 查询数据
        queryData(queryData) {
            console.log('开始查询数据，参数:', queryData);

            // 构建详细查询条件
            const condition = {
                pageNumber: queryData.pageNumber || 1,
                pageSize: queryData.pageSize || 10,
                ...queryData.condition
            };

            console.log('详细查询条件:', condition);

            getMortgageList(condition).then(res => {
                console.log('查询数据响应:', res);
                if (res.code === "0000") {
                    let {records, total} = res.data;
                    console.log('查询成功，记录数:', records ? records.length : 0, '总数:', total);
                    this.$refs.mortgageTable.updateTableData(records, total);
                } else {
                    console.error('查询失败:', res.msg);
                    this.$Message.error(res.msg || '查询失败');
                }
            }).catch(error => {
                console.error('查询数据失败:', error);
                this.$Message.error('查询数据失败');
            });
        },

        // 新增
        handleAdd() {
            this.modalTitle = '新增抵押登记';
            this.isView = false;
            this.resetForm();
            this.modalVisible = true;

            // 弹窗打开后，提示用户输入申请编号以获取预填充信息
            this.$nextTick(() => {
                this.$Message.info('请先输入抵押合同编号，然后可以通过选择按钮获取相关信息');
            });
        },

        // 预创建抵押 - 根据申请编号获取基础信息
        handlePreCreate() {
            if (!this.mortgageForm.applyNo) {
                this.$Message.warning('请先输入抵押合同编号');
                return;
            }

            const params = {
                applyNo: this.mortgageForm.applyNo
            };

            preCreateMortgage(params).then(res => {
                if (res.code === "0000") {
                    // 用返回的数据填充表单
                    const data = res.data;
                    this.mortgageForm = {
                        ...this.mortgageForm,
                        userType: data.userType || '',
                        financialInstitution: data.financialInstitution || '',
                        financialInstitutionUniqueCode: data.financialInstitutionUniqueCode || '',
                        ownerName: data.ownerName || '',
                        ownerIdType: data.ownerIdType || 'A',
                        ownerIdNo: data.ownerIdNo || '',
                        ownerPhone: data.ownerPhone || '',
                        vinNo: data.vinNo || '',
                        plateType: data.plateType || '',
                        mortgageStatus: data.mortgageStatus
                    };
                    this.$Message.success('基础信息已自动填充');
                }
            });
        },

        // 编辑
        handleEdit(row) {
            this.modalTitle = '编辑抵押登记';
            this.isView = false;
            this.mortgageForm = {...row};
            this.modalVisible = true;
        },

        // 电子签约
        handleSignRelation(row) {
            this.applyNo = row.applyNo;
            this.signApplyNo = row.signApplyNo;
            this.mortgageType = row.mortgageType;
            this.isShowMessageModel = true;
        },

        // 查看
        handleView(row) {
            this.modalTitle = '查看抵押登记';
            this.isView = true;
            this.mortgageForm = {...row};
            this.modalVisible = true;
        },

        closeMessage(){
            this.isShowMessageModel = false;
        },

        // 查看日志
        handleViewLog(row) {
            if (!row.applyNo) {
                this.$Message.warning('该记录缺少申请编号，无法查看日志');
                return;
            }
            this.currentLogApplyNo = row.applyNo;
            this.currentLogType = row.mortgageType;
            this.logModalVisible = true;
            this.loadLogData();
        },

        // 加载日志数据
        loadLogData() {
            this.logLoading = true;
            this.logData = [];

            const params = {
                applyNo: this.currentLogApplyNo,
                mortgageType: this.currentLogType
            };

            getMortgageLogList(params).then(res => {
                this.logLoading = false;
                if (res.code === "0000") {
                    this.logData = res.data || [];
                    // 按创建时间倒序排列，最新的在前面
                    this.logData.sort((a, b) => {
                        if (!a.createTime && !b.createTime) return 0;
                        if (!a.createTime) return 1;
                        if (!b.createTime) return -1;
                        return new Date(b.createTime) - new Date(a.createTime);
                    });
                } else {
                    this.$Message.error(res.msg || '获取日志失败');
                }
            }).catch(error => {
                this.logLoading = false;
                console.error('获取日志失败:', error);
                this.$Message.error('获取日志失败');
            });
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            const date = new Date(time);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },

        // 获取日志颜色（时间线颜色）
        getLogColor(index) {
            const colors = ['#19be6b', '#2d8cf0', '#ff9900', '#ed4014', '#9b59b6'];
            return colors[index % colors.length];
        },

        // 撤销操作
        handleRevoke(row) {
            if (!row.applyNo) {
                this.$Message.warning('该记录缺少申请编号，无法执行撤销操作');
                return;
            }

            if (!row.mortgageType) {
                this.$Message.warning('该记录缺少抵押类型，无法执行撤销操作');
                return;
            }

            // 保存当前要撤销的行数据
            this.currentRevokeRow = row;
            // 重置撤销表单
            this.revokeForm.revokeReason = '';
            // 显示撤销确认弹窗
            this.revokeModalVisible = true;
        },

        // 取消撤销
        cancelRevoke() {
            this.revokeModalVisible = false;
            this.currentRevokeRow = {};
            this.revokeForm.revokeReason = '';
            // 重置表单验证状态
            this.$refs.revokeForm.resetFields();
        },

        // 确认撤销
        confirmRevoke() {
            // 验证表单
            this.$refs.revokeForm.validate((valid) => {
                if (valid) {
                    this.executeRevoke(this.currentRevokeRow);
                } else {
                    this.$Message.error('请完善撤销原因说明');
                }
            });
        },

        // 执行撤销操作
        executeRevoke(row) {
            this.revokeLoading = true;

            const params = {
                applyNo: row.applyNo,
                mortgageType: row.mortgageType,
                revokeRemark: this.revokeForm.revokeReason // 添加撤销原因
            };

            this.$Message.loading({
                content: '正在执行撤销操作...',
                duration: 0
            });

            mortgageStart(params).then(res => {
                this.$Message.destroy();
                this.revokeLoading = false;

                if (res.code === "0000") {
                    this.$Message.success('撤销操作执行成功');
                    // 关闭撤销确认弹窗
                    this.revokeModalVisible = false;
                    this.currentRevokeRow = {};
                    this.revokeForm.revokeReason = '';
                    // 刷新列表数据
                    this.$refs.mortgageTable.reloadData();
                } else {
                    this.$Message.error(res.msg || '撤销操作失败');
                }
            }).catch(error => {
                this.$Message.destroy();
                this.revokeLoading = false;
                console.error('撤销操作失败:', error);
                this.$Message.error('撤销操作失败，请稍后重试');
            });
        },

        getMortgageTypeText(mortgageType) {
            const typeMap = {
                'mortgage': '抵押',
                'releaseMortgage': '解押'
            };
            return typeMap[mortgageType] || mortgageType;
        },

        cancelUser() {
            this.userModalVisible = false;
        },

        initiateApplication() {
            this.submitLoading = true;
            let data = {
                applyNo:  this.userForm.applyNo,
                mortgageType: this.userForm.mortgageType,
            }
            initiateApplication(data).then(res => {
                this.submitLoading = false;
                if (res.code === "0000") {
                    this.$Message.success('发起成功');
                    this.$refs.mortgageTable.reloadData();
                } else {
                    this.$Message.error(res.msg || '操作失败');
                }
            }).catch(() => {
                this.submitLoading = false;
                this.$Message.error('操作失败');
            });
        },

        attachment(v) {
            this.modalType = 1;
            this.openTabs= 'name2'
            // 转换null为""
            for (let attr in v) {
                if (v[attr] === null) {
                    v[attr] = "";
                }
            }
            if(v.mortgageStatus === 'signing_completed'){
                this.readonly = false  //判断 附件是否只读
            }else {
                this.readonly = true
            }
            let str = JSON.stringify(v);
            this.userForm = JSON.parse(str);
            this.initBusiness(v);
            this.userModalVisible = true;
        },

        initBusiness(data) {
            let params = {
                applyNo: data.signApplyNo,
            }
            getFilterParams(params).then(res => {
                if (res.code === '0000') {
                    this.uploadParam.busiData = res.data;
                    this.uploadParam.busiNo =data.signApplyNo;
                    this.uploadParam.belongNo = data.signApplyNo;
                    this.canUpload=true;
                }
            });
        },

        // 提交
        handleSubmit(opr) {
            this.$refs.mortgageForm.validate((valid) => {
                if (valid) {
                    console.log("saveOrUpdateMortgage"+JSON.stringify(this.mortgageForm))
                    this.submitLoading = true;
                    if(opr === 'approve'){
                        this.mortgageForm.approve = '1';
                    }
                    saveOrUpdateMortgage(this.mortgageForm).then(res => {
                        this.submitLoading = false;
                        if (res.code === "0000") {
                            this.$Message.success('操作成功');
                            this.modalVisible = false;
                            this.$refs.mortgageTable.reloadData();
                        } else {
                            this.$Message.error(res.msg || '操作失败');
                        }
                    }).catch(() => {
                        this.submitLoading = false;
                        this.$Message.error('操作失败');
                    });
                }
            });
        },

        // 取消
        handleCancel() {
            this.modalVisible = false;
            this.resetForm();
        },

        // 打开抵押代理人选择弹窗
        openProxyModal() {
            if (!this.mortgageForm.applyNo) {
                this.$Message.warning('请先输入抵押合同编号');
                return;
            }
            this.proxyModalVisible = true;
            this.searchProxyList();
        },

        // 查询抵押代理人列表
        searchProxyList() {
            this.proxyLoading = true;
            const params = {
                ...this.proxySearchForm,
                pageNumber: this.proxySearchForm.pageNumber,
                pageSize: this.proxySearchForm.pageSize,
                // 只查有效的
                status: "submit"
            };

            queryApplyMortgageProxyPage(params).then(res => {
                this.proxyLoading = false;
                if (res.code === "0000") {
                    this.proxyData = res.data.records || [];
                    this.proxyTotal = res.data.total || 0;
                } else {
                    this.$Message.error(res.msg || '查询抵押代理人失败');
                }
            }).catch(() => {
                this.proxyLoading = false;
                this.$Message.error('查询抵押代理人失败');
            });
        },

        // 选择抵押代理人
        selectProxy(row) {
            this.mortgageForm.agentName = row.customerName || '';
            this.mortgageForm.agentIdNo = row.certNo || '';
            this.mortgageForm.agentPhone = row.telPhone || '';
            this.mortgageForm.agentNo = row.applyNo || '';
            this.proxyModalVisible = false;
        },

        // 抵押代理人分页相关
        changeProxyPage(page) {
            this.proxySearchForm.pageNumber = page;
            this.searchProxyList();
        },

        changeProxyPageSize(size) {
            this.proxySearchForm.pageSize = size;
            this.proxySearchForm.pageNumber = 1;
            this.searchProxyList();
        },

        // 打开车辆配置信息选择弹窗
        openPlaceModal() {
            if (!this.mortgageForm.applyNo) {
                this.$Message.warning('请先输入抵押合同编号');
                return;
            }
            this.placeModalVisible = true;
            this.searchPlaceList();
        },

        // 查询车辆配置信息列表
        searchPlaceList() {
            this.placeLoading = true;
            const params = {
                condition: {
                    ...this.placeSearchForm
                },
                pageNumber: this.placeSearchForm.pageNumber,
                pageSize: this.placeSearchForm.pageSize
            };

            pageMortgageConfig(params).then(res => {
                this.placeLoading = false;
                if (res.code === "0000") {
                    this.placeData = res.data.records || [];
                    this.placeTotal = res.data.total || 0;
                } else {
                    this.$Message.error(res.msg || '查询车辆配置信息失败');
                }
            }).catch(() => {
                this.placeLoading = false;
                this.$Message.error('查询车辆配置信息失败');
            });
        },

        // 选择车辆配置信息
        selectPlace(row) {
            this.mortgageForm.issuingAuthority = row.issuingAuthority || '';
            this.mortgageForm.plannedPlatePrefix = row.city + '-' + row.platePrefix || '';
            this.placeModalVisible = false;
        },

        // 车辆配置信息分页相关
        changePlacePage(page) {
            this.placeSearchForm.pageNumber = page;
            this.searchPlaceList();
        },

        changePlacePageSize(size) {
            this.placeSearchForm.pageSize = size;
            this.placeSearchForm.pageNumber = 1;
            this.searchPlaceList();
        },

        // 重置表单
        resetForm() {
            this.mortgageForm = {
                id: null,
                applyNo: '',
                mortgageType: '',
                signTime: null,
                userType: '',
                issuingTime: null,
                expirationDate: null,
                financialInstitution: '',
                financialInstitutionUniqueCode: '',
                ownerName: '',
                ownerIdType: '',
                ownerIdNo: '',
                ownerPhone: '',
                vinNo: '',
                plateType: '',
                issuingAuthority: '',
                plannedPlatePrefix: '',
                agentName: '',
                agentIdType: '',
                agentIdNo: '',
                agentPhone: '',
                mortgageeAgentName: '',
                mortgageeAgentIdType: '',
                mortgageeAgentIdNo: '',
                mortgageeAgentPhone: '',
                mortgageStatus: '',
                remark: ''
            };
            this.$nextTick(() => {
                this.$refs.mortgageForm && this.$refs.mortgageForm.resetFields();
            });
        },
    }

}
</script>

<style lang="less" scoped>
.mortgage-manage {
    padding: 20px;
}

.proxy-modal-content,
.place-modal-content {
    padding: 10px;
}

.page-wrapper {
    margin-top: 15px;
    text-align: right;
}

/* 表格行鼠标悬停效果 */
/deep/ .ivu-table-row:hover {
    cursor: pointer;
    background-color: #f0f7ff;
}

/* 日志弹窗样式 */
.log-modal-content {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.log-loading {
    text-align: center;
    padding: 50px 0;
    color: #999;
}

.log-empty {
    text-align: center;
    padding: 50px 0;
    color: #c5c8ce;
}

.log-empty p {
    margin-top: 10px;
    font-size: 14px;
}

.log-timeline {
    padding: 10px 0;
}

.log-item {
    padding-left: 10px;
}

.log-content {
    font-size: 14px;
    color: #333;
    line-height: 1.6;
    margin-bottom: 8px;
    background: #f8f9fa;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 3px solid #2d8cf0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.log-time {
    font-size: 12px;
    color: #999;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 时间线样式优化 */
/deep/ .ivu-timeline-item-content {
    top: -6px;
}

/deep/ .ivu-timeline-item-tail {
    border-left: 2px solid #e8eaec;
}

/deep/ .ivu-timeline-item-head-custom {
    background: #fff;
    border: 2px solid;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}
/deep/ .ivu-input-wrapper{ width: 200px !important;}
/deep/ .ivu-select{ width: 200px !important;}
</style>
