<template>
    <!--订单管理-放款申请页面-->
    <div class="applyList contractApplyMng">
        <cancelOrder :query-change="queryChange" :row-data="contractNoAndApplyStatus" v-model="cancelVisible" ref="cancelOrder"/>
        <rui-page :defines="defines" :has-card-box="false" class="contractApply">
            <!-- 查询条件 -->
            <rui-query :label-width="100" isApplyQuery ref="contractMngLoanForm" :query="queryData"
                       :query-rows="searchFormOptions" :more-rows="searchMoreOptions">
                <template slot="applyQueryHead">
                    <Button style="margin-left:15px " :disabled="operationLoading" @click="exportOrderEvent" type="primary"><p style="font-size: 12px;">导出</p></Button>
                    <circleLoading v-if="operationLoading"/>
                </template>
            </rui-query>
            <!--根据状态查询订单信息 -->
            <div class="table-card">
                <div class="btn-status flex flex-between">
                    <div class="loanApplyBtn">
                        <template v-for="(item,index) in contractApply">
                            <Button :key="index" :class="activeBtn === item.key ? 'activeBtn btn' :'btn'" size="default"
                                    @click="applyStatusQuery(item.key)">{{ item.title }}
                            </Button>
                        </template>
                    </div>
                </div>

                <!--放款申请详情显示页面-->
                <rui-table
                    :defineId="'contractMngLoanApplication'"
                    :fixed-right="['action']"
                    :showIndex="false"
                    :select="false"
                    :columns="detailsColumns"
                    :slots="[{key:'action',slot:'action'},{key:'applyNo',slot:'applyNo'},{key:'contractApplyStatus',slot: 'contractApplyStatus'}]"
                    @loadDatas="queryData"
                    ref-query="contractMngLoanForm"
                    ref="contractMngLoanTable"
                    :border="false"
                    :fixedLeft="['applyNo']"
                >
                    <template slot-scope="{ row, index }" slot="applyNo">
                        <a style="color:#3086eb;cursor: pointer;" @click="skipDetail(row)">
                            {{row.applyNo}}
                        </a>
                        <Tooltip max-width="80" v-if="row.paymentStatus === 'successPayment'" content="合同放款成功" placement="left">
                            <Icon type="ios-warning" class="ios-warning"/>
                        </Tooltip>
                    </template>
                    <template slot-scope="{ row, index,dataDic }" slot="contractApplyStatus">
                        <Badge v-if="row.contractApplyStatus==='01'" text="待提交" color="#e6a23c"/>
                        <Badge v-if="row.contractApplyStatus==='02'" text="已提交" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='03'" text="审核中" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='04'" text="审核通过" color="#67c23a"/>
                        <Badge v-if="row.contractApplyStatus==='05'" text="已退回" color="#f56c6c"/>
                        <Badge v-if="row.contractApplyStatus==='06'" text="合同取消" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='07'" text="合同关闭" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='09'" text="失效" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='10'" text="待提交前" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='11'" text="取消放款中" color="#999"/>
                        <Badge v-if="row.contractApplyStatus==='refuse'" text="拒绝" color="#f25d4e"/>
                    </template>
                    <template slot="action" slot-scope="{ row }">
                        <Button    style="margin-right:5px"  v-if="row.contractApplyStatus==='04' && row.startDate != null"  type="primary" size="small" @click="repeal(row)">还款计划
                        </Button>
                        <Button    style="margin-right:5px"  v-if="row.contractApplyStatus==='04' && row.startDate != null"  type="primary" size="small" @click="onlineMortgage(row)">在线抵押
                        </Button>
                        <Dropdown style="text-align: center" :transfer="true" class="btn-no-border"
                                  transfer-class-name="btn-no-border dropdown-hover-bg afs-header-transfer"
                        >
                            <Button ghost type="primary" :disabled="!dropdownMenuHasMore(row)">更多</Button>
                            <DropdownMenu slot="list" v-show="dropdownMenuHasMore(row)">
                                <DropdownItem
                                    v-if="(row.systemType==='' || row.systemType===undefined || row.systemType==='0')&&$store.getters.permissions['apply_loan_edit']&&(row.contractApplyStatus=='01'||row.contractApplyStatus=='05')"
                                    @click.native="edit(row)" size="small">编辑
                                </DropdownItem>
                                <DropdownItem v-if="$store.getters.permissions['apply_loan_transfer_order']&&(row.contractApplyStatus==='05'
                                    || row.contractApplyStatus==='01') &&(roleCode.includes('ROLE_NEW_CHANNEL_ADMIN')||roleCode.includes('ROLE_OLD_CHANNEL_ADMIN')
                                        ||roleCode.includes('ROLE_NEW_DIRECT_ADMIN') || roleCode.includes('ROLE_OLD_DIRECT_ADMIN')) && row.createBy!=''"
                                              @click.native=" transfers(row)
                                " size="small">转单
                                </DropdownItem>
                                <DropdownItem
                                    v-if="(row.systemType==='' || row.systemType===undefined || row.systemType==='0')&&$store.getters.permissions['apply_loan_cancel']&&username!=='admin'
                                    && (row.contractApplyStatus==='01' || row.contractApplyStatus==='05' || (row.contractApplyStatus==='11' &&row.cannelStatus==='04'))"
                                    @click.native="cancelOrder(row)" size="small">取消放款
                                </DropdownItem>
                                <DropdownItem
                                    v-if="$store.getters.permissions['apply_loan_review']&&username!=='admin'"
                                    @click.native="showApproveDetails(row)" size="small">审批进度
                                </DropdownItem>
                            </DropdownMenu>
                        </Dropdown>
                    </template>
                </rui-table>
            </div>
            <!--撤回-->
            <Modal v-model="backModalVisible"
                   title="撤回页面"
                   :width="1100"
                   :mask-closable="false" @on-ok="backSubmit">
                <rui-form :form-options="backFormOptions"
                          :read-only="false"
                          ref="backForm">
                </rui-form>
            </Modal>
            <!--转单-->
            <Modal v-model="transferModalVisible"
                   title="转单页面"
                   :width="1100"
                   @on-cancel="onTurnBack"
                   :mask-closable="false">
                <rui-query :label-width="120"
                           ref="transferForm"
                           :query="transferData"
                           :query-rows="transferFormOptions">
                </rui-query>
                <Button @click.native="refreshUserData" icon="ios-refresh">刷新</Button>
                <rui-table :defineId="'contractMngLoanApplication'"
                           :showIndex="false"
                           :slots="[{key:'action',slot:'action'}]"
                           :columns="transferColumns"
                           @loadDatas="transferData"
                           ref-query="transferForm"
                           ref="transferTable">
                    <template slot="action" slot-scope="{ row }">
                        <Button type="primary" @click.native="transferSubmit(row)">转单</Button>
                    </template>
                </rui-table>
                <div slot="footer">
                    <Button type="primary" @click.native="onTurnBack">返回</Button>
                </div>
            </Modal>
            <!--弹出审批流程窗口-->
            <Modal v-model="approveDetailsVisible" :width="500" :mask-closable="false" title="订单审批进度">
                <!--审批详情头部-->
                <Card style="background-color:#FFCC66">
                    <Col>
                        <p>承租人：{{approveForm.custName}}</p>
                        <p style="padding-top: 5px;">申请编号：{{approveForm.applyNo}}</p>
                        <p style="padding-top: 5px;">融资租赁产品：{{approveForm.productName}}</p>
                        <p style="padding-top: 5px;">租赁金额：{{approveForm.loanAmt}}</p>
                        <p style="padding-top: 5px;">租赁期限：{{approveForm.loanTerm}}</p>
                        <p style="padding-top: 5px;">品牌：{{approveForm.brandName}}</p>
                        <p style="padding-top: 5px;">制造商：{{approveForm.seriesGroupName}}</p>
                        <p style="padding-top: 5px;">车系：{{approveForm.seriseName}}</p>
                        <p style="padding-top: 5px;">车型：{{approveForm.modelName}}</p>
                    </Col>
                </Card>

                <!--审批流程时间线显示-->
                <Card style="padding-top: 10px;">
                    <Timeline v-for="(item,index) in approveDetailsForm" :key="index">
                        <TimelineItem><Icon type="ios-arrow-down" slot="dot" v-show="index<5"/>
                            <Icon type="ios-trophy" slot="dot" v-show="index==5"/>
<!--                            {{item.approveNode}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{item.approveDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{item.disposeStaff}}</TimelineItem>-->
                            {{item.approveNode}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{item.approveDate}}</TimelineItem>
                    </Timeline>
                </Card>
                <div slot="footer">
                    <Button type="primary" @click.native="approveBack">返回</Button>
                </div>
            </Modal>
            <repay-plans :contract-no="repayPlansQueryContractNo" v-model="repayPlansVisible"/>
        </rui-page>
    </div>
</template>

<script>
    import contractMngLoanApplication from "../../api/order-mng/contractLoanApplication";
    import {
        getLoanOrderInfo,
        getSpecialInfo,
        saveBack,
        saveCancel,
        searchOprInfoForAssertChange,
        exportLoanOrder
    } from "../../api/order-mng/contractLoanApplicationApi.js";
    import {getUserInfo} from "../../../basic/api/admin/user";
    import {deepClone} from "../../../../libs/utils/ObjectClone";
    import {changeLoanReport, getTransferUser, getUserRolesByCondition,getListGeneralloanRecords} from "../../api/order-mng/loanApplyMngApi";
    import {transferData} from "../../api/order-mng/contractLoanApplicationApi";
    import RepayPlans from "../post-loan/repayPlans";
    import {checkApplyInfo} from "_p/afs-apply/api/apply-contract/contract/special/specialBusiness";
    import circleLoading from "_c/circle-loading/circle-loading.vue";
    import cancelOrder from "_p/afs-apply/pages/cancel/cancelOrder.vue";

    export default {
        computed: {
            defines() {
                return [{id: "contractMngLoanApplication", fields: contractMngLoanApplication}];
            }
        },
        components: {
            cancelOrder,
            RepayPlans,
            circleLoading
        },
        mounted() {
            let obj = this.afs.getPageParams(this);
            if (obj) {
                if (obj.flag == "home") {
                    if (obj.contractApplyStatus) {
                        this.applyStatusQuery(obj.contractApplyStatus);
                    }
                } else {
                    this.refresh();
                }
            } else {
                this.refresh();
            }
            this.initGteUserInfo();
        },
        data() {

            return {
                applyNo: '',
                approveDetailsForm:[],
                name: "contractMngLoanApplication",
                backModalVisible: false,
                transferModalVisible: false,
                businessType: '',
                disable: false,
                deptId: '',//部门id
                transferDatas: {},//查询转单人员条件
                roleCode: [],//
                roleCodes: '',
                username: '',
                userRealName: '',
                pageData: {
                    pageNumber: 1,
                    pageSize: 10,
                    total: 0
                },
                queryChange: {
                    pageNumber: 1,
                    pageSize: 10,
                    condition: {
                        applyNo: '',
                    }
                },
                queryDate: {},
                count: 5,
                activeBtn: null, //订单状态按钮
                contractApply: [
                    {key: null, title: '全部'},
                    {key: '01', title: '待提交'},
                    {key: '02', title: '已提交'},
                    {key: '03', title: '审核中'},
                    {key: '04', title: '审核通过'},
                    {key: '05', title: '已退回'},
                    {key: '06', title: '合同取消'},
                    {key: 'refuse', title: '拒绝'},
                    {key: '09', title: '失效'},
                    {key: '11', title: '取消放款中'},
                    {key: '30', title: '资方订单'},
                ],
                //订单管理-放款申请-多条件查询options
                searchStatus: {
                    pageNumber: 1,
                    pageSize: 10,
                    condition: {
                        contractApplyStatus: ""
                    }
                },
                searchFormOptions: [
                    {
                       // isGroup: false,
                        defineId: "contractMngLoanApplication",
                        // fields: ["applyNo", "custName", "businessType"],
                        fields: ["key"],
                    }
                ],
                searchMoreOptions: [

                    {
                        defineId: "contractMngLoanApplication",
                        fields: ["applyNo", "custName", "businessType", "applyDateStart", "applyDateEnd", "operateWay", "carType", "carNature", "affiliatedWay","startDateStart","startDateEnd"],
                    }
                ],

                //列表详情显示字段
                detailsColumns: ["custName", "applyNo", "businessType", "carType", "carNature", "loanTerm", "loanAmt", "carVin", "riskPassDate", "lendingFirstDate", "startDate", "backWord","contractApplyStatus", "realName", "action","contractNo","belongingCapital"],
                //撤回
                backFormOptions: [
                    {
                        isGroup: false,
                        groupName: '撤回页面',
                        grids: [
                            {defineId: "contractMngLoanApplication", fields: ["backReason"]}
                        ]
                    }
                ],
                //转单查询
                transferFormOptions: [
                    {
                        defineId: "contractMngLoanApplication",
                        fields: ["userRealName", "phone"]
                    }
                ],
                //转单
                transferColumns: [
                    'userRealName', 'username', 'phone', 'action'
                ],
                approveDetailsVisible: false,
                approveForm:[],//审批记录
                repayPlansVisible:false,
                repayPlansQueryContractNo:"",
                operationLoading:false,

                cancelVisible: false,
                contractNoAndApplyStatus: {
                    contractNo: "",
                    contractApplyStatus: "",
                }, // 传输行数据到取消放款界面

                // 在线抵押参数
                onlineMortgageParam: {}
            };
        },
        watch: {
            '$route'(to, from) {
                if (from.path.indexOf("contract-edit") > -1 || from.path.indexOf("contract-gpsAgainDistribute") > -1) {
                    this.refresh();
                }
            }
        },
        //方法
        methods: {
            exportOrderEvent() {

                  //no params
                  let self = this;
                    let params={
                        pageNumber:'0',
                        pageSize:'-1',
                        condition:this.$refs.contractMngLoanForm.getFormData().condition,
                    }
                  this.operationLoading = true;
                  try {
                  exportLoanOrder(params).then(res => {
                        if (res.status === 200) {
                            self.afs.downloadFile(res);
                        }
                         this.operationLoading = false;
                  })
                  } catch (error) {
                         this.operationLoading = false;
                  }

            },
            //点击弹出审批流程信息
            showApproveDetails(v){
                this.approveDetailsVisible=true;
                this.getWorkFlowRecordInfos(v);
            },
            //查询审批记录信息
            getWorkFlowRecordInfos(v){
                let params={
                    applyNo:v.applyNo,
                }
                getListGeneralloanRecords(params).then(res=>{
                    this.approveForm=res.data;
                    this.approveDetailsForm = res.data.voList;
                    console.log(this.approveForm,"审批流程信息")
                });
            },
            //关闭审批流程窗口
            approveBack(){
                this.approveDetailsVisible=false;
            },
            dropdownMenuHasMore(row) {
                const {$store, username, roleCode} = this;
                if ($store.getters.permissions['apply_loan_withdraw'] && row.contractApplyStatus === '02' && username !== 'admin') {
                    return true;
                }
                if ($store.getters.permissions['apply_loan_cancel']&&username!=='admin'
                    && (row.contractApplyStatus==='01'||row.contractApplyStatus==='05'||(row.contractApplyStatus==='11'&&row.cannelStatus==='04'))
                ) {

                    return true;
                }
                if ($store.getters.permissions['apply_loan_transfer_order'] && (row.contractApplyStatus === '05'
                    || row.contractApplyStatus === '01') && (roleCode.includes('ROLE_NEW_CHANNEL_ADMIN') || roleCode.includes('ROLE_OLD_CHANNEL_ADMIN')
                    || roleCode.includes('ROLE_NEW_DIRECT_ADMIN') || roleCode.includes('ROLE_OLD_DIRECT_ADMIN')) && row.createBy!='') {
                    return true;
                }
                if ($store.getters.permissions['apply_loan_edit'] && (row.contractApplyStatus == '01' || row.contractApplyStatus == '05'))
                    {
                    return true;
                }
                if ($store.getters.permissions['apply_loan_second_order'] && typeof row.startDate=='undefined'
                    && row.contractApplyStatus !== '06') {
                    return true;
                }
                if ($store.getters.permissions['apply_loan_review'] && row.contractApplyStatus !== '01')
                   {
                    return true;
                }
                return false;
            },
            //放款-订单管理，跳转合同录入界面
            skipDetail(row) {
                let contractType = true;
                if (typeof row.startDate === "undefined") {
                    contractType = true;

                } else {
                    let thisTime = row.startDate.replace(/-/g, '/');
                    let Time = new Date(thisTime);

                    let time = ((new Date()).getTime() - Time.getTime()) / 1000 / 3600 / 24;
                    if (time >= 15) {
                        contractType = false;
                    }
                }
                let param = {
                    applyNo: row.applyNo,
                    contractNo: row.contractNo,
                    diffType: "details",
                    type: 1,
                    contractType: contractType,
                    row,
                    onlyCheck: true
                };
                this.afs.newTab(this, 'projects/afs-apply/pages/apply-contract/contract/entry/contract-entry', '放款申请', 'ios-add',
                    param, 'contract-entry' + param.applyNo, [], true);
            },
            //二次派单
            distributeTwo(row) {
                let param = {
                    applyNo: row.applyNo,
                };
                this.afs.newTab(this, 'projects/afs-apply/pages/apply-contract/contract/gps/gpsAgainDistribute', 'GPS二次派单', 'ios-add',
                    param, 'contract-gpsAgainDistribute' + param.applyNo, [], true);
            },
            //根据多条件分页查询放款申请订单信息
            queryData(queryData) {
                this.queryDate = queryData;
                queryData.condition.contractApplyStatus = this.searchStatus.condition.contractApplyStatus;
                getLoanOrderInfo(queryData).then(res => {
                    if (res.code === "0000") {
                        if (typeof res.data !== "undefined") {
                            let {records, total} = res.data;
                            this.$refs.contractMngLoanTable.updateTableData(records, total);
                        } else {
                            this.$refs.contractMngLoanTable.updateTableData([], 0);
                        }
                    }
                });
            },

            edit(row) {
                this.queryChange.condition.applyNo = row.applyNo;
                searchOprInfoForAssertChange(this.queryChange).then(res => {
                    let flag = true;
                    if (res.code === "0000") {
                        if (res.data.length > 0) {
                            for (let i = 0; i < res.data.length; i++) {
                                if (res.data[i].applyType === "ASSERTCHANGE" && res.data[i].applyStatus !== "05" && res.data[i].applyStatus !== "06" && res.data[i].applyStatus !== "04" &&
                                    res.data[i].applyStatus !== "07") {
                                    this.$Message.error("该订单正在申请资产变更，不可操作！");
                                    flag = false;
                                    return;
                                }
                            }
                            if (flag) {
                                let contractType = true;
                                if (typeof row.startDate === "undefined") {
                                    contractType = true;

                                } else {
                                    let thisTime = row.startDate.replace(/-/g, '/');
                                    let Time = new Date(thisTime);

                                    let time = ((new Date()).getTime() - Time.getTime()) / 1000 / 3600 / 24;
                                    if (time >= 15) {
                                        contractType = false;
                                    }
                                }
                                let turn = "contract-edit" + row.applyNo;
                                let param = {
                                    applyNo: row.applyNo,
                                    type: 0,
                                    diffType:'edit',
                                    contractType: contractType,
                                    contractNo: row.contractNo,
                                    contractStatus: row.contractApplyStatus,
                                    row,
                                }
                                this.afs.newTab(this, 'projects/afs-apply/pages/apply-contract/contract/entry/contract-entry', '放款申请', 'ios-add',
                                    param,
                                    turn, [], true)
                            }
                        } else {
                            let contractType = true;
                            if (row.startDate){
                                let thisTime = row.startDate.replace(/-/g, '/');
                                let Time = new Date(thisTime);

                                let time = ((new Date()).getTime() - Time.getTime()) / 1000 / 3600 / 24;
                                if (time >= 15) {
                                    contractType = false;
                                }
                            }
                            let turn = "contract-edit" + row.applyNo;
                            let param = {
                                applyNo: row.applyNo,
                                type: 0,
                                diffType:'edit',
                                contractType: contractType,
                                contractNo: row.contractNo,
                                contractStatus: row.contractApplyStatus,
                                row,
                            }
                            this.afs.newTab(this, 'projects/afs-apply/pages/apply-contract/contract/entry/contract-entry', '放款申请', 'ios-add',
                                param,
                                turn, [], true)
                        }
                        let params={
                            applyNo: row.applyNo,
                        }
                        this.changeLoanReport(params);
                    }
                });
            },
            //跳转特殊业务页面
            jumpToSpTap(row) {
                let param = {
                    applyNo: row.applyNo,
                    submitType:"1",
                };
                param.contractNo=row.contractNo;
                checkApplyInfo(param).then(res => {
                    if (res.code == "0000") {
                        this.afs.newTab(this, 'projects/afs-apply/pages/apply-contract/contract/special/specialBusiness', '特殊业务申请', 'ios-add', param, 'specialLoanBusiness' + row.applyNo, [], true)
                    }else {
                        this.$Message.error(res.data);
                    }
                });            },
            //根据申请状态查询订单信息
            applyStatusQuery(v) {
                this.activeBtn = v;
                this.searchStatus.condition = this.$refs.contractMngLoanForm.getFormData().condition;
                this.searchStatus.condition.contractApplyStatus = v;
                this.searchStatus.pageSize = this.pageData.pageSize;
                this.searchStatus.pageNumber = this.pageData.pageNumber;
                getLoanOrderInfo(this.searchStatus).then(res => {
                    if (res.code === "0000") {
                        if (typeof res.data !== "undefined") {
                            let {records, total} = res.data;
                            this.$refs.contractMngLoanTable.pageData.pageNumber = 1;
                            this.$refs.contractMngLoanTable.updateTableData(records, total);
                        } else {
                            this.$refs.contractMngLoanTable.updateTableData([], 0);
                        }
                    }
                });
            },
            //如果加急次数用完，置灰按钮
            urgentCount() {
                this.count--;
                if (this.count === 0) {
                    console.log(this.count)
                    this.disable = true;
                }
            },
            //初始化获得用户信息
            initGteUserInfo() {
                this.roleCode=[];
                var role = new Set();
                // ROLE_SALE_LOAN 放款预录单员  ROLE_OFFICE_WORKER_LOAN 放款报单员
                var roleCodeArr = new Set(['ROLE_SALE_LOAN', 'ROLE_OFFICE_WORKER_LOAN', 'ROLE_NEW_CHANNEL_ADMIN', 'ROLE_OLD_CHANNEL_ADMIN', 'ROLE_OLD_DIRECT_ADMIN', 'ROLE_NEW_DIRECT_ADMIN']);
                getUserInfo().then(res => {
                    if (res.code === '0000') {
                        let userExtInfo = res.data.userExtInfo;
                        let roles = deepClone(userExtInfo.roles)
                        roles.forEach(res => {
                            //新车/二手车角色
                            if (roleCodeArr.has(res.roleCode)) {
                                this.roleCode.push(res.roleCode);
                            }
                        });
                        let sysUser = res.data.sysUser;
                        this.deptId = deepClone(sysUser.deptId);
                        this.username = sysUser.username;
                    }
                })
            },
            //刷新
            refreshUserData() {
                this.$refs['transferTable'].reloadData();
            },
            //转单查询人员信息
            transferData(transferData) {
                // this.userRealName = transferData.condition.userRealName;
                console.log(transferData, 'transferData')
                console.log(this.businessType, 'this.businessType')
                console.log(this.roleCode, 'this.roleCode')
                if (this.businessType === "01") {
                    this.businessType = "newCar";
                    if (this.roleCode.length > 0) {
                        for (let i = 0; i < this.roleCode.length; i++) {
                            if (this.roleCode[i] === "ROLE_SALE_LOAN") {
                                this.roleCodes = "ROLE_SALE_LOAN";
                            } else if (this.roleCode[i] === "ROLE_OFFICE_WORKER_LOAN") {
                                this.roleCodes = "ROLE_OFFICE_WORKER_LOAN";
                            }
                        }
                    }
                } else if (this.businessType === "02") {
                    this.businessType = "oldCar";
                    if (this.roleCode.length > 0) {
                        for (let i = 0; i < this.roleCode.length; i++) {
                            if (this.roleCode[i] === "ROLE_OFFICE_WORKER_LOAN") {
                                this.roleCodes = "ROLE_OFFICE_WORKER_LOAN";
                            } else if (this.roleCode[i] === "ROLE_SALE_LOAN") {
                                this.roleCodes = "ROLE_SALE_LOAN";
                            }
                        }
                    }
                }
                // 先判断管理员角色  //二手车渠道管理员 //二手车直营管理员//新车渠道管理员 //新车直营管理员
                if (this.roleCode.includes("ROLE_NEW_CHANNEL_ADMIN") || this.roleCode.includes("ROLE_OLD_CHANNEL_ADMIN")
                    || this.roleCode.includes("ROLE_NEW_DIRECT_ADMIN") || this.roleCode.includes("ROLE_OLD_DIRECT_ADMIN")) {
                    let params = {
                        username: this.usernameCreateBy,
                        businessType: this.businessType
                    }
                    //再判断订单操作人角色
                    getUserRolesByCondition(params).then(res => {
                        if (res.code === '0000') {
                            let records = res.data;
                            console.log(records);
                            let roleCode = '';
                            records.forEach(res => {
                                if (res.roleCode === "ROLE_SALE_LOAN") {
                                    //新车放款预录单员
                                    roleCode = 'ROLE_SALE_LOAN';
                                    this.roleCode.push('ROLE_SALE_LOAN')
                                } else if (res.roleCode === "ROLE_OFFICE_WORKER_LOAN") {
                                    //新车放款报单员
                                    roleCode = 'ROLE_OFFICE_WORKER_LOAN';
                                    this.roleCode.push('ROLE_OFFICE_WORKER_LOAN');
                                }
                            });
                            this.transferDatas = {
                                roleCode: roleCode,
                                username: this.username,
                                userRealName: transferData.condition.userRealName,
                                deptId: this.deptId,
                                businessType: this.businessType
                            }
                            transferData = this.transferDatas;
                            getTransferUser(transferData).then(res => {
                                if (res.code === '0000') {
                                    console.log(this.usernameCreateBy)
                                    let data = [];
                                    for (let i = 0; i < res.data.length; i++) {
                                        if (res.data[i].username != this.usernameCreateBy) {
                                            data.push(res.data[i]);
                                            console.log(res.data[i], '结果')
                                        }
                                    }

                                    let records = data;
                                    let total = res.data.length;
                                    this.$refs.transferTable.updateTableData(records, total);
                                }
                            });
                        }
                    });
                } else {
                    this.userRealName = transferData.condition.userRealName;
                    this.transferDatas = {
                        roleCode: this.roleCodes,
                        deptId: this.deptId,
                        phone: transferData.condition.phone,
                        username: this.username,
                        userRealName: this.userRealName,
                        businessType: this.businessType
                    };
                    transferData = this.transferDatas;
                    getTransferUser(transferData).then(res => {
                        if (res.code === '0000') {
                            let records = res.data;
                            let total = res.data.length;
                            this.$refs.transferTable.updateTableData(records, total);
                        }
                    })
                }
            },
            //刷新
            refresh() {
                this.queryDate = {};
                this.$refs['contractMngLoanTable'].reloadData();
            },
            //转单
            transfers(row) {
                this.transferModalVisible = true;
                this.applyNo = row.applyNo;
                if (row.loanReporter !=''&&row.loanReporter != undefined) {
                    this.usernameCreateBy = row.loanReporter;
                } else if (row.createBy !=''&&row.createBy != undefined) {
                    this.usernameCreateBy = row.createBy;
                }
                this.businessType = row.businessType;
                this.refreshUserData();
            },
            //保存转单信息,需要转单用户，申请编号
            transferSubmit(row) {
                this.$Modal.confirm({
                    title: '转单',
                    content: '确认是否转单',
                    onOk: () => {
                        let params = {
                            createBy: row.username,
                            applyNo: this.applyNo,
                            phone: row.phone,
                            sellerRealName: row.userRealName,
                            username: row.username,
                            roleCode: this.roleCode,
                            departmentId: row.deptId,
                            businessType: this.businessType,
                        }
                        transferData(params).then(res => {
                            if (res.code === '0000') {
                                this.$Message.success("转单成功")
                                this.transferModalVisible = false;
                                this.refresh();
                                this.initGteUserInfo();
                            }else {
                                this.initGteUserInfo();
                            }
                        }).catch(error=>{
                            this.initGteUserInfo();
                        });
                    }
                })
            },
            //撤回订单
            back(row) {
                this.$Modal.confirm({
                    title: "确认撤回?",
                    onOk: () => {
                        this.queryChange.condition.applyNo = row.applyNo;
                        searchOprInfoForAssertChange(this.queryChange).then(res => {
                            let flag = true;
                            if (res.code === "0000") {
                                if (res.data.length > 0) {
                                    for (let i = 0; i < res.data.length; i++) {
                                        if (res.data[i].applyType === "ASSERTCHANGE" && res.data[i].applyStatus !== "05" && res.data[i].applyStatus !== "06" && res.data[i].applyStatus !== "04") {
                                            this.$Message.error("该订单正在申请资产变更，不可操作！");
                                            flag = false;
                                            return;
                                        }
                                    }
                                    if (flag) {
                                        let param = {
                                            contractNo: row.contractNo,
                                            contractApplyStatus: row.contractApplyStatus,
                                            cancelOrWithdraw: 'back'
                                        }
                                        saveCancel(param).then(res => {
                                            if (res.code === "0000") {
                                                this.$Message.success("通知成功");
                                                this.refresh();
                                            }
                                        });
                                    }
                                } else {
                                    let param = {
                                        contractNo: row.contractNo,
                                        contractApplyStatus: row.contractApplyStatus,
                                        cancelOrWithdraw: 'back'
                                    }
                                    saveCancel(param).then(res => {
                                        if (res.code === "0000") {
                                            this.$Message.success("通知成功");
                                            this.refresh();
                                        }
                                    });
                                }
                            }
                        });
                    }
                });
            },
            //保存撤回信息
            backSubmit() {
                this.$refs.backForm.getForm().validate((valid) => {
                    if (valid) {
                        saveBack(this.$refs.backForm.getFormData()).then(res => {
                            if (res.code === "0000") {
                                this.$Message.success("撤回成功");
                                this.refresh();
                                this.backModalVisible = false;
                            }
                        }).catch(() => {
                            this.refresh();
                        });
                    }
                });
            },

            //弹出转单
            transfer(row) {
                this.transferModalVisible = true;
            },
            //转单请求提交
            submit() {

            },
            //取消订单
            cancelOrder(row) {
                this.$Modal.confirm({
                    title: "确认取消?",
                    onOk: () => {
                        this.queryChange.condition.applyNo = row.applyNo;
                        console.log("row:" + row);
                        console.log('点击了确认取消' + row.applyNo);
                        this.queryChange.condition.applyNo = row.applyNo;
                        this.contractNoAndApplyStatus.contractApplyStatus = row.contractApplyStatus;
                        this.contractNoAndApplyStatus.contractNo = row.contractNo;
                        this.cancelVisible = true;
                        this.$refs.cancelOrder.getLoanCannelInfo(row.contractNo);
                    }
                });
            },

            cancelOrderCancel() {
                this.cancelVisible = false;
                this.refresh();
            },

            /**
             * 转单返回时调用用户角色查询 add by gjh
             */
            onTurnBack(){
                this.transferModalVisible=false;
                this.initGteUserInfo();
            },

            /**
             * 进件/放款提报单员置空
             */
            changeLoanReport(v){
                let params = {
                    applyNo:v.applyNo,
                    flag:"loanApply"
                }
                changeLoanReport(params).then(res=>{
                    // eslint-disable-next-line no-empty
                    if(res.code==='0000'){
                    }
                })
            },
            repeal(v){
                this.repayPlansQueryContractNo = v.contractNo;
                this.repayPlansVisible = true;
            },
            onlineMortgage(v){
                this.onlineMortgageParam = v;

            },
        }
    }
</script>
<style lang="less" scoped>
    @import "../../assets/css/applyList.less";
</style>
<style>
    .ios-warning{
        color: red;
    }
</style>
