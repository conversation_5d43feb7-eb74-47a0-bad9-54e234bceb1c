const config = {
    getProjectName: () => {
        return _AFS_PROJECT_CONFIG.name;
    },
    getProjectPath: () => {
        return _AFS_PROJECT_CONFIG.path;
    },
    getTitle:()=>{
        return _AFS_PROJECT_CONFIG.title
    },
    getHomeName:()=>{
        return _AFS_PROJECT_CONFIG.homeName
    },
    getProjectInfo:()=>{
        return _AFS_PROJECT_CONFIG;
    },
    getBaseUrl: () => {
        return _AFS_PROJECT_CONFIG.apiUri;
    },
    getDynamicScope: () => {
        return _AFS_PROJECT_CONFIG.dynamicServiceScope;
    },
    getClientInfo: () => {
        return _AFS_PROJECT_CONFIG.clientInfo;
    },
    getGrayConfig: () =>{
        return {
            zone:'yjp',
            version:'yjp'
        }
    }
}
export default config
