//当前开发工程（node代理请求后台地址）系统url(开发必配置)

const proxyUrl = {
     // afsApply: 'http://127.0.0.1:9999',
    // afsApply: 'http://39.98.90.101:7777',
   afsApply: 'https://leasingtest.byd.com:9800/leasing/apply_uat/api',
}

let projectInfo = { name: process.argv[4] }

//当前开发工程，后台代理地址(开发需访问后台必配置)
let projectsProxies = {
    afsApply: {
        proxyInfo: { //代理信息配置
            '/apply': {
                // target: proxyUrl[projectInfo.name],
                target: proxyUrl.afsApply,
                ws: true,
                pathRewrite: {
                    '^/apply': '/apply'
                },
            },
            '/image': {
                target: proxyUrl[projectInfo.name],
                ws: true,
                pathRewrite: {
                    '^/image': '/image'
                }
            },
            '/config': {
                target: proxyUrl[projectInfo.name],
                ws: true,
                pathRewrite: {
                    '^/config': '/config'
                }
            }
            // ,
        }
    },
}

const COMMON_PROXY = {
    '/auth': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/auth': '/auth'
        }
    },
    '/userlogin': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/userlogin': '/userlogin'
        }
    },
    '/pubkey': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/pubkey': '/pubkey'
        }
    },
    '/refreshtoken': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/refreshtoken': '/refreshtoken'
        }
    },
    '/admin': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/admin': '/admin'
        }
    },
    '/filecenter': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/filecenter': '/filecenter'
        }
    },
    '/captcha': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/captcha': '/captcha'
        }
    },
    '/workflow': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/workflow': '/workflow'
        }
    },
    '/afsworkflow': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/afsworkflow': '/afsworkflow'
        }
    },
    '/demo': {
        target: proxyUrl[projectInfo.name],
        ws: true,
        pathRewrite: {
            '^/demo': '/demo'
        }
    },
    '/foo': {
        target: '<other_url>'
    }
}

const afs_proxy_config = {
    getProjectInfo: () => {
        return projectInfo;
    },
    getProxyInfo: () => {
        return {
            ...COMMON_PROXY,
            ...projectsProxies[projectInfo.name].proxyInfo
        }
    }
}
module.exports = afs_proxy_config;
