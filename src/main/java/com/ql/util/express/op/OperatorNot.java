package com.ql.util.express.op;

import com.ql.util.express.Operator;
import com.ql.util.express.OperatorOfNumber;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/20 17:25
 */
public class OperatorNot extends Operator {
    public OperatorNot(String name) {
        this.name = name;
    }
    public OperatorNot(String aAliasName, String aName, String aErrorInfo) {
        this.name = aName;
        this.aliasName = aAliasName;
        this.errorInfo = aErrorInfo;
    }
    public Object executeInner(Object[] list) throws Exception {
        return executeInner(list[0]);
    }

    public Object executeInner(Object op)
            throws Exception {
        Object result = null;
        if (op == null) {
            throw new Exception("null 不能执行操作：" + this.getAliasName());
        }
        if ("!".equals(this.getName())) {
            result = OperatorOfNumber.multiply(op, new BigDecimal("-1"),this.isPrecise);
        } else {
            //
            String msg = "没有定义类型" + op.getClass().getName() + " 的 " + this.name
                    + "操作";
            throw new Exception(msg);
        }
        return result;
    }
}
