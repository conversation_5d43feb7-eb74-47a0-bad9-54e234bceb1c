package com.ruicar.afs.cloud.afscase.infomanagement.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;
import lombok.Data;

/**
 * 功能说明:驾驶证识别结果汇总表
 * <AUTHOR>
 */
@Data
@TableName("case_driver_license_summary")
public class CaseDriverLicenseSummary extends BaseEntity<CaseDriverLicenseSummary> {
    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 档案编号（纸质副页专属）
     */
    private String fileNo;
    /**
     * 发证机关（纸质主页专属）
     */
    private String issuingAuthority;
    /**
     * 档案编号识别结果【0不符合，1符合】
     */
    private String fileNoCompare;
    /**
     * 初次领证时间
     */
    private String dateOfFirstIssue;
    /**
     * 有效期开始
     */
    private String startDate;
    /**
     * 欺诈标识【1未见异常，2疑似欺诈】
     */
    private String fraudIdentification;

}
