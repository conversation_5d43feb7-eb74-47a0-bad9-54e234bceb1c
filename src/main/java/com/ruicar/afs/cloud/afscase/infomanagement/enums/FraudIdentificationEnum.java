package com.ruicar.afs.cloud.afscase.infomanagement.enums;

import lombok.Getter;

/**
 * 功能说明:是否欺诈
 * <AUTHOR>
 */
@Getter
public enum FraudIdentificationEnum {
    /**
     * 未见异常
     */
    NO_ABNORMAL("1","未见异常"),
    /**
     * 疑似欺诈
     */
    SUSPECTED_FRAUD("2","疑似欺诈");

    private String code;
    private String desc;

    FraudIdentificationEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
