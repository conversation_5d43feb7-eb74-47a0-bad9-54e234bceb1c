package com.ruicar.afs.cloud.afscase.onlinemortgage.controller;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruicar.afs.cloud.afscase.channel.entity.ChannelBaseInfo;
import com.ruicar.afs.cloud.afscase.channel.service.ChannelBaseInfoService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustAddress;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseCustInfo;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustAddressService;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseCustInfoService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.MortgageService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.condition.VehicleMortgageRegistrationCondition;
import com.ruicar.afs.cloud.afscase.onlinemortgage.config.SocialMortgageConfig;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.CaseMortgageProxyInfo;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.CaseMortgageRecord;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.MortgagePlaceConfig;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.VehicleMortgageRegistration;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.BlqdEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.BusinessStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.ImpawnZllxEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.YwlxEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.CaseMortgageProxyInfoService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.CaseMortgageRecordService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.MortgagePlaceConfigService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.VehicleMortgageRegistrationService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnFlowRequest;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnPhotoRequest;
import com.ruicar.afs.cloud.afscase.onlinemortgage.vo.request.ImpawnRequest;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.CustRoleEnum;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;
import com.ruicar.afs.cloud.image.service.ComAttachmentFileService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.MessageFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;
import javax.imageio.plugins.jpeg.JPEGImageWriteParam;
import javax.imageio.stream.ImageOutputStream;

/**
 * 车辆抵押登记信息表控制层
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/mortgage")
@Api("车辆抵押登记管理")
public class VehicleMortgageRegistrationController {

    private static final String ATTACHMENT_AGENT_PHOTO_ON_SITE = "代理人现场照";
    private static final String ATTACHMENT_AGENT_POA = "代理人委托书";
    private static final String ATTACHMENT_OWNER_PHOTO_ON_SITE = "所有人现场照";
    private static final String ATTACHMENT_MORTGAGEE_POA = "抵押权人委托书";
    private static final String ATTACHMENT_APPLICATION_FORM = "机动车抵押登记质押备案申请表";
    private static final String ATTACHMENT_VEHICLE_MORTGAGE_CONTRACT = "车辆抵押合同";
    private static final String OWNER_ID_CARD_FRONT_SUBSTRING = "承租人身份证正面";
    private static final String OWNER_ID_CARD_BACK_SUBSTRING = "承租人身份证反面";
    private static final String AGENT_ID_CARD_FRONT_SUBSTRING = "代理人身份证正面";
    private static final String AGENT_ID_CARD_BACK_SUBSTRING = "代理人身份证反面";

    private final VehicleMortgageRegistrationService vehicleMortgageRegistrationService;

    private final MortgageService mortgageService;

    private final ChannelBaseInfoService channelBaseInfoService;

    private final MortgagePlaceConfigService mortgageConfigService;

    private final CaseMortgageProxyInfoService caseMortgageProxyInfoService;

    private final StringRedisTemplate stringRedisTemplate;

    private final SocialMortgageConfig socialMortgageConfig;

    private final CaseMortgageRecordService mortgageRecordService;

    private final CaseCustInfoService caseCustInfoService;

    private final CaseCustAddressService caseCustAddressService;

    private final ComAttachmentFileService comAttachmentFileService;

    private final AddressService addressService;

    /**
     * 分页查询车辆抵押登记信息
     *
     * @param condition 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页查询车辆抵押登记信息")
    public IResponse<?> pageWithCondition(@RequestBody VehicleMortgageRegistrationCondition condition) {
        IPage<VehicleMortgageRegistration> page = vehicleMortgageRegistrationService.pageWithCondition(condition);
        return IResponse.success(page);
    }

    /**
     * 新增或更新车辆抵押登记信息
     *
     * @param request 车辆抵押登记信息
     * @return 操作结果
     */
    @PostMapping("saveOrUpdate")
    @ApiOperation(value = "新增或更新车辆抵押登记信息")
    public IResponse<Boolean> saveOrUpdate(@RequestBody VehicleMortgageRegistration request) {
        List<VehicleMortgageRegistration> existList = vehicleMortgageRegistrationService.lambdaQuery()
            .eq(VehicleMortgageRegistration::getApplyNo, request.getApplyNo())
            .eq(VehicleMortgageRegistration::getMortgageType, request.getMortgageType())
            .list();
        String desc = MortgageTypeEnum.getDesc(request.getMortgageType());
        // 有代理人做抵押权人，无代理人客户做抵押权人
        if (StrUtil.isNotBlank(request.getAgentNo())) {
            request.setMortgageeAgentIdType(request.getAgentIdType());
            request.setMortgageeAgentIdNo(request.getAgentIdNo());
            request.setMortgageeAgentName(request.getAgentName());
            request.setMortgageeAgentPhone(request.getAgentPhone());
        } else {
            request.setMortgageeAgentIdType(request.getOwnerIdType());
            request.setMortgageeAgentIdNo(request.getOwnerIdNo());
            request.setMortgageeAgentName(request.getOwnerName());
            request.setMortgageeAgentPhone(request.getOwnerPhone());
            // 客户邮寄信息
            CaseCustInfo caseCustInfo = caseCustInfoService.getOne(Wrappers.<CaseCustInfo>query().lambda()
                .eq(CaseCustInfo::getApplyNo, request.getApplyNo())
                .eq(CaseCustInfo::getCustRole, CustRoleEnum.MIANCUST.getCode()));
            CaseCustAddress caseCustAddress = caseCustAddressService.list(Wrappers.<CaseCustAddress>query()
                .lambda()
                .eq(CaseCustAddress::getCustId, caseCustInfo.getId())
                .eq(CaseCustAddress::getAddressType, CaseConstants.RESIDENTIAL_ADDRESS)).get(0);
            String[] addressData = {
                caseCustAddress.getProvince(), caseCustAddress.getCity(), caseCustAddress.getCounty(),
                caseCustAddress.getTown(), caseCustAddress.getStreet(), caseCustAddress.getDetailAddress()
            };
            request.setOwnerAddress(this.getAddress(addressData));
            // 邮编没有来源待定
            request.setOwnerAddress("");
        }

        if (request.getId() == null) {
            // 新增场景
            if (CollUtil.isNotEmpty(existList)) {
                throw new AfsBaseException("已存在合同号为" + request.getApplyNo() + "的" + desc + "登记信息，请勿重复");
            }
            String log = String.format("金融机构[%s]创建订单，[%s]提交订单", request.getChannelName(),
                request.getSponsor());
            mortgageRecordService.logging(log,request.getApplyNo(),request.getMortgageType());
        } else {
            if (CollUtil.isNotEmpty(existList)
                && existList.get(0).getMortgageStatus() != MortgageStatusEnum.MORTGAGE_DRAFT) {
                throw new AfsBaseException("非草稿状态不可编辑");
            }
            // 修改场景
            if (CollUtil.isNotEmpty(existList) && !existList.get(0).getId().equals(request.getId())) {
                throw new AfsBaseException("已存在合同号为" + request.getApplyNo() + "的" + desc + "登记信息，请勿重复");
            }
        }
        if (request.getId() != null && request.getApprove() != null) {
            request.setMortgageStatus(MortgageStatusEnum.TO_BE_SIGNED);
            // 校验上牌地
            if(StrUtil.isBlank(request.getIssuingAuthority())){
                throw new AfsBaseException("上牌地信息必选");
            }
        }
        request.setSignApplyNo(request.getApplyNo()+AfsEnumUtil.key(request.getMortgageType()));
        return IResponse.success(vehicleMortgageRegistrationService.saveOrUpdateVehicleMortgageRegistration(request));
    }

    private  String getAddress(String[] detailAddressTemp){
        StringBuilder detail= new StringBuilder();
        if(detailAddressTemp==null||detailAddressTemp.length==0){
            return detail.toString() ;
        }

        for(int i=0; i<detailAddressTemp.length;i++ ){
            if(StringUtil.isBlank ( detailAddressTemp[i] )){
                continue;
            }
            String label = "";
            if(i+1 == detailAddressTemp.length){
                label = detailAddressTemp[i];
            }else{
                label = addressService.getLabelByCode(detailAddressTemp[i]);
            }
            if(StringUtil.isBlank (detail.toString())){
                detail.append(label);
            } else if ("99999999".equals(label) || "99999999".equals(detailAddressTemp[i])) {

            } else {
                detail.append("/"+label);
            }
        }
        return detail.toString();
    }

    /**
     * 发起抵押/解抵押申请
     *
     * @param request 发起抵押/解抵押申请
     * @return 操作结果
     */
    @PostMapping("initiateApplication")
    @ApiOperation(value = "发起抵押/解抵押申请")
    public IResponse<?> initiateApplication(@RequestBody VehicleMortgageRegistrationCondition request) {
        log.info("接收到申请参数{}", JSONObject.toJSONString(request));

        // 1. 获取并校验核心业务数据
        VehicleMortgageRegistration registration = vehicleMortgageRegistrationService.lambdaQuery()
            .eq(VehicleMortgageRegistration::getApplyNo, request.getApplyNo())
            .eq(VehicleMortgageRegistration::getMortgageType, request.getMortgageType())
            .oneOpt()
            .orElseThrow(() -> new AfsBaseException("抵押的数据不存在"));

        if (registration.getMortgageStatus() != MortgageStatusEnum.SIGNING_COMPLETED) {
            throw new AfsBaseException("非签约完成状态，不可发起");
        }

        // 2. 准备并校验附件
        boolean isAgent = StrUtil.isNotBlank(registration.getAgentNo());
        request.setAgentNo(registration.getAgentNo());
        Map<String, ComAttachmentFile> attachmentMap = buildAttachmentMap(request);
        validateRequiredAttachments(attachmentMap, isAgent, request);

        // 3. 准备调用外部接口的数据
        ImpawnRequest impawnRequest = prepareImpawnRequest(registration, isAgent);

        // 4. 处理并准备图片附件
        Map<String, byte[]> mergedImageMap = processIdCardImages(request, isAgent);
        List<ImpawnPhotoRequest> imageList = prepareImpawnPhotoRequests(attachmentMap, mergedImageMap, isAgent);
        impawnRequest.setImageList(imageList);

        log.info("预申请参数{}", JSONObject.toJSONString(impawnRequest));

        // 5. 分布式锁防止重复提交
        String lockValue = registration.getSignApplyNo();
        String lockKey = MessageFormat.format("{0}{1}", "lock-mortgage-submit:", lockValue);
        Boolean locked = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, 2, TimeUnit.MINUTES);
        Assert.isTrue(locked, "不可重复提交！");

        try {
            // 6. 执行核心业务并更新状态
            executeMortgageAndHandleResult(impawnRequest, registration);
            return IResponse.success("发起成功");
        } catch (Exception e) {
            log.error("{}无锡所抵押申请发起异常", request.getApplyNo(), e);
            updateRegistrationOnFailure(registration.getId(), "发起异常:" + e.getMessage());
            return IResponse.fail("签约发起异常: " + e.getMessage());
        } finally {
            // 7. 释放锁
            String value = stringRedisTemplate.opsForValue().get(lockKey);
            if (lockValue.equals(value)) {
                stringRedisTemplate.delete(lockKey);
            }
        }
    }

    /**
     * 构建附件名称到附件对象的映射，便于快速查找
     */
    private Map<String, ComAttachmentFile> buildAttachmentMap(VehicleMortgageRegistrationCondition request) {
        String conNo = request.getApplyNo().replace("AP", "CT");
        List<ComAttachmentFile> vehicleMortgageContract = comAttachmentFileService.lambdaQuery()
            .eq(ComAttachmentFile::getBusiNo, conNo)
            .eq(ComAttachmentFile::getBelongNo, conNo)
            .eq(ComAttachmentFile::getAttachmentName, ATTACHMENT_VEHICLE_MORTGAGE_CONTRACT)
            .list();
        if (CollUtil.isEmpty(vehicleMortgageContract)) {
            throw new AfsBaseException("抵押合同附件未找到");
        }

        List<ComAttachmentFile> allFiles = new ArrayList<>(request.getFileList());
        allFiles.addAll(vehicleMortgageContract);

        return allFiles.stream()
            .collect(Collectors.toMap(ComAttachmentFile::getAttachmentName, f -> f, (first, second) -> first));
    }

    /**
     * 校验必要附件是否都已上传
     */
    private void validateRequiredAttachments(Map<String, ComAttachmentFile> attachmentMap, boolean isAgent,
        VehicleMortgageRegistrationCondition request) {
        checkAttachmentExists(attachmentMap, ATTACHMENT_OWNER_PHOTO_ON_SITE, "所有人现场照必需上传");
        checkAttachmentExists(attachmentMap, ATTACHMENT_MORTGAGEE_POA, "抵押权人委托书未签署完成，请稍后再试");
        checkAttachmentExists(attachmentMap, ATTACHMENT_APPLICATION_FORM, "机动车抵押登记质押备案申请表未签署完成，请稍后再试");

        if (CollUtil.isEmpty(request.getOwnerFileList())) {
            throw new AfsBaseException("所有人身份证附件不能为空");
        }

        if (isAgent) {
            checkAttachmentExists(attachmentMap, ATTACHMENT_AGENT_PHOTO_ON_SITE, "代理人现场照必需上传");
            checkAttachmentExists(attachmentMap, ATTACHMENT_AGENT_POA, "代理人委托书未签署完成，请稍后再试");
            if (CollUtil.isEmpty(request.getAgentFileList())) {
                throw new AfsBaseException("代理人身份证附件不能为空");
            }
        }
    }

    /**
     * 辅助方法：检查附件是否存在
     */
    private void checkAttachmentExists(Map<String, ComAttachmentFile> map, String name, String errorMessage) {
        if (!map.containsKey(name)) {
            throw new AfsBaseException(errorMessage);
        }
    }

    /**
     * 准备调用无锡所接口的请求体
     */
    private ImpawnRequest prepareImpawnRequest(VehicleMortgageRegistration registration, boolean isAgent) {
        ImpawnRequest impawnRequest = new ImpawnRequest();
        // 基础信息设置
        impawnRequest.setJkxlh(socialMortgageConfig.getClientId());
        impawnRequest.setYwlx(YwlxEnum.IMPAWN.getCode());
        impawnRequest.setBlqd(BlqdEnum.PRIVATE.getCode());
        boolean isBlank = StrUtil.isBlank(registration.getMortContractNo());
        impawnRequest.setXh(isBlank ? null : registration.getMortContractNo());
        impawnRequest.setCzlx(isBlank ? "1" : "2");
        // 抵押权人信息
        impawnRequest.setZzjgdm(socialMortgageConfig.getUnifiedSocialCreditCode());
        impawnRequest.setDyhth(registration.getApplyNo());
        // 机动车所有人信息
        impawnRequest.setSyrsfzmmc(registration.getOwnerIdType().key());
        impawnRequest.setSyrsfzmhm(registration.getOwnerIdNo());
        impawnRequest.setSyr(registration.getOwnerName());
        impawnRequest.setSyrsjhm(registration.getOwnerPhone());
        // 车辆信息
        impawnRequest.setClsbdh(registration.getVinNo());
        impawnRequest.setHpzl(registration.getPlateType().key());
        impawnRequest.setHphm(registration.getIssuingAuthority()); // 号牌头或完整号牌
        // 所有人代理人信息(可选)
        if (isAgent) {
            impawnRequest.setSyrdlrsfzmmc(registration.getAgentIdType().key());
            impawnRequest.setSyrdlrsfzmhm(registration.getAgentIdNo());
            impawnRequest.setSyrdlrlxdh(registration.getAgentPhone());
            impawnRequest.setSyrdlrxm(registration.getAgentName());
        }
        // 抵押权人代理人信息
        impawnRequest.setDyqrdlrsfzmmc(registration.getMortgageeAgentIdType().key());
        impawnRequest.setDyqrdlrsfzmhm(registration.getMortgageeAgentIdNo());
        impawnRequest.setDyqrdlrxm(registration.getMortgageeAgentName());
        impawnRequest.setDyqrdlrlxdh(registration.getMortgageeAgentPhone());
        // 业务办理信息
        impawnRequest.setYwblsj(DateUtil.format(registration.getSignTime(),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        impawnRequest.setBz(registration.getRemark());
        // 邮寄信息
        impawnRequest.setYjdz(socialMortgageConfig.getMailAddress());
        impawnRequest.setYjlxdh(socialMortgageConfig.getContactPhone());
        impawnRequest.setYjlxr(socialMortgageConfig.getFinancialInstitution());
        impawnRequest.setYjyzbm(socialMortgageConfig.getZipCode());
        return impawnRequest;
    }

    /**
     * 处理身份证图片：合并、压缩
     */
    private Map<String, byte[]> processIdCardImages(VehicleMortgageRegistrationCondition request, boolean isAgent) {
        Map<String, byte[]> imageMap = new HashMap<>();

        ComAttachmentFile ownerFront = findFileInList(request.getOwnerFileList(), OWNER_ID_CARD_FRONT_SUBSTRING);
        ComAttachmentFile ownerBack = findFileInList(request.getOwnerFileList(), OWNER_ID_CARD_BACK_SUBSTRING);
        imageMap.put("owner", mergeAndCompressIdCardImages(ownerFront, ownerBack));

        if (isAgent) {
            ComAttachmentFile agentFront = findFileInList(request.getAgentFileList(), AGENT_ID_CARD_FRONT_SUBSTRING);
            ComAttachmentFile agentBack = findFileInList(request.getAgentFileList(), AGENT_ID_CARD_BACK_SUBSTRING);
            imageMap.put("agent", mergeAndCompressIdCardImages(agentFront, agentBack));
        }
        return imageMap;
    }

    /**
     * 辅助方法：从列表中查找包含特定名称的文件
     */
    private ComAttachmentFile findFileInList(List<ComAttachmentFile> files, String nameSubstring) {
        return files.stream()
            .filter(f -> f.getAttachmentName().contains(nameSubstring))
            .findFirst()
            .orElseThrow(() -> new AfsBaseException("身份证文件缺失: " + nameSubstring));
    }

    /**
     * 准备接口需要的图片附件列表
     */
    private List<ImpawnPhotoRequest> prepareImpawnPhotoRequests(Map<String, ComAttachmentFile> attachmentMap,
        Map<String, byte[]> mergedImageMap, boolean isAgent) {
        List<ImpawnPhotoRequest> imageList = new ArrayList<>();

        // 必传类型
        List.of(ImpawnZllxEnum.SQB, ImpawnZllxEnum.DYHT, ImpawnZllxEnum.DYQR_WTS, ImpawnZllxEnum.DYQR_DLR_SFZ,
                ImpawnZllxEnum.DYQR_DLS_XCZ)
            .forEach(type -> imageList.add(createPhotoRequest(type, attachmentMap, mergedImageMap, isAgent)));

        // 根据是否有代理人，添加可选附件
        if (isAgent) {
            List.of(ImpawnZllxEnum.SYR_DLR_SFZ, ImpawnZllxEnum.SYR_DLR_XCZ, ImpawnZllxEnum.SYR_WTS)
                .forEach(type -> imageList.add(createPhotoRequest(type, attachmentMap, mergedImageMap, isAgent)));
        } else {
            List.of(ImpawnZllxEnum.SYR_SFZ, ImpawnZllxEnum.SYR_XCZ)
                .forEach(type -> imageList.add(createPhotoRequest(type, attachmentMap, mergedImageMap, isAgent)));
        }
        return imageList;
    }

    /**
     * 创建单个图片附件请求
     */
    private ImpawnPhotoRequest createPhotoRequest(ImpawnZllxEnum type, Map<String, ComAttachmentFile> attachmentMap,
        Map<String, byte[]> mergedImageMap, boolean isAgent) {
        ImpawnPhotoRequest photoRequest = new ImpawnPhotoRequest();
        photoRequest.setZllx(type.getCode());

        byte[] content = switch (type) {
            // =============必传======
            case SQB -> downloadAttachment(attachmentMap.get(ATTACHMENT_APPLICATION_FORM));
            case DYHT -> downloadAttachment(attachmentMap.get(ATTACHMENT_VEHICLE_MORTGAGE_CONTRACT));
            case DYQR_WTS -> downloadAttachment(attachmentMap.get(ATTACHMENT_MORTGAGEE_POA));
            case DYQR_DLR_SFZ -> isAgent ? mergedImageMap.get("agent") : mergedImageMap.get("owner");
            case DYQR_DLS_XCZ -> isAgent
                ? downloadAttachment(attachmentMap.get(ATTACHMENT_AGENT_PHOTO_ON_SITE))
                : downloadAttachment(attachmentMap.get(ATTACHMENT_OWNER_PHOTO_ON_SITE));
            // ==========代理人=====
            case SYR_DLR_SFZ -> mergedImageMap.get("agent");
            case SYR_DLR_XCZ -> downloadAttachment(attachmentMap.get(ATTACHMENT_AGENT_PHOTO_ON_SITE));
            case SYR_WTS -> downloadAttachment(attachmentMap.get(ATTACHMENT_AGENT_POA));
            // ==========所有人=====
            case SYR_SFZ -> mergedImageMap.get("owner");
            case SYR_XCZ -> downloadAttachment(attachmentMap.get(ATTACHMENT_OWNER_PHOTO_ON_SITE));
        };
        photoRequest.setZpnr(content);
        return photoRequest;
    }

    /**
     * 执行抵押/解抵押操作并处理结果
     */
    private void executeMortgageAndHandleResult(ImpawnRequest impawnRequest, VehicleMortgageRegistration registration)
        throws Exception {
        boolean isMortgage = registration.getMortgageType() == MortgageTypeEnum.MORTGAGE;
        JSONObject result = isMortgage
            ? mortgageService.mortgage(impawnRequest)
            : mortgageService.releaseMortgage(impawnRequest);

        boolean isSuccess = result.getString("code") != null && "200".equals(result.getString("code"));

        if (isSuccess) {
            String xh = "1".equals(impawnRequest.getCzlx()) ? result.getJSONObject("data").getString("xh") : null;
            vehicleMortgageRegistrationService.lambdaUpdate()
                .eq(VehicleMortgageRegistration::getId, registration.getId())
                .set(VehicleMortgageRegistration::getMortgageStatus,
                    isMortgage ? MortgageStatusEnum.MORTGAGING : MortgageStatusEnum.MORTGAGE_RELEASING)
                .set(VehicleMortgageRegistration::getDealStatus, BusinessStatusEnum.PRE_ACCEPTED)
                .set(StrUtil.isNotBlank(xh), VehicleMortgageRegistration::getMortContractNo, xh)
                .update();
        } else {
            String failureReason = "发起失败:" + result.getString("message");
            updateRegistrationOnFailure(registration.getId(), failureReason);
            throw new AfsBaseException(failureReason);
        }
    }

    /**
     * 当操作失败时，更新数据库记录失败原因
     */
    private void updateRegistrationOnFailure(Long registrationId, String reason) {
        vehicleMortgageRegistrationService.lambdaUpdate()
            .eq(VehicleMortgageRegistration::getId, registrationId)
            .set(VehicleMortgageRegistration::getFailureReason, reason)
            .update();
    }

    /**
     * 辅助方法：下载附件
     */
    private byte[] downloadAttachment(ComAttachmentFile file) {
        if (file == null) {
            throw new AfsBaseException("附件不存在，无法下载。");
        }
        return comAttachmentFileService.downloadAttachmentFile(file);
    }

    /**
     * 合并身份证正反面图片并压缩
     * @param frontFile 正面图片文件
     * @param backFile 反面图片文件
     * @return 合并并压缩后的图片字节数组
     */
    private byte[] mergeAndCompressIdCardImages(ComAttachmentFile frontFile, ComAttachmentFile backFile) {
        try {
            // 下载正面和反面图片
            byte[] frontImageBytes = comAttachmentFileService.downloadAttachmentFile(frontFile);
            byte[] backImageBytes = comAttachmentFileService.downloadAttachmentFile(backFile);

            // 将字节数组转换为BufferedImage
            BufferedImage frontImage = ImageIO.read(new ByteArrayInputStream(frontImageBytes));
            BufferedImage backImage = ImageIO.read(new ByteArrayInputStream(backImageBytes));

            // 创建新的合并图片，高度为两张图片高度之和，宽度为两张图片中较宽的
            int width = Math.max(frontImage.getWidth(), backImage.getWidth());
            int height = frontImage.getHeight() + backImage.getHeight();
            BufferedImage mergedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

            // 绘制合并图片
            Graphics2D g = mergedImage.createGraphics();
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, width, height);
            g.drawImage(frontImage, 0, 0, null);
            g.drawImage(backImage, 0, frontImage.getHeight(), null);
            g.dispose();

            // 压缩图片到500KB以下
            return compressImageToSize(mergedImage, 500 * 1024); // 500KB
        } catch (IOException e) {
            throw new RuntimeException("身份证图片处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 压缩图片到指定大小
     *
     * @param image 图片
     * @param maxSize 最大大小（字节）
     * @return 压缩后的图片字节数组
     */
    private static byte[] compressImageToSize(BufferedImage image, int maxSize) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        // 初始质量
        float quality = 0.9f;

        // 压缩图片
        ImageIO.write(image, "jpg", baos);
        byte[] imageData = baos.toByteArray();

        // 如果图片大于指定大小，则进行压缩
        while (imageData.length > maxSize && quality > 0.1) {
            baos.reset();
            compressImage(image, baos, quality);
            imageData = baos.toByteArray();
            quality -= 0.1f;
        }

        if (imageData.length > maxSize) {
            double scale = Math.sqrt((double) maxSize / imageData.length);
            int newWidth = (int) (image.getWidth() * scale);
            int newHeight = (int) (image.getHeight() * scale);
            Image scaledImage = ImgUtil.scale(image, newWidth, newHeight);
            BufferedImage scaledBufferedImage = toBufferedImage(scaledImage);

            // 递归调用压缩方法
            return compressImageToSize(scaledBufferedImage, maxSize);
        }

        return imageData;
    }

    /**
     * 使用指定质量压缩图片
     *
     * @param image 图片
     * @param baos 输出流
     * @param quality 质量（0.1-1.0）
     * @throws IOException IO异常
     */
    private static void compressImage(BufferedImage image, ByteArrayOutputStream baos, float quality)
        throws IOException {
        JPEGImageWriteParam jpegParams = new JPEGImageWriteParam(null);
        jpegParams.setCompressionMode(JPEGImageWriteParam.MODE_EXPLICIT);
        jpegParams.setCompressionQuality(quality);

        try (ImageOutputStream ios = ImageIO.createImageOutputStream(baos)) {
            javax.imageio.ImageWriter writer = ImageIO.getImageWritersByFormatName("jpg").next();
            writer.setOutput(ios);
            writer.write(null, new javax.imageio.IIOImage(image, null, null), jpegParams);
            writer.dispose();
        }
    }

    /**
     * 将Image转换为BufferedImage
     *
     * @param img Image对象
     * @return BufferedImage对象
     */
    private static BufferedImage toBufferedImage(Image img) {
        if (img instanceof BufferedImage) {
            return (BufferedImage) img;
        }
        BufferedImage bufferedImage = new BufferedImage(img.getWidth(null), img.getHeight(null),
            BufferedImage.TYPE_INT_RGB);
        Graphics2D g = bufferedImage.createGraphics();
        g.drawImage(img, 0, 0, null);
        g.dispose();
        return bufferedImage;
    }

    /**
     * 查询操作日志
     *
     * @param request 查询操作日志
     * @return 操作结果
     */
    @PostMapping("listLog")
    @ApiOperation(value = "查询操作日志")
    public IResponse<?> listLog(@RequestBody VehicleMortgageRegistrationCondition request) {
        return IResponse.success(mortgageRecordService.lambdaQuery()
            .eq(CaseMortgageRecord::getApplyNo, request.getApplyNo())
            .eq(CaseMortgageRecord::getMortgageType, request.getMortgageType())
            .orderByDesc(CaseMortgageRecord::getCreateTime)
            .list());
    }

    @PostMapping("/getInfoBySignApplyNo")
    @ApiOperation(value = "通过申请编号查询对应的信息")
    public IResponse<VehicleMortgageRegistration> getInfoBySignApplyNo(
        @RequestBody VehicleMortgageRegistrationCondition condition) {
        Assert.isTrue(StringUtil.isNotEmpty(condition.getSignApplyNo()), "关联申请编号不能为空！");
        return IResponse.success(vehicleMortgageRegistrationService.getInfoBySignApplyNo(condition));
    }

    /**
     * 抵押状态更新
     *
     * @param condition
     * @return
     */
    @PostMapping("/testStatusUpdate")
    @ApiOperation(value = "通过申请编号查询对应的信息")
    public IResponse<?> testStatusUpdate(@RequestBody VehicleMortgageRegistration condition) throws Exception {
        ImpawnFlowRequest impawnFlowRequest = new ImpawnFlowRequest();
        impawnFlowRequest.setJkxlh(socialMortgageConfig.getClientId());
        impawnFlowRequest.setXh(condition.getMortContractNo());
        impawnFlowRequest.setYwzt(AfsEnumUtil.key(condition.getDealStatus()));
        impawnFlowRequest.setTbyyms(condition.getRemark());
        JSONObject data = mortgageService.testStatusUpdate(impawnFlowRequest);
        return IResponse.success(data);
    }

    /**
     * 代理人过期删除
     *
     * @param request 代理人过期删除
     * @return 操作结果
     */
    @PostMapping("/deletedAgentExpired")
    @ApiOperation(value = "代理人过期删除")
    public IResponse<?> deletedAgentExpired(@RequestBody VehicleMortgageRegistrationCondition request) {
        LambdaQueryWrapper<CaseMortgageProxyInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CaseMortgageProxyInfo::getApplyNo, request.getApplyNo());
        return IResponse.success(caseMortgageProxyInfoService.remove(wrapper));
    }

    /**
     * 更新签约状态
     *
     * @param request 代理人过期删除
     * @return 操作结果
     */
    @PostMapping("/updateSignStatus")
    @ApiOperation(value = "更新签约状态")
    public IResponse<?> updateSignStatus(@RequestBody VehicleMortgageRegistrationCondition request) {

        boolean update = vehicleMortgageRegistrationService.update(Wrappers.<VehicleMortgageRegistration>lambdaUpdate()
                .set(VehicleMortgageRegistration::getMortgageStatus, request.getMortgageStatus())
                .eq(VehicleMortgageRegistration::getSignApplyNo, request.getSignApplyNo())
                .eq(VehicleMortgageRegistration::getMortgageStatus, request.getOldMortgageStatus())
        );

        if(update){
            return IResponse.success("");
        }

        return IResponse.fail("");
    }

    @ApiOperation(value = "经销商权限校验")
    @PostMapping("/checkMortgagePermission")
    public IResponse<List<MortgagePlaceConfig>> checkMortgagePermission(@RequestBody String channelId) {
        List<ChannelBaseInfo> channelList = channelBaseInfoService.lambdaQuery()
            .eq(ChannelBaseInfo::getChannelId, Long.valueOf(channelId))
            .list();
        if(CollUtil.isEmpty(channelList)){
            throw new AfsBaseException("经销商不存在");
        }
        String channelCity = channelList.get(0).getChannelCity();
        // 操作的权限，地区的权限
        List<MortgagePlaceConfig> list = mortgageConfigService.lambdaQuery()
            .eq(MortgagePlaceConfig::getCityNo, channelCity)
            .list();
        return IResponse.success(list);
    }
}
