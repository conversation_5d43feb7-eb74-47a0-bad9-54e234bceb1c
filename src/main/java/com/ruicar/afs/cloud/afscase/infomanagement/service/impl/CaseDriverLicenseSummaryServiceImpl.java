package com.ruicar.afs.cloud.afscase.infomanagement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruicar.afs.cloud.afscase.infomanagement.entity.CaseDriverLicenseSummary;
import com.ruicar.afs.cloud.afscase.infomanagement.mapper.CaseDriverLicenseSummaryMapper;
import com.ruicar.afs.cloud.afscase.infomanagement.service.CaseDriverLicenseSummaryService;
import org.springframework.stereotype.Service;

/**
 * 功能说明:驾驶证智能识别汇总
 * <AUTHOR>
 */
@Service
public class CaseDriverLicenseSummaryServiceImpl extends ServiceImpl<CaseDriverLicenseSummaryMapper, CaseDriverLicenseSummary> implements CaseDriverLicenseSummaryService {

}
