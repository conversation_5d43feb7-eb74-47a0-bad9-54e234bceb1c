package com.ruicar.afs.cloud.afscase.onlinemortgage.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.common.utils.CaseConstants;
import com.ruicar.afs.cloud.afscase.onlinemortgage.condition.MortgageInfoCondition;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.CaseMortgageRecord;
import com.ruicar.afs.cloud.afscase.onlinemortgage.entity.VehicleMortgageRegistration;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.mapper.VehicleMortgageRegistrationMapper;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.CaseMortgageRecordService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.MortgageCommonService;
import com.ruicar.afs.cloud.afscase.onlinemortgage.service.VehicleMortgageRegistrationService;
import com.ruicar.afs.cloud.afscase.workflow.WorkflowHelper;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConfigProperties;
import com.ruicar.afs.cloud.afscase.workflow.entity.FlowConstant;
import com.ruicar.afs.cloud.afscase.workflow.entity.WorkflowTaskInfo;
import com.ruicar.afs.cloud.afscase.workflow.entity.bo.StartFlowRequestBo;
import com.ruicar.afs.cloud.afscase.workflow.entity.param.SubmitTaskParam;
import com.ruicar.afs.cloud.afscase.workflow.service.WorkflowTaskInfoService;
import com.ruicar.afs.cloud.common.core.constant.CommonConstants;
import com.ruicar.afs.cloud.common.core.enums.AfsEnumUtil;
import com.ruicar.afs.cloud.common.core.query.QueryCondition;
import com.ruicar.afs.cloud.common.core.security.util.SecurityUtils;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.common.modules.afscorebusiness.enums.UseSceneEnum;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * 抵押/解抵押
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class MortgageCommonServiceImpl implements MortgageCommonService {
    private final FlowConfigProperties flowConfigProperties;
    private final RedissonClient redissonClient;
    private final WorkflowHelper workflowHelper;
    private final WorkflowTaskInfoService workflowTaskInfoService;
    private final VehicleMortgageRegistrationService vehicleMortgageRegistrationService;
    private final CaseMortgageRecordService mortgageRecordService;
    private final VehicleMortgageRegistrationMapper vehicleMortgageRegistrationMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public IResponse mortgageStart(MortgageInfoCondition condition) {
        // 校验
        MortgageTypeEnum enumByKey = MortgageTypeEnum.getEnumByKey(condition.getMortgageType());
        List<VehicleMortgageRegistration> list = vehicleMortgageRegistrationService.lambdaQuery()
            .eq(VehicleMortgageRegistration::getApplyNo, condition.getApplyNo())
            .eq(VehicleMortgageRegistration::getMortgageType, enumByKey)
            .list();
        Assert.isTrue(CollUtil.isNotEmpty(list),"数据不存在");
        // 待签约 签约完成 预抵押失败 可以撤销回到草稿状态重新编辑
        if (!List.of(MortgageStatusEnum.TO_BE_SIGNED, MortgageStatusEnum.SIGNING_COMPLETED,
            MortgageStatusEnum.MORTGAGE_ERROR).contains(list.get(0).getMortgageStatus())) {
            return IResponse.fail("当前状态不可发起撤销");
        }
        String signApplyNo = condition.getSignApplyNo();
        if (StrUtil.isBlank(signApplyNo)) {
            signApplyNo = condition.getApplyNo() + condition.getMortgageType();
        }
        RLock lock = redissonClient.getLock(CaseConstants.SYNC_MORTGAGE_LOCK_KEY + ":" + signApplyNo);
        try {
            String desc = MortgageTypeEnum.getDescByKey(condition.getMortgageType());
            Assert.isTrue(!lock.isLocked(), "不可重复提交!");
            StartFlowRequestBo startFlowRequestBo = new StartFlowRequestBo();
            startFlowRequestBo.setParams(new JSONObject());
            startFlowRequestBo.getParams().put(FlowConstant.BUSINESS_NO, signApplyNo);
            startFlowRequestBo.getParams()
                .put(FlowConstant.STATUS_AT_TIME_OF_REVOCATION, AfsEnumUtil.key(list.get(0).getMortgageStatus()));
            startFlowRequestBo.setBusinessNo(signApplyNo);
            startFlowRequestBo.setPackageId(flowConfigProperties.getMortgagePackageId());
            startFlowRequestBo.setTemplateId(StrUtil.equals(desc, "抵押")
                ? flowConfigProperties.getMortgageTemplateId()
                : flowConfigProperties.getMortgageUntieTemplateId());
            String time = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
            startFlowRequestBo.setSubject(signApplyNo +time);
            IResponse response = workflowHelper.startFlow(startFlowRequestBo, StrUtil.equals(desc, "抵押")
                ? UseSceneEnum.MORTGAGE_APPLY_REVOKE
                : UseSceneEnum.MORTGAGE_APPLY_UNTIE_REVOKE);
            Assert.isTrue(CaseConstants.CODE_SUCCESS.equals(response.getCode()), "流程发起异常！!");

            // 日志
            CaseMortgageRecord entity = new CaseMortgageRecord();
            entity.setApplyNo(condition.getApplyNo());
            entity.setMortgageType(enumByKey);
            entity.setLog("发起撤销流程");
            mortgageRecordService.save(entity);
            // 保存
            vehicleMortgageRegistrationService.update(Wrappers.<VehicleMortgageRegistration>lambdaUpdate()
                .eq(VehicleMortgageRegistration::getApplyNo, condition.getApplyNo())
                .eq(VehicleMortgageRegistration::getMortgageType, enumByKey)
                .set(VehicleMortgageRegistration::getRevokeRemark, condition.getRevokeRemark())
                .set(VehicleMortgageRegistration::getMortgageStatus, StrUtil.equals(desc, "抵押")
                    ? MortgageStatusEnum.MORTGAGE_REV
                    : MortgageStatusEnum.MORTGAGE_RELEASE_REV));
            return IResponse.success("流程发起成功！");
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    public IResponse mortgageSubmit(List<MortgageInfoCondition> conditions) {
        String username = SecurityUtils.getUsername();
        int successNum = 0;
        int failNum = 0;
        for (MortgageInfoCondition condition : conditions) {
            try {
                WorkflowTaskInfo workflowTaskInfo = workflowTaskInfoService.getOne(Wrappers.<WorkflowTaskInfo>query()
                        .lambda()
                        .eq(WorkflowTaskInfo::getBusinessNo, condition.getSignApplyNo())
                        .eq(WorkflowTaskInfo::getTaskId, condition.getTaskId())
                        .eq(WorkflowTaskInfo::getProcessInstanceId, condition.getProcessInstanceId()));
                Assert.isTrue(workflowTaskInfo != null, "流程任务不存在！");
                Assert.isTrue(username.equals(workflowTaskInfo.getAssign()), "审批人不对！");
                SubmitTaskParam submitTaskParam = new SubmitTaskParam();
                JSONObject flowVariables = new JSONObject();
                flowVariables.put("bizOperationType", condition.getOperationType());
                submitTaskParam.setOperationType(condition.getOperationType());
                submitTaskParam.setApproveSuggest(condition.getOperationType());
                submitTaskParam.setApproveSuggestName(condition.getOperationRemark());
                submitTaskParam.setRemark(condition.getOperationRemark());
                submitTaskParam.setTaskId(condition.getTaskId());
                submitTaskParam.setNodeId(condition.getProcessInstanceId());
                submitTaskParam.setUserDefineIndex(condition.getUserDefinedIndex());
                submitTaskParam.setUseName(username);
                submitTaskParam.setExtendParams(flowVariables);
                // 提交流程
                IResponse<Boolean> iResponse = workflowHelper.submitTask(submitTaskParam,
                        StrUtil.equals(condition.getFlowTemplateId(),flowConfigProperties.getMortgageTemplateId())
                                ? UseSceneEnum.MORTGAGE_APPLY_REVOKE
                                : UseSceneEnum.MORTGAGE_APPLY_UNTIE_REVOKE);
                Assert.isTrue(CommonConstants.SUCCESS.equals(iResponse.getCode()), "流程提交失败");
                successNum++;
            } catch (Exception e) {
                failNum++;
                log.error("{}流程提交失败,异常{}", condition.getSignApplyNo(), e);
            }
        }
        return IResponse.success("操作完成,其中成功[" + successNum + "]条" + "," + "异常[" + failNum + "]条");
    }

    @Override
    public IResponse queryTaskList(QueryCondition<MortgageInfoCondition> queryCondition) {
        MortgageInfoCondition condition = queryCondition.getCondition();
        condition.setAssign(SecurityUtils.getUsername());
        Assert.isTrue(StrUtil.isAllNotBlank(condition.getFlowPackageId(), condition.getFlowTemplateId(), condition.getUserDefinedIndex(), condition.getTaskNodeName(), condition.getAssign()), "查询参数异常");
        return IResponse.success(vehicleMortgageRegistrationMapper.queryWorkList(new Page(queryCondition.getPageNumber(), queryCondition.getPageSize()), condition));
    }
}
