package com.ruicar.afs.cloud.afscase.onlinemortgage.condition;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageUserTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.PlateTypeEnum;
import com.ruicar.afs.cloud.image.entity.ComAttachmentFile;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 车辆线上抵押登记查询条件
 */
@Data
public class VehicleMortgageRegistrationCondition {

    /**
     * 页码
     */
    private Integer pageNumber = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 抵押合同编号
     */
    private String signApplyNo;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 抵押类型
     */
    private MortgageTypeEnum mortgageType;

    /**
     * 签署时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 签署时间开始
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTimeStart;

    /**
     * 签署时间结束
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTimeEnd;

    /**
     * 车辆性质
     * 公户 pubic
     * 个人 person
     */
    private MortgageUserTypeEnum userType;

    /**
     * 主债权发放时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issuingTime;

    /**
     * 主债权发放时间开始
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issuingTimeStart;

    /**
     * 主债权发放时间结束
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issuingTimeEnd;

    /**
     * 主债权到期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    /**
     * 主债权到期时间开始
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateStart;

    /**
     * 主债权到期时间结束
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateEnd;

    // ========== 金融机构信息 ==========

    /**
     * 金融机构
     */
    private String financialInstitution;

    /**
     * 金融机构统一社会信用代码
     */
    private String financialInstitutionUniqueCode;

    // ========== 所有人信息 ==========

    /**
     * 所有人身份证明类型 (A-居民身份证)
     */
    private String ownerIdType;

    /**
     * 所有人身份证明号码
     */
    private String ownerIdNo;

    /**
     * 所有人姓名
     */
    private String ownerName;

    /**
     * 所有人手机号码
     */
    private String ownerPhone;

    // ========== 车辆信息 ==========

    /**
     * 车架号
     */
    private String vinNo;

    /**
     * 号牌种类
     */
    private PlateTypeEnum plateType;

    /**
     * 预上牌地发证机关
     */
    private String issuingAuthority;

    /**
     * 预上牌信息(城市加上牌头信息)
     */
    private String plannedPlatePrefix;

    // ========== 代理人信息 (可选) ==========

    /**
     * 代理人身份证明类型 (A-居民身份证)
     */
    private String agentIdType;

    /**
     * 代理人身份证明号码
     */
    private String agentIdNo;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 代理人联系电话
     */
    private String agentPhone;

    // ========== 抵押权人代理人信息 ==========

    /**
     * 抵押权人代理人身份证明类型 (A-居民身份证)
     */
    private String mortgageeAgentIdType;

    /**
     * 抵押权人代理人身份证明号码
     */
    private String mortgageeAgentIdNo;

    /**
     * 抵押权人代理人姓名
     */
    private String mortgageeAgentName;

    /**
     * 抵押权人代理人联系电话
     */
    private String mortgageeAgentPhone;

    /**
     * 抵押状态
     */
    private MortgageStatusEnum mortgageStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 旧抵押状态
     */
    private String oldMortgageStatus;

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 附件列表
     */
    private List<ComAttachmentFile> fileList;

    /**
     * 代理人身份证附件
     */
    private List<ComAttachmentFile> agentFileList;

    /**
     * 所有人身份证附件
     */
    private List<ComAttachmentFile> ownerFileList;
}
