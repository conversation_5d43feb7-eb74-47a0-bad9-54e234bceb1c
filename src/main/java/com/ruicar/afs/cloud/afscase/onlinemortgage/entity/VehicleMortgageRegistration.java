package com.ruicar.afs.cloud.afscase.onlinemortgage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.BusinessStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageStatusEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.MortgageUserTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.PlateTypeEnum;
import com.ruicar.afs.cloud.afscase.onlinemortgage.enums.SfzmmcEnum;
import com.ruicar.afs.cloud.common.core.entity.BaseEntity;

import lombok.Data;

import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 车辆线上抵押登记表
 */
@Data
@TableName("case_vehicle_mortgage_registration")
public class VehicleMortgageRegistration extends BaseEntity<VehicleMortgageRegistration> {

    /**
     * 抵押合同编号
     */
    private String applyNo;

    /**
     * 签约关联合同号
     */
    private String signApplyNo;

    /**
     * 抵押编号
     */
    private String mortContractNo;

    /**
     * 渠道id
     */
    private String channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 发起人
     */
    private String sponsor;

    /**
     * 抵押类型
     * mortgage 抵押
     * releaseMortgage 解押
     */
    private MortgageTypeEnum mortgageType;

    /**
     * 签署时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 车辆性质
     */
    private MortgageUserTypeEnum userType;

    /**
     * 主债权发放时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issuingTime;

    /**
     * 主债权到期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate;

    // ========== 金融机构信息 ==========

    /**
     * 金融机构
     */
    private String financialInstitution;

    /**
     * 金融机构统一社会信用代码
     */
    private String financialInstitutionUniqueCode;

    // ========== 所有人信息 预审批带过来==========

    /**
     * 所有人身份证明类型 (A-居民身份证)
     */
    private SfzmmcEnum ownerIdType;

    /**
     * 所有人身份证明号码
     */
    private String ownerIdNo;

    /**
     * 所有人姓名
     */
    private String ownerName;

    /**
     * 所有人手机号码
     */
    private String ownerPhone;

    /**
     * 所有人地址
     */
    private String ownerAddress;

    /**
     * 所有人邮编
     */
    private String ownerPostalCode;

    // ========== 车辆信息 ==========

    /**
     * 车架号
     */
    private String vinNo;

    /**
     * 号牌种类
     */
    private PlateTypeEnum plateType;

    /**
     * 预上牌地发证机关
     */
    private String issuingAuthority;

    /**
     * 预上牌信息(城市加上牌头信息)
     */
    private String plannedPlatePrefix;

    // ========== 代理人信息 (可选) ==========

    /**
     * 代理人编号
     */
    private String agentNo;

    /**
     * 代理人身份证明类型 (A-居民身份证)
     */
    private SfzmmcEnum agentIdType;

    /**
     * 代理人身份证明号码
     */
    private String agentIdNo;

    /**
     * 代理人姓名
     */
    private String agentName;

    /**
     * 代理人联系电话
     */
    private String agentPhone;

    // ========== 抵押权人代理人信息 ==========

    /**
     * 抵押权人代理人身份证明类型 (A-居民身份证)
     */
    private SfzmmcEnum mortgageeAgentIdType;

    /**
     * 抵押权人代理人身份证明号码
     */
    private String mortgageeAgentIdNo;

    /**
     * 抵押权人代理人姓名
     */
    private String mortgageeAgentName;

    /**
     * 抵押权人代理人联系电话
     */
    private String mortgageeAgentPhone;

    /**
     * 抵押状态
     */
    private MortgageStatusEnum mortgageStatus;

    /**
     * 无锡所处理状态
     */
    private BusinessStatusEnum dealStatus;

    /**
     * 无锡所登记证书编号
     */
    private String certNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交
     */
    @TableField(exist = false)
    private String approve;

    /**
     * 撤销备注
     */
    private String revokeRemark;

    /**
     * 失败原因(无锡所)
     */
    private String failureReason;
}
