package com.ruicar.afs.cloud.afscase.approveinspectionrule.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruicar.afs.cloud.afscase.apply.entity.FilingProductGroup;
import com.ruicar.afs.cloud.afscase.apply.service.FilingProductGroupService;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.condition.AffiliatedCompanyRuleCondition;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.entity.AffiliatedCompanyRule;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.service.AffiliatedCompanyRuleService;
import com.ruicar.afs.cloud.afscase.approveinspectionrule.vo.AffiliatedCompanyRuleVo;
import com.ruicar.afs.cloud.afscase.vehicle.condition.MultipartFileCondition;
import com.ruicar.afs.cloud.afscase.workflow.feign.ProducePlanFeign;
import com.ruicar.afs.cloud.common.core.exception.AfsBaseException;
import com.ruicar.afs.cloud.common.core.util.IResponse;
import com.ruicar.afs.cloud.config.api.address.dto.AddrQueryDto;
import com.ruicar.afs.cloud.config.api.address.service.AddressService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/affiliatedCompanyRule")
@Api("挂靠公司规则配置")
public class AffiliatedCompanyRuleController {

    private AffiliatedCompanyRuleService affiliatedCompanyRuleService;
    private ProducePlanFeign producePlanFeign;
    private AddressService addressService;
    private FilingProductGroupService filingProductGroupService;

    /**
     * 保存挂靠公司规则
     * @param affiliatedCompanyRule
     * @return
     */
    @PostMapping("/saveCompanyRule")
    @ApiOperation("保存挂靠公司规则")
    public IResponse saveCompanyRule(@RequestBody AffiliatedCompanyRule affiliatedCompanyRule){
        boolean flag = false;
        if (ObjectUtil.isNotEmpty(affiliatedCompanyRule.getAddressinfo())){
            affiliatedCompanyRule.setChannelProvince(affiliatedCompanyRule.getAddressinfo()[0]);
            affiliatedCompanyRule.setChannelCity(affiliatedCompanyRule.getAddressinfo()[1]);
        }else {
            affiliatedCompanyRule.setChannelProvince(null);
            affiliatedCompanyRule.setChannelCity(null);
        }
        if (StrUtil.isEmpty(affiliatedCompanyRule.getAffiliatedName())){
            affiliatedCompanyRule.setAffiliatedName(null);
        }
        if (StrUtil.isEmpty(affiliatedCompanyRule.getSocUniCrtCode())){
            affiliatedCompanyRule.setSocUniCrtCode(null);
        }
        if (ArrayUtil.isNotEmpty(affiliatedCompanyRule.getProductIds())) {
            StringBuilder produceNames = new StringBuilder();
            StringBuilder productIds = new StringBuilder();
            int length = affiliatedCompanyRule.getProductIds().length;
            for (int i = 0;i < length;i++){
                IResponse resp = producePlanFeign.getProductById(affiliatedCompanyRule.getProductIds()[i]);
                if (Objects.equals("0000", resp.getCode())){
                    JSONObject productPlan = Optional.ofNullable(resp.getData()).map(JSONObject::toJSONString).map(JSONObject::parseObject).orElse(new JSONObject());
                    String produceName = productPlan.getString("productName");
                    produceNames.append(produceName);
                    productIds.append(affiliatedCompanyRule.getProductIds()[i]);
                    if (i < length-1){
                        produceNames.append(",");
                        productIds.append(",");
                    }
                }
            }
            affiliatedCompanyRule.setProductId(productIds.toString());
            affiliatedCompanyRule.setProductName(produceNames.toString());
        }else {
            affiliatedCompanyRule.setProductId(null);
            affiliatedCompanyRule.setProductName(null);
        }
        if (ObjectUtil.isNotNull(affiliatedCompanyRule.getId())){
            flag = affiliatedCompanyRuleService.updateById(affiliatedCompanyRule);
        }else {
            if (StrUtil.isNotEmpty(affiliatedCompanyRule.getChannelProvince()) && StrUtil.isNotEmpty(affiliatedCompanyRule.getChannelCity())){
                AffiliatedCompanyRule rule = affiliatedCompanyRuleService.getOne(Wrappers.<AffiliatedCompanyRule>lambdaQuery()
                        .eq(AffiliatedCompanyRule::getChannelProvince,affiliatedCompanyRule.getChannelProvince())
                        .eq(AffiliatedCompanyRule::getChannelCity,affiliatedCompanyRule.getChannelCity())
                        .isNull(AffiliatedCompanyRule::getSocUniCrtCode).isNull(AffiliatedCompanyRule::getAffiliatedName)
                        .isNull(AffiliatedCompanyRule::getProductId));
                if (ObjectUtil.isNotNull(rule)){
                    throw new AfsBaseException("改经销商省市规则已存在");
                }
            }
            if (StrUtil.isNotEmpty(affiliatedCompanyRule.getAffiliatedName()) || StrUtil.isNotEmpty(affiliatedCompanyRule.getSocUniCrtCode())){
                LambdaQueryWrapper<AffiliatedCompanyRule> wrapper = Wrappers.lambdaQuery();
                wrapper.isNull(AffiliatedCompanyRule::getProductId);
                wrapper.isNull(AffiliatedCompanyRule::getChannelProvince);
                wrapper.and(wq ->wq.eq(StrUtil.isNotEmpty(affiliatedCompanyRule.getAffiliatedName()),AffiliatedCompanyRule::getAffiliatedName,affiliatedCompanyRule.getAffiliatedName())
                        .or()
                        .eq(StrUtil.isNotEmpty(affiliatedCompanyRule.getSocUniCrtCode()),AffiliatedCompanyRule::getSocUniCrtCode,affiliatedCompanyRule.getSocUniCrtCode()));
                AffiliatedCompanyRule rule = affiliatedCompanyRuleService.getOne(wrapper);
                if (ObjectUtil.isNotNull(rule)){
                    throw new AfsBaseException("该挂靠公司或者统一社会信用代码规则已存在");
                }
            }
            if (StrUtil.isNotEmpty(affiliatedCompanyRule.getProductId()) && (StrUtil.isNotEmpty(affiliatedCompanyRule.getSocUniCrtCode()) || StrUtil.isNotEmpty(affiliatedCompanyRule.getAffiliatedName()))){
                LambdaQueryWrapper<AffiliatedCompanyRule> wrapper = Wrappers.lambdaQuery();
                wrapper.like(AffiliatedCompanyRule::getProductId,affiliatedCompanyRule.getProductId());
                wrapper.and(wq ->wq.eq(StrUtil.isNotEmpty(affiliatedCompanyRule.getAffiliatedName()),AffiliatedCompanyRule::getAffiliatedName,affiliatedCompanyRule.getAffiliatedName())
                        .or()
                        .eq(StrUtil.isNotEmpty(affiliatedCompanyRule.getSocUniCrtCode()),AffiliatedCompanyRule::getSocUniCrtCode,affiliatedCompanyRule.getSocUniCrtCode()));
                wrapper.isNull(AffiliatedCompanyRule::getChannelProvince);
                wrapper.isNull(AffiliatedCompanyRule::getChannelName);
                AffiliatedCompanyRule rule = affiliatedCompanyRuleService.getOne(wrapper);
                if (ObjectUtil.isNotNull(rule)){
                    throw new AfsBaseException("该挂靠公司或者统一社会信用代码下此产品规则已存在");
                }
            }
            flag = affiliatedCompanyRuleService.save(affiliatedCompanyRule);
        }
        if (flag){
            return IResponse.success("操作成功");
        }else {
            return IResponse.fail("操作失败");
        }
    }

    @PostMapping("/findMatchingRule")
    public IResponse findMatchingRule(@RequestBody AffiliatedCompanyRuleCondition affiliatedCompanyRuleCondition){
        List<AffiliatedCompanyRule> rules = affiliatedCompanyRuleService.list();
        Optional<AffiliatedCompanyRule> optional = rules.stream()
                .filter(rule -> isFieldMatch(rule.getChannelProvince(), affiliatedCompanyRuleCondition.getChannelProvince()))
                .filter(rule -> isFieldMatch(rule.getChannelCity(), affiliatedCompanyRuleCondition.getChannelCity()))
                .filter(rule -> isFieldMatch(rule.getChannelName(), affiliatedCompanyRuleCondition.getChannelName()))
                .filter(rule -> isFieldMatch(rule.getAffiliatedName(), affiliatedCompanyRuleCondition.getAffiliatedName()))
                .filter(rule -> isFieldMatch(rule.getSocUniCrtCode(), affiliatedCompanyRuleCondition.getSocUniCrtCode()))
                .filter(rule -> isFieldMatchProduct(rule.getProductId(),affiliatedCompanyRuleCondition.getProductId()))
                .max(Comparator.comparingInt(this::countNonNullFields));
        if (optional.isPresent()){
            return IResponse.success(optional.get());
        }
        return IResponse.success(null);
    }


    /**
     * 根据输入参数查询匹配的规则
     * 匹配原则：字段组合最具体的规则优先(非空字段最多的规则)
     */
    public Optional<AffiliatedCompanyRule> findMatchingRule(String channelProvince,String channelCity,String channelName,
                                                       String affiliatedName, String socUniCrtCode,String productId) {
        List<AffiliatedCompanyRule> rules = affiliatedCompanyRuleService.list();
        return rules.stream()
                .filter(rule -> isFieldMatch(rule.getChannelProvince(), channelProvince))
                .filter(rule -> isFieldMatch(rule.getChannelCity(), channelCity))
                .filter(rule -> isFieldMatch(rule.getChannelName(), channelName))
                .filter(rule -> isFieldMatch(rule.getAffiliatedName(), affiliatedName))
                .filter(rule -> isFieldMatch(rule.getSocUniCrtCode(), socUniCrtCode))
                .filter(rule -> isFieldMatchProduct(rule.getProductId(),productId))
                .max(Comparator.comparingInt(this::countNonNullFields));
    }

    /**
     * 计算规则中非空字段的数量
     * @param rule
     * @return
     */
    private int countNonNullFields(AffiliatedCompanyRule rule) {
        int count = 0;
        if (rule.getChannelProvince() != null) {
            count++;
        }
        if (rule.getChannelCity() != null) {
            count++;
        }
        if (rule.getChannelName() != null){
            count++;
        }
        if (rule.getAffiliatedName() != null) {
            count++;
        }
        if (rule.getSocUniCrtCode() != null) {
            count++;
        }
        if (rule.getProductId() != null){
            count++;
        }
        return count;
    }

    /**
     * 检查字段是否匹配
     * @param ruleField
     * @param inputField
     * @return
     */
    private boolean isFieldMatch(String ruleField, String inputField) {
        // 如果规则中该字段为空，表示匹配任何输入
        if (ruleField == null || ruleField.isEmpty()) {
            return true;
        }
        // 否则需要精确匹配
        return ruleField.equals(inputField);
    }

    private boolean isFieldMatchProduct(String ruleField, String inputField){
        // 如果规则中该字段为空，表示匹配任何输入
        if (ruleField == null || ruleField.isEmpty()) {
            return true;
        }
        return ruleField.contains(inputField);
    }



    /**
     * 查询挂靠公司规则
     * @param condition
     * @return
     */
    @PostMapping("/queryCompanyRule")
    @ApiOperation("查询挂靠公司规则")
    public IResponse<IPage<AffiliatedCompanyRule>> queryCompanyRule(@RequestBody AffiliatedCompanyRuleCondition condition){
        if (StrUtil.isAllNotEmpty(condition.getAddressinfo())){
            condition.setChannelProvince(condition.getAddressinfo()[0]);
            condition.setChannelCity(condition.getAddressinfo()[1]);
        }
        IPage<AffiliatedCompanyRule> iPage = affiliatedCompanyRuleService.page(new Page<>(condition.getPageNumber(),condition.getPageSize())
                ,Wrappers.<AffiliatedCompanyRule>lambdaQuery()
                        .eq(StrUtil.isNotEmpty(condition.getChannelProvince()),AffiliatedCompanyRule::getChannelProvince,condition.getChannelProvince())
                        .eq(StrUtil.isNotEmpty(condition.getChannelCity()),AffiliatedCompanyRule::getChannelCity,condition.getChannelCity())
                        .eq(StrUtil.isNotEmpty(condition.getChannelName()),AffiliatedCompanyRule::getChannelName,condition.getChannelName())
                        .eq(StrUtil.isNotEmpty(condition.getAffiliatedName()),AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                        .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()),AffiliatedCompanyRule::getSocUniCrtCode,condition.getSocUniCrtCode())
                        .like(StrUtil.isNotEmpty(condition.getProductName()),AffiliatedCompanyRule::getProductName,condition.getProductName())
                        .eq(StrUtil.isNotEmpty(condition.getAttachmentNum()),AffiliatedCompanyRule::getAttachmentNum,condition.getAttachmentNum())
                        .eq(ObjectUtil.isNotNull(condition.getChannelProvince()),AffiliatedCompanyRule::getChannelProvince,condition.getChannelProvince())
                        .eq(StrUtil.isNotEmpty(condition.getRuleState()),AffiliatedCompanyRule::getRuleState,condition.getRuleState()));
        List<String> codes = new ArrayList<>();
        for (AffiliatedCompanyRule rule : iPage.getRecords()){
            if (StrUtil.isNotEmpty(rule.getChannelProvince())){
                codes.add(rule.getChannelProvince());
            }
            if (StrUtil.isNotEmpty(rule.getChannelCity())){
                codes.add(rule.getChannelCity());
            }
        }
        if (codes.size() > 0){
            List<AddrQueryDto> addList = addressService.getAddrListByCodes(codes);
            if (CollectionUtil.isNotEmpty(addList)){
                for (AffiliatedCompanyRule companyRule : iPage.getRecords()){
                    if(StrUtil.isNotEmpty(companyRule.getChannelCity())){
                        for(AddrQueryDto param:addList){
                            if(param.getValue().equals(companyRule.getChannelCity())){
                                companyRule.setChannelCityName(param.getLabel());
                                break;
                            }
                        }
                    }
                    if(StrUtil.isNotEmpty(companyRule.getChannelProvince())){
                        for(AddrQueryDto param:addList){
                            if(param.getValue().equals(companyRule.getChannelProvince())){
                                companyRule.setChannelProvinceName(param.getLabel());
                                break;
                            }
                        }
                    }
                }
            }
        }
        return IResponse.success(iPage);
    }

    @PostMapping("/queryCompanyRuleList")
    @ApiOperation("查询挂靠公司规则列表")
    public IResponse queryCompanyRuleList(@RequestBody AffiliatedCompanyRuleCondition condition){
        AffiliatedCompanyRule rule = new AffiliatedCompanyRule();
        try {
            LambdaQueryWrapper<AffiliatedCompanyRule> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(AffiliatedCompanyRule::getRuleState,"2")
                    .isNull(AffiliatedCompanyRule::getProductId)
                    .isNotNull(AffiliatedCompanyRule::getAttachmentNum)
                    .isNull(AffiliatedCompanyRule::getAffiliatedName)
                    .isNull(AffiliatedCompanyRule::getSocUniCrtCode)
                    .eq(AffiliatedCompanyRule::getChannelProvince,condition.getChannelProvince())
                    .eq(AffiliatedCompanyRule::getChannelCity,condition.getChannelCity());
            wrapper.last("limit 1");
            rule = affiliatedCompanyRuleService.getOne(wrapper);
            if (ObjectUtil.isEmpty(rule)){
                if (StrUtil.isNotEmpty(condition.getAffiliatedName()) || StrUtil.isNotEmpty(condition.getSocUniCrtCode())){
                    wrapper.eq(AffiliatedCompanyRule::getRuleState,"2")
                            .isNull(AffiliatedCompanyRule::getProductId)
                            .isNotNull(AffiliatedCompanyRule::getAttachmentNum)
                            .eq(AffiliatedCompanyRule::getChannelProvince,condition.getChannelProvince())
                            .eq(AffiliatedCompanyRule::getChannelCity,condition.getChannelCity());
                    wrapper.and(wq ->wq.eq(AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                            .or()
                            .eq(AffiliatedCompanyRule::getSocUniCrtCode,condition.getSocUniCrtCode()));
                    wrapper.last("limit 1");
                    rule = affiliatedCompanyRuleService.getOne(wrapper);
                    if (ObjectUtil.isEmpty(rule)){
                        wrapper = Wrappers.lambdaQuery();
                        wrapper.eq(AffiliatedCompanyRule::getRuleState,"2")
                                .isNull(AffiliatedCompanyRule::getProductId)
                                .isNotNull(AffiliatedCompanyRule::getAttachmentNum);
                        wrapper.and(wq ->wq.eq(AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                                .or()
                                .eq(AffiliatedCompanyRule::getSocUniCrtCode,condition.getSocUniCrtCode()));
                        wrapper.last("limit 1");
                        rule = affiliatedCompanyRuleService.getOne(wrapper);
                    }
                }
            }
        }catch (Exception e){
            log.info("查询挂靠公司规则列表异常",e);
        }
        return IResponse.success(rule);
    }

    @PostMapping("/queryAffiliatedProuctRule")
    @ApiOperation("查询信审挂靠公司产品规则")
    public IResponse queryAffiliatedProuctRule(@RequestBody AffiliatedCompanyRuleCondition condition){
        AffiliatedCompanyRule rule = new AffiliatedCompanyRule();
        try {
            LambdaQueryWrapper<AffiliatedCompanyRule> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                    .isNull(AffiliatedCompanyRule::getProductId)
                    .isNull(AffiliatedCompanyRule::getAffiliatedName)
                    .eq(AffiliatedCompanyRule::getChannelProvince, condition.getChannelProvince())
                    .eq(AffiliatedCompanyRule::getChannelCity, condition.getChannelCity());
            wrapper.last("limit 1");
            rule = affiliatedCompanyRuleService.getOne(wrapper);
            if (ObjectUtil.isEmpty(rule)) {
                wrapper = Wrappers.lambdaQuery();
                wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                        .like(AffiliatedCompanyRule::getProductId, condition.getProductId())
                        .isNull(AffiliatedCompanyRule::getChannelProvince)
                        .isNull(AffiliatedCompanyRule::getAffiliatedName)
                        .isNull(AffiliatedCompanyRule::getSocUniCrtCode);
                wrapper.last("limit 1");
                rule = affiliatedCompanyRuleService.getOne(wrapper);
                if (ObjectUtil.isEmpty(rule)) {
                    wrapper = Wrappers.lambdaQuery();
                    wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                            .like(AffiliatedCompanyRule::getProductId, condition.getProductId())
                            .eq(AffiliatedCompanyRule::getChannelProvince, condition.getChannelProvince())
                            .eq(AffiliatedCompanyRule::getChannelCity, condition.getChannelCity())
                            .isNull(AffiliatedCompanyRule::getAffiliatedName)
                            .isNull(AffiliatedCompanyRule::getSocUniCrtCode);
                    wrapper.last("limit 1");
                    rule = affiliatedCompanyRuleService.getOne(wrapper);
                    if (ObjectUtil.isEmpty(rule)) {
                        if (StrUtil.isNotEmpty(condition.getAffiliatedName()) || StrUtil.isNotEmpty(condition.getSocUniCrtCode())) {
                            wrapper = Wrappers.lambdaQuery();
                            wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                                    .isNull(AffiliatedCompanyRule::getProductId)
                                    .isNull(AffiliatedCompanyRule::getChannelProvince);
                            wrapper.and(wq -> wq.eq(StrUtil.isNotEmpty(condition.getAffiliatedName()), AffiliatedCompanyRule::getAffiliatedName, condition.getAffiliatedName())
                                    .or()
                                    .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()), AffiliatedCompanyRule::getSocUniCrtCode, condition.getSocUniCrtCode()));
                            wrapper.last("limit 1");
                            rule = affiliatedCompanyRuleService.getOne(wrapper);
                            if (ObjectUtil.isEmpty(rule)) {
                                wrapper = Wrappers.lambdaQuery();
                                wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                                        .like(AffiliatedCompanyRule::getProductId, condition.getProductId())
                                        .isNull(AffiliatedCompanyRule::getChannelProvince);
                                wrapper.and(wq -> wq.eq(StrUtil.isNotEmpty(condition.getAffiliatedName()), AffiliatedCompanyRule::getAffiliatedName, condition.getAffiliatedName())
                                        .or()
                                        .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()), AffiliatedCompanyRule::getSocUniCrtCode, condition.getSocUniCrtCode()));
                                wrapper.last("limit 1");
                                rule = affiliatedCompanyRuleService.getOne(wrapper);
                                if (ObjectUtil.isEmpty(rule)) {
                                    wrapper = Wrappers.lambdaQuery();
                                    wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                                            .isNull(AffiliatedCompanyRule::getProductId)
                                            .eq(AffiliatedCompanyRule::getChannelProvince, condition.getChannelProvince())
                                            .eq(AffiliatedCompanyRule::getChannelCity, condition.getChannelCity());
                                    wrapper.and(wq -> wq.eq(StrUtil.isNotEmpty(condition.getAffiliatedName()), AffiliatedCompanyRule::getAffiliatedName, condition.getAffiliatedName())
                                            .or()
                                            .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()), AffiliatedCompanyRule::getSocUniCrtCode, condition.getSocUniCrtCode()));
                                    wrapper.last("limit 1");
                                    rule = affiliatedCompanyRuleService.getOne(wrapper);
                                    if (ObjectUtil.isEmpty(rule)) {
                                        wrapper = Wrappers.lambdaQuery();
                                        wrapper.eq(AffiliatedCompanyRule::getRuleState, "2")
                                                .like(AffiliatedCompanyRule::getProductId, condition.getProductId())
                                                .eq(AffiliatedCompanyRule::getChannelProvince, condition.getChannelProvince())
                                                .eq(AffiliatedCompanyRule::getChannelCity, condition.getChannelCity());
                                        wrapper.and(wq -> wq.eq(StrUtil.isNotEmpty(condition.getAffiliatedName()), AffiliatedCompanyRule::getAffiliatedName, condition.getAffiliatedName())
                                                .or()
                                                .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()), AffiliatedCompanyRule::getSocUniCrtCode, condition.getSocUniCrtCode()));
                                        wrapper.last("limit 1");
                                        rule = affiliatedCompanyRuleService.getOne(wrapper);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("查询信审挂靠公司产品规则异常:",e);
        }
        return IResponse.success(rule);
    }

    @PostMapping("/queryFilingProductRule")
    @ApiOperation("查询备案规则")
    public IResponse queryFilingProductRule(@RequestBody AffiliatedCompanyRuleCondition condition){
        List<FilingProductGroup> filingProductGroups = filingProductGroupService.list(Wrappers.<FilingProductGroup>lambdaQuery()
                .eq(FilingProductGroup::getProductId,condition.getProductId()));
        if (CollectionUtil.isEmpty(filingProductGroups)){
            return IResponse.success(null);
        }
        LambdaQueryWrapper<AffiliatedCompanyRule> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AffiliatedCompanyRule::getRuleState,"2")
                .eq(AffiliatedCompanyRule::getChannelName,condition.getChannelName())
                .eq(AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                .like(AffiliatedCompanyRule::getProductId,condition.getProductId());
        wrapper.and(wq -> wq.eq(AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                .or()
                .eq(AffiliatedCompanyRule::getSocUniCrtCode,condition.getSocUniCrtCode()));
        List<AffiliatedCompanyRule> list = affiliatedCompanyRuleService.list(wrapper);
        if (CollectionUtil.isNotEmpty(list)){
            return IResponse.success(null);
        }
        return IResponse.fail(null);
    }

    /**
     * 删除挂靠公司规则
     * @param affiliatedCompanyRule
     * @return
     */
    @PostMapping("/removeRule")
    @ApiOperation("删除挂靠公司规则")
    public IResponse removeRule(@RequestBody AffiliatedCompanyRule affiliatedCompanyRule){
        return IResponse.success(affiliatedCompanyRuleService.removeById(affiliatedCompanyRule));
    }

    @PostMapping("/updateRule")
    @ApiOperation("修改挂靠公司规则状态")
    public IResponse updateRule(@RequestBody AffiliatedCompanyRule affiliatedCompanyRule){
        return IResponse.success(affiliatedCompanyRuleService.update(Wrappers.<AffiliatedCompanyRule>lambdaUpdate()
                .set(AffiliatedCompanyRule::getRuleState,affiliatedCompanyRule.getRuleState())
                .eq(AffiliatedCompanyRule::getId,affiliatedCompanyRule.getId())));
    }

    @ApiOperation("下载导入模板")
    @GetMapping(value = "/downTemplate")
    public void downTemplate(HttpServletResponse response) throws Exception{
        List<AffiliatedCompanyRuleVo> list = new ArrayList<>();
        //数据组装输出excel
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("挂靠规则导入模板_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        ExcelWriterBuilder writerBuilder = EasyExcel.write(response.getOutputStream(), AffiliatedCompanyRuleVo.class);
        //内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        writerBuilder.sheet("挂靠规则").doWrite(list);
    }

    @ApiOperation("导出挂靠规则")
    @PostMapping("/exportRules")
    public void exportRules(@RequestBody AffiliatedCompanyRuleCondition condition,HttpServletResponse response) throws Exception{
        if (StrUtil.isAllNotEmpty(condition.getAddressinfo())){
            condition.setChannelProvince(condition.getAddressinfo()[0]);
            condition.setChannelCity(condition.getAddressinfo()[1]);
        }
        List<AffiliatedCompanyRule> list = affiliatedCompanyRuleService.list(Wrappers.<AffiliatedCompanyRule>lambdaQuery()
                        .eq(StrUtil.isNotEmpty(condition.getChannelProvince()),AffiliatedCompanyRule::getChannelProvince,condition.getChannelProvince())
                        .eq(StrUtil.isNotEmpty(condition.getChannelCity()),AffiliatedCompanyRule::getChannelCity,condition.getChannelCity())
                        .eq(StrUtil.isNotEmpty(condition.getChannelName()),AffiliatedCompanyRule::getChannelName,condition.getChannelName())
                        .eq(StrUtil.isNotEmpty(condition.getAffiliatedName()),AffiliatedCompanyRule::getAffiliatedName,condition.getAffiliatedName())
                        .eq(StrUtil.isNotEmpty(condition.getSocUniCrtCode()),AffiliatedCompanyRule::getSocUniCrtCode,condition.getSocUniCrtCode())
                        .like(StrUtil.isNotEmpty(condition.getProductName()),AffiliatedCompanyRule::getProductName,condition.getProductName())
                        .eq(StrUtil.isNotEmpty(condition.getAttachmentNum()),AffiliatedCompanyRule::getAttachmentNum,condition.getAttachmentNum())
                        .eq(ObjectUtil.isNotNull(condition.getFinancingAmount()),AffiliatedCompanyRule::getFinancingAmount,condition.getFinancingAmount())
                        .eq(StrUtil.isNotEmpty(condition.getRuleState()),AffiliatedCompanyRule::getRuleState,condition.getRuleState()));
        if(CollUtil.isEmpty(list)){
            throw new AfsBaseException("暂无挂靠规则");
        }

        //获取规则用到省市的code
        List<String> codes = new ArrayList<>();
        for (AffiliatedCompanyRule rule : list){
            if (StrUtil.isNotEmpty(rule.getChannelProvince())){
                codes.add(rule.getChannelProvince());
            }
            if (StrUtil.isNotEmpty(rule.getChannelCity())){
                codes.add(rule.getChannelCity());
            }
        }
        List<AddrQueryDto> addList;
        if (codes.size() > 0){
            addList = addressService.getAddrListByCodes(codes);
        } else {
            addList = new ArrayList<>();
        }
        List<AffiliatedCompanyRuleVo> voList = list.stream().map(vo -> {
            AffiliatedCompanyRuleVo companyRuleVo = new AffiliatedCompanyRuleVo();
            if (CollectionUtil.isNotEmpty(addList) && StrUtil.isNotEmpty(vo.getChannelProvince())){
                for(AddrQueryDto param : addList){
                    if(param.getValue().equals(vo.getChannelProvince())){
                        companyRuleVo.setChannelProvince(param.getLabel());
                        break;
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(addList) && StrUtil.isNotEmpty(vo.getChannelCity())){
                for(AddrQueryDto param : addList){
                    if(param.getValue().equals(vo.getChannelCity())){
                        companyRuleVo.setChannelCity(param.getLabel());
                        break;
                    }
                }
            }
            companyRuleVo.setChannelName(vo.getChannelName());
            companyRuleVo.setAffiliatedName(vo.getAffiliatedName());
            companyRuleVo.setSocUniCrtCode(vo.getSocUniCrtCode());
            companyRuleVo.setProductName(vo.getProductName());
            companyRuleVo.setAttachmentNum(vo.getAttachmentNum());
            companyRuleVo.setFinancingAmount(vo.getFinancingAmount());
            companyRuleVo.setRuleState(StrUtil.equals("1",vo.getRuleState()) ? "失效" : "生效");
            return companyRuleVo;
        }).toList();

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("挂靠规则" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_MS_FORMAT), "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        ExcelWriterBuilder writerBuilder = EasyExcel.write(response.getOutputStream(), AffiliatedCompanyRuleVo.class);
        //内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        //设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        writerBuilder.sheet("挂靠规则").doWrite(voList);
    }

    @PostMapping("/importRules")
    @ApiOperation("导入挂靠规则")
    public IResponse importRules(@ModelAttribute MultipartFileCondition condition) throws Exception{
        MultipartFile file = condition.getUploadFile();
        List<AffiliatedCompanyRuleVo> excelVOList = new ArrayList<>();
        try{
            EasyExcelFactory.read(file.getInputStream(), AffiliatedCompanyRuleVo.class,
                    new AnalysisEventListener<AffiliatedCompanyRuleVo>() {
                        @Override
                        public void invoke(AffiliatedCompanyRuleVo excelVO, AnalysisContext analysisContext) {
                            excelVOList.add(excelVO);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                            log.info("挂靠规则导入数据解析完成！数据长度为{}", excelVOList.size());
                        }
                    }).sheet().doRead();
        }catch (Exception e){
            log.error("导入表格异常:{}",e);
            return IResponse.fail("导入表格异常！");
        }

        if (CollectionUtil.isEmpty(excelVOList)){
            return IResponse.fail("导入数据不能为空！");
        }
        log.info("导入数据{}", JSON.toJSONString(excelVOList));

        //校验导入数据信息
        List<AffiliatedCompanyRule> list = new ArrayList<>();
        for (int i = 1;i <= excelVOList.size();i++){
            AffiliatedCompanyRuleVo vo = excelVOList.get(i - 1);
            AffiliatedCompanyRule rule = new AffiliatedCompanyRule();
            //校验经销商城市
            if (StrUtil.isNotEmpty(vo.getChannelProvince()) || StrUtil.isNotEmpty(vo.getChannelCity())){
                if (StrUtil.isNotEmpty(vo.getChannelProvince()) && StrUtil.isNotEmpty(vo.getChannelCity())){
                    String provinceCode = addressService.getCodeByLabel(vo.getChannelProvince());
                    String cityCode = null;
                    if (StrUtil.equals("上海市",vo.getChannelProvince()) || StrUtil.equals("天津市",vo.getChannelProvince())
                            || StrUtil.equals("重庆市",vo.getChannelProvince()) || StrUtil.equals("北京市",vo.getChannelProvince())){
                        StringBuilder stringBuilder = new StringBuilder(provinceCode);
                        stringBuilder.setCharAt(3,'1');
                        cityCode = stringBuilder.toString();
                    }else {
                        cityCode  = addressService.getCodeByLabel(vo.getChannelCity());
                    }
                    if (StrUtil.isNotEmpty(provinceCode) && StrUtil.isNotEmpty(cityCode)){
                        rule.setChannelProvince(provinceCode);
                        rule.setChannelCity(cityCode);
                    }else {
                        return IResponse.fail("表格第"+ i +"行数据出错，经销商省份和城市需要同时存在，请检查！");
                    }
                }else {
                    return IResponse.fail("表格第"+ i +"行数据出错，经销商省份和城市需要同时存在，请检查！");
                }
            }

            //校验产品名称
            if (StrUtil.isNotEmpty(vo.getProductName())){
                List<String> ids = producePlanFeign.checkProduceByNames(Arrays.asList(vo.getProductName().split(",")));
                if (CollectionUtil.isNotEmpty(ids)){
                    StringBuilder sb = new StringBuilder();
                    for (int j = 0;j < ids.size();j++){
                        sb.append(ids.get(j));
                        if (j < ids.size() -1){
                            sb.append(",");
                        }
                    }
                    rule.setProductId(sb.toString());
                    rule.setProductName(vo.getProductName());
                }else {
                    return IResponse.fail("表格第"+ i +"行数据出错，请检查是否都是在线产品！");
                }
            }

            if (StrUtil.isEmpty(vo.getAttachmentNum()) && ObjectUtil.isEmpty(vo.getFinancingAmount())){
                return IResponse.fail("表格第"+ i +"行数据出错，挂靠数量和融资金额不能都为空！");
            }else {
                rule.setAttachmentNum(vo.getAttachmentNum());
                rule.setFinancingAmount(vo.getFinancingAmount());
            }

            if (StrUtil.isNotEmpty(vo.getRuleState()) && (StrUtil.equals("生效",vo.getRuleState()) || StrUtil.equals("失效",vo.getRuleState()))){
                rule.setRuleState(StrUtil.equals("生效",vo.getRuleState()) ? "2" : "1");
            }else {
                return IResponse.fail("表格第"+ i +"行数据出错，规则状态不能为空,规则状态应为生效或者失效!");
            }
            rule.setAffiliatedName(vo.getAffiliatedName());
            rule.setSocUniCrtCode(vo.getSocUniCrtCode());
            rule.setChannelName(vo.getChannelName());
            list.add(rule);
        }
        log.info("新增配置list：{}",list);
        affiliatedCompanyRuleService.saveBatch(list);
        return IResponse.success("导入成功");
    }
}
