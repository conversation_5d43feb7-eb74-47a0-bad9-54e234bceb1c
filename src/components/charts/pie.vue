<template>
    <div class="charts chart-pie" ref="dom"></div>
</template>

<script>
    import echarts from 'echarts'
    import tdTheme from './theme.json'
    import {on, off} from '@/libs/tools'

    echarts.registerTheme('tdTheme', tdTheme);
    export default {
        name: 'Chart<PERSON>ie',
        props: {
            value: Array,
            text: String,
            subtext: String
        },
        data() {
            return {
                dom: null
            }
        },
        methods: {
            resize() {
                this.dom.resize()
            }
        },
        mounted() {
            this.$nextTick(() => {
                let legend = this.value.map(_ => _.name);
                let option = {
                    title: {
                        text: this.text,
                        subtext: this.subtext,
                        x: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b} : {c} ({d}%)'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left',
                        data: legend
                    },
                    series: [
                        {
                            type: 'pie',
                            radius: '55%',
                            center: ['50%', '60%'],
                            data: this.value,
                            itemStyle: {
                                emphasis: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
                this.dom = echarts.init(this.$refs.dom, 'tdTheme');
                this.dom.setOption(option);
                on(window, 'resize', this.resize)
            })
        },
        beforeDestroy() {
            off(window, 'resize', this.resize)
        }
    }
</script>
