<template>
    <div :class="[menuLocationClass,themeClass,systemClass,'main']">
        <template v-if="!forceChangePass && !target">
            <LayoutLeftMenu v-if="isLeftMenu"/>
            <LayoutTopMenu v-if="isTopMenu"/>
        </template>
        <div style="padding-top: 100px" v-else-if="forceChangePass">
            <Row align="middle" style="height: 100%" type="flex">
                <Col offset="7" span="10">
                    <ChangePass title="请修改密码"/>
                </Col>
            </Row>
        </div>
        <div style="padding-top: 100px" v-else-if="target">
            <AutoOpen></AutoOpen>
        </div>
        <AiQa></AiQa>
        <reminder></reminder>
        <!-- <Softphone v-if="systemName === 'afsCoreBusiness'" /> -->
        <!-- <call-center-record v-if="systemName === 'afsCoreBusiness'" /> -->
        <!-- <newsoftPhone></newsoftPhone> -->
    </div>
</template>
<script>
// import main from './main';
import LayoutLeftMenu from "./LayoutLeftMenu"
import LayoutTopMenu from "./LayoutTopMenu";
import config from "@/config";
import {mapGetters} from "vuex";
import {getStore} from "@/libs/utils/store";
import Constants from "@/const/common/Constants";
import ChangePass from './components/change-pass/change-pass'
import AutoOpen from './components/auto-open/Auto-open'
//add by gjq 1025 软电话
import Softphone from  "_p/basic/pages/softphone/softphone"
//add by jxl 1201 来电提醒
import callCenterRecord from "_p/basic/pages/call-center-record/callCenterRecord";
// import newsoftPhone from '@/components/main/newsoftPhone.vue'
import reminder from "./components/reminder/reminder"
import AiQa from './components/ai-qa/Ai-qa'

export default {
    name: 'Main',
    components: {LayoutLeftMenu, LayoutTopMenu,ChangePass,AutoOpen,Softphone,callCenterRecord,reminder,AiQa},
    // mixins: [main],
    created() {

    },
    computed: {
        ...mapGetters(['forceChangePass']),
        target() {
            return !!getStore({name: Constants.afs_login_target_key})
        },
        isLeftMenu() {
            return _AFS_PROJECT_CONFIG.menuLocation === 'left';
        },
        isTopMenu() {
            return _AFS_PROJECT_CONFIG.menuLocation === 'top';
        },
        // /不同主题下的class
        themeClass() {
            let cls = 'theme_'
            if (_AFS_PROJECT_CONFIG.theme !== undefined) {
                cls += _AFS_PROJECT_CONFIG.theme;
            }
            return cls;
        },
        systemName(){
            return _AFS_PROJECT_CONFIG.name
        },
        // 不同定位下的class
        menuLocationClass() {
            let cls = 'menuLocation_';
            if (_AFS_PROJECT_CONFIG.menuLocation !== undefined) {
                cls += _AFS_PROJECT_CONFIG.menuLocation;
            }
            return cls;
        },
        // 不同系统的 class
        systemClass() {
            let cls = 'system_';
            let name = config.getProjectName();
            if (name !== undefined) {
                cls += name;
            }
            return cls;
        },
    },
}
</script>
<style lang="less">
@import './main.less';
</style>
