<template>
    <div>

    </div>
</template>


<!--
 在线 1
 小休 2
 挂起 6
 离线 0
 -->
<script>
    import {callCenter} from './newsoft'
    import {setStore,getStore} from "@/libs/utils/store";
    import Constants from "@/const/common/Constants"
    export default{
        data(){
            return{
                sdkUrl:'',
                isInit:false,
            }
        },
        methods:{
            callPhone(phoneNumber){
                return;
                if(!window.qiConnect){
                    this.$Message.info('请稍等,电话正在初始化中...');
                    if(this.isInit){
                        return;
                    }
                    this.initData();
                    this.isInit = true;
                    setTimeout(()=>{
                        qiConnect.call(phoneNumber);
                        qiConnect.keepPanelUnfold(true);
                    },5000);
                }else{
                    qiConnect.call(phoneNumber);
                }
            },
            initData(){
                callCenter().then((res)=>{
                    if(res.code==='0000'){
                        let src = res.data.sdkUrl;
                        if(src){
                            this.initSDK(src);
                            this.sdkUrl = src;
                        }
                    }
                })
            },
            initSDK(src){
                var sdk = document.createElement('script');
                sdk.async = !0;
                sdk.src = src;
                sdk.onload = (onload)=>{
                    console.log('onload',onload);
                    qiConnect.on({
                        onload: function(onload) {//呼叫工具条加载完毕的事件通知。此事件完成后才可调用外呼接口
                            console.log('呼叫工具条加载完毕！',onload);
                        },
                        //初始化成功
                        initSuccess:(initSuccess)=>{
                            console.log('电话---initSuccess',initSuccess);
                            qiConnect.setStatus(['1']);
                        },
                        //会话开始
                        session:(session)=>{
                            console.log('电话---session开始',session);
                        },
                        //会话接通
                        answered:(answered)=>{
                            console.log('电话---answered接通',answered);
                        },
                        //通话挂断失败
                        byeFailed:(byeFailed)=>{
                            console.log('电话---byeFailed',byeFailed);
                        },
                        //会话结束
                        sessionClose:(sessionClose)=>{
                            console.log('电话---sessionClose结束',sessionClose);
                        },
                        //状态可选项变更
                        statusOptionsChanged:(statusOptionsChanged)=>{
                            console.log('电话---statusOptionsChanged',statusOptionsChanged);
                        },
                        //状态设置成功
                        statusChanged:(statusChanged)=>{
                            console.log('电话---statusChanged',statusChanged[0]);
                        },
                        //状态设置失败
                        statusChangeFailed:(statusChangeFailed)=>{
                            console.log('电话---statusChangeFailed',statusChangeFailed);
                        },
                        //overProcess
                        overProcess:(overProcess)=>{
                            console.log('电话---overProcess',overProcess	);
                        }
                    })
                }
                document.body.appendChild(sdk);
            }
        },
        created() {

        },
        mounted() {

        }
    }
    window.onbeforeunload = function(e){
    	console.log("刷新浏览器执行");
        if(window.qiConnect){
           window.qiConnect.logoff();
        }
    }
</script>

<style>
    #CONTAINER-CC-TOOLBAR {
        top: 0px;
        right: 50px;
        z-index: 0;
        margin-bottom: 10px;
    }
</style>
