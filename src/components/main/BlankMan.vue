<template>
    <div style="height: 100%">
        <div v-if="init"  style="height: 100%">
            <div v-watermark="waterMark"   :style="waterMarkStyle"></div>
            <component :is="component"></component>
        </div>
    </div>
</template>
<script>
    import {Base64} from 'js-base64'
    import {fullPath,projectView} from "@/libs/lazyLoading";
    import config from "@/config";
    import {getAmPm} from "@/libs/tools";
    import moment from 'moment'
    import {getSysTime} from "_p/basic/api/system-date";
    import Constants from "@/const/common/Constants";
    export default {
        name:'blank-main',
        data(){
            return{
                component:null,
                componentPath:'',
                isFull:false,
                params:null,
                init:false,
                refreshTime:-1,
                refreshLock:false,
                ssoRefreshTime:-1,
                time:null
            }
        },
        computed:{
            loader(){
                if(this.componentPath!==''){
                    if(this.isFull){
                        return fullPath(this.componentPath);
                    }else{
                        return projectView(this.componentPath);
                    }
                }else{
                    return null;
                }
            },
            waterMarkStyle(){
                return {
                    height: 'calc(100% - 10px)',
                    width: 'calc(100% - 10px)',
                    position: 'fixed',
                    top:'10px',
                    left:'10px',
                    zIndex:99999,
                    pointerEvents:'none'
                }
            },
            waterMark(){
                if(!this.init){
                    return {text:''}
                }
                return {
                    text:this.$store.state.user.userInfo.username + ' ' +
                        moment().format('YYYY/MM/DD') +
                        getAmPm() +
                        moment().format('hh:mm:ss')
                }
            }
        },
        methods:{

        },
        created(){
            const jsonData = JSON.parse(Base64.decode(this.$route.query._link,true));
            this.componentPath = jsonData.pageData.component;
            this.isFull = jsonData.pageData.isFull;
            this.params = jsonData.pageData.params;
            window.document.title = config.getTitle() +'-'+ jsonData.pageData.pageTitle;
            console.log(jsonData,'blank Main Init')
            console.log(this,'blank Main Init')
            getSysTime().then(res => {
                if (res.code === "0000" && res.data) {
                    this.time = moment(res.data.bhDate).format('YYYY-MM-DD');
                    this.$store.commit('updateBatchDate',this.time);
                    this.init = true;
                }
            })
        },
        mounted(){
            this.component = this.loader;
        },
        destroyed() {
            clearInterval(this.refreshTime)
        }
    }
</script>
