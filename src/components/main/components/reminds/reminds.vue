<template>
    <div  class="lock-screen-btn-con" style="padding-top: 3px">
        <Tooltip placement="bottom" theme="light">
            <Badge :count="count">
                <Icon :size="24" type="ios-notifications-outline"></Icon>
            </Badge>
            <div slot="content">
                <div v-for="item in reminds" >{{item.id}}   {{item.count}}条留言</div>
            </div>
        </Tooltip>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
    name: "reminds",
    props: {
        value: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        ...mapGetters({ count: "remindCount", reminds: "reminds" })
    },
    methods: {
    },
    mounted() {
    }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-badge-count {
    top: 0px
}
</style>
