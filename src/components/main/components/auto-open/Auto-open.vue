<template>
    <div>

    </div>
</template>
<script>
import {Base64} from 'js-base64'
import {getStore,removeStore} from '@/libs/utils/store';
import Constants from "@/const/common/Constants";
export default {
    name:'target-open',
    data(){
        return{
        }
    },
    computed:{

    },
    methods:{
        openLink(){
            let _target = getStore({name:Constants.afs_login_target_key});
            if(_target.type==Constants.auto_open_type_dlink){
                let {token,pageData} = _target;
                console.log("aaa5"+Base64.encode(JSON.stringify({token:token,pageData:pageData}), true));

                setTimeout(()=>{ removeStore({name:Constants.afs_login_target_key,type:Constants.session_store_type})},2000)
                this.$router.replace('/d/afsLink?_link=' + Base64.encode(JSON.stringify({token:token,pageData:pageData}), true));

            }
        }
    },
    created(){

    },
    mounted(){
        this.openLink();
    }
}
</script>
