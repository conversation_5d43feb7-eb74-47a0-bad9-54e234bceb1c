<template>
  <div>
    <!-- 触发AI问答弹窗的按钮 -->
    <div class="ivu-dropdown-rel">
      <div class="i-feedback" @click="showChat = true">
        <img src="https://leasing.byd.com/prod-oss/static/ai.gif" />
      </div>
    </div>
    <div v-if="showChat" class="chat-popup">
      <div class="chat-title">
        <span style="color:red;">温馨提示：留言反馈问题格式：全界面截图+单号+操作内容+异常描述</span>
        <Icon type="md-close" class="close" :size="22" @click="showChat = false"/>
      </div>
      <iframe
        frameborder="0"
        width="100%"
        height="98%"
        :src="geturl()"
        allow="camera *; microphone *"
      >
      </iframe>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import CryptoJS from 'crypto-js';
export default {
  name: "ai-qa",
  data() {
    return {
      showChat: false,
    };
  },
  computed: {
    ...mapGetters(["userAllInfo","userInfo"]),
  },
  methods: {
    geturl() {
      const username = "fls" + this.userInfo.username;
      const keyHex = CryptoJS.enc.Utf8.parse("bc9e6fk5h6ea21e77fb365d2ecfedliv");
      var encrypted = CryptoJS.DES.encrypt(username, keyHex, {
          mode: CryptoJS.mode.ECB,
          padding: CryptoJS.pad.Pkcs7
      });
      let jsUsername = encrypted.ciphertext.toString();
      return `https://leasing.byd.com/aichat/?workno_sec=${jsUsername}&external_system_from=cwc_fls&version=2`;

      // https://leasingtest.byd.com:9800/aichat/?workno_sec=b8accb9ed2ddf07d4ed923197631130f&external_system_from=cwc_fls&version=2
      // https://bigdata-aichat.byd.com/aichat/?workno_sec=b8accb9ed2ddf07d4ed923197631130f&external_system_from=cwc_fls&version=2
    },
    // channelFullName(){
    //   return this.userAllInfo && this.userAllInfo.userExtInfo && this.userAllInfo.userExtInfo.channelInfo ? this.userAllInfo.userExtInfo.channelInfo.channelFullName : null
    // }
  },
};
</script>

<style lang="less" scoped>
.chat-popup {
  text-align: right;
  position: fixed;
  top: 100px;
  bottom: 20px;
  right: 20px;
  width: 500px;
  z-index: 1000; /* 确保弹窗在其他内容之上 */
  background: #fff;
  padding: 10px;
  box-shadow: 0px 3px 5px rgba(0, 0, 0, 0.2);
  .close{
    cursor: pointer;
    padding-top: 0px;
  }
}
.ivu-dropdown-rel {
    position: relative;
}
.i-feedback {
  display: block;
  border-radius: 50%;
  text-align: center;
  position: fixed;
  bottom: 48px;
  right: -30px;
  z-index: 2;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  transition: right 0.3s ease;
  cursor: pointer;
  transform: rotate(-60deg);
  img {
    vertical-align: middle
  }
}
.i-feedback:hover {
  right: 5px;
  transform: rotate(0);
}
.chat-title{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
}
</style>
