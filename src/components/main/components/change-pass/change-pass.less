.change-pass {
  margin-top: 12px;
  .ivu-poptip {
    //display: inline-block;
    //width: 100%;
  }

  .ivu-poptip-rel {
    width: 100%;
    display: inline-block;
    position: relative;
  }

  .ivu-slider-button {
    display: none;
  }

  .ivu-slider-button-wrap .ivu-tooltip {
    display: none;
  }

  .password-tip-none {
    padding: 1vh 0;
    font-size: 14px;

    .ivu-slider-bar {
      height: 4px;
      border-radius: 3px;
      position: absolute;
    }
  }

  .password-tip-weak {
    padding: 1vh 0;
    font-size: 14px;

    .words {
      color: #ed3f14;
    }

    .ivu-slider-bar {
      height: 4px;
      background: #ed3f14;
      border-radius: 3px;
      position: absolute;
    }
  }

  .password-tip-middle {
    padding: 1vh 0;
    font-size: 14px;

    .words {
      color: #faad14;
    }

    .ivu-slider-bar {
      height: 4px;
      background: #faad14;
      border-radius: 3px;
      position: absolute;
    }
  }

  .password-tip-strong {
    padding: 1vh 0;
    font-size: 14px;

    .words {
      color: #52c41a;
    }

    .ivu-slider-bar {
      height: 4px;
      background: #52c41a;
      border-radius: 3px;
      position: absolute;
    }
  }

  &-btn-box {
    margin-bottom: 10px;

    button {
      padding-left: 0;

      span {
        color: #2D8CF0;
        transition: all .2s;
      }

      span:hover {
        color: #0C25F1;
        transition: all .2s;
      }
    }
  }
}
