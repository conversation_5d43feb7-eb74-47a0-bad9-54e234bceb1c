<template>
    <Modal
        v-model="modal"
        title="修改密码"
        :mask-closable="false"
        footer-hide
        @on-ok="submit"
        @on-cancel="cancel"
    >
        <div class="ChangePassword">
            <div v-show="pubLoaded" class="change-pass">
                <Form :label-width="100" :model="editPasswordForm" :rules="passwordValidate" label-position="right"
                      ref="editPasswordForm">
                    <FormItem label="原密码" prop="oldPass">
                        <Input placeholder="请输入现在使用的密码" type="afspassword" password
                               v-model="editPasswordForm.oldPass"></Input>
                    </FormItem>
                    <FormItem label="新密码" prop="newPass">
                        <Poptip placement="right" trigger="focus" width="250">
                            <Input @input="strengthChange" placeholder="请输入新密码" type="afspassword" password
                                   v-model="editPasswordForm.newPass"></Input>
                            <div slot="content" v-bind:class="tipStyle">
                                <span class="words">强度 : {{ strength }}</span>
                                <Slider :step="33" style="width:95%" v-model="strengthValue"></Slider>
                                <p style="font-size: 12px;color: #FA8072">密码长度在
                                    [{{ this.passRule.minLength }}-{{ this.passRule.maxLength }}]之间</p>
                                <p style="font-size: 12px;color: #FA8072" v-for="(item,index) in passRule.passRules"
                                   :key="index">
                                    {{ item.msg }}
                                </p>
                            </div>
                        </Poptip>
                    </FormItem>
                    <FormItem label="确认新密码" prop="rePass">
                        <Input placeholder="请再次输入新密码" type="afspassword" password
                               v-model="editPasswordForm.rePass"></Input>
                    </FormItem>
                    <div class="footer">
                        <Button v-show="title==''" @click="cancel">取消</Button>&nbsp;&nbsp;&nbsp;
                        <Button :loading="savePassLoading" @click="saveEditPass" type="primary">保存</Button>
                    </div>
                </Form>
            </div>
        </div>
    </Modal>

</template>
<script>
import changePass from "./changePass"

export default {
    name: 'ChangePassword',
    mixins: [changePass],
    props: {
        openModal: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            modal: this.openModal,
        }
    },
    watch: {
        openModal(val) {
            this.modal = val
        }
    },
    methods: {
        cancel(bool) {
            this.$emit('closeModal')
        },
        submit() {
            this.saveEditPass()
        }
    }
}
</script>
<style lang="less">
@import "./change-pass";
.ChangePassword {
    .footer {
        text-align: end;
    }
}
</style>
