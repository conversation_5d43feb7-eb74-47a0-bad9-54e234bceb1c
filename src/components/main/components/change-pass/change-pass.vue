<style lang="less">
    @import "change-pass.less";
</style>
<template>
        <Card v-show="pubLoaded" class="change-pass">
            <p slot="title">
                <Icon type="key"></Icon>
                <span style="color: #de7264" v-show="title!=''">{{title}}</span><span v-show="title==''">修改密码</span>
            </p>
            <div>
                <Form :label-width="100" :model="editPasswordForm" :rules="passwordValidate" label-position="right"
                      ref="editPasswordForm" style="width:450px">
                    <FormItem label="原密码" prop="oldPass">
                        <Input placeholder="请输入现在使用的密码" type="afspassword" password v-model="editPasswordForm.oldPass"></Input>
                    </FormItem>
                    <FormItem label="新密码" prop="newPass">
                        <Poptip placement="right" trigger="focus" width="250">
                            <Input @input="strengthChange" placeholder="请输入新密码" type="afspassword" password
                                   v-model="editPasswordForm.newPass"></Input>
                            <div slot="content" v-bind:class="tipStyle">
                                <span class="words">强度 : {{strength}}</span>
                                <Slider :step="33" style="width:95%" v-model="strengthValue"></Slider>
                                <p style="font-size: 12px;color: #FA8072">密码长度在 [{{this.passRule.minLength}}-{{this.passRule.maxLength}}]之间</p>
                                <p style="font-size: 12px;color: #FA8072" v-for="item in passRule.passRules">
                                    {{item.msg}}
                                </p>
                            </div>
                        </Poptip>
                    </FormItem>
                    <FormItem label="确认新密码" prop="rePass">
                        <Input placeholder="请再次输入新密码" type="afspassword" password v-model="editPasswordForm.rePass"></Input>
                    </FormItem>
                    <FormItem style="text-align: center">
                        <Button v-show="title==''" @click="cancelEditPass">取消 </Button>&nbsp;&nbsp;
                        <Button :loading="savePassLoading" @click="saveEditPass" type="primary"> 保存</Button>
                    </FormItem>
                </Form>
            </div>
        </Card>
</template>

<script>
import changePass from "./changePass"

export default {
    name: "change_pass",
    mixins: [changePass],
};

</script>
