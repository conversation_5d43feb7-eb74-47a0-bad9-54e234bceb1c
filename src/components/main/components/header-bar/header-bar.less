.header-bar{
  width: 100%;
  height: 100%;
  position: relative;
  .sider-trigger-a{
    padding: 6px 0;
  }
  .custom-content-con-left{
    float: left;
    line-height: 60px;
    height: 60px;
  }
  //.custom-content-con{
  //  float: right;
  //  height: auto;
  //  padding-right: 20px;
  //  line-height: 60px;
  //  & > *{
  //    float: right;
  //  }
  //}
  .custom-content-con-right{
    float: right;
    padding-right: 10px;
    line-height: 60px;
    height: 60px;
    & > *{
      float: right;
    }
  }
}
.menuLocation_top .header-bar{
  display: flex;
  justify-content: space-between;

  .custom-content-con-left{
    float: none;
    display: flex;
    flex-flow: row nowrap;
  }
  .custom-content-con-right{
    flex: 0 0 auto;
    float: none;
    & > *{
      float: right;
    }
  }
}
