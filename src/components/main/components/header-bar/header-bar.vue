<template>
    <div class="header-bar">
        <sider-trigger :collapsed="collapsed" @on-change="handleCollpasedChange" icon="md-menu"
                       v-if="isLeftMenu"></sider-trigger>
        <custom-bread-crumb v-if="isLeftMenu" :list="breadCrumbList" show-icon style="margin-left: 10px;"></custom-bread-crumb>
        <slot name="left"></slot>
        <slot name="right"></slot>
    </div>
</template>
<script>
    import siderTrigger from './sider-trigger'
    import customBreadCrumb from './custom-bread-crumb'
    import './header-bar.less'

    export default {
        name: 'HeaderBar',
        components: {
            siderTrigger,
            customBreadCrumb
        },
        props: {
            collapsed: Boolean
        },
        computed: {
            breadCrumbList() {
                return this.$store.state.menu.breadCrumbList
            },
            isLeftMenu(){
                return _AFS_PROJECT_CONFIG.menuLocation=='left';
            }
        },
        methods: {
            handleCollpasedChange(state) {
                this.$emit('on-coll-change', state)
            }
        }
    }
</script>
