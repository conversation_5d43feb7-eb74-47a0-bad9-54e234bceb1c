.no-select{
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.size{
  width: 100%;
  height: 100%;
}
.tags-nav{
  position: relative;
  //border-top: 1px solid #F0F0F0;
  //border-bottom: 1px solid #F0F0F0;
  background: #ffffff;
  .no-select;
  .size;
  .close-con{
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    background: #fff;
    text-align: center;
    z-index: 10;
    height: 30px;
    .ivu-btn-text:hover{
      color:#eb9620;
    }
  }
  .btn-con{
    position: absolute;
    top: 0px;
    background: #fff;
    width: 28px;
    padding-top: 3px;
    z-index: 10;
    height: 28px; // 页面放大 高度遮挡首页底部横线
    .ivu-btn-text:hover{
      color:#eb9620;
    }
    button{
      padding: 2px 2px;
      line-height: 14px;
      text-align: center;
    }
    &.left-btn{
      left: 0px;
    }
    &.right-btn{
      right: 26px;
    }
  }
  .scroll-outer{
    position: absolute;
    left: 23px;
    right: 58px;
    top: 0;
    bottom: 0;
    height: 30px;
    background: #ffffff;
    overflow: hidden;
    .scroll-body{
      display: inline-block;
      position: absolute;
      overflow: visible;
      white-space: nowrap;
      transition: left .3s ease;
      height: 28px;

      .ivu-tag-primary {
        background: #ffffff;
        color: #2d8cf0;
        border-bottom: 2px solid #2d8cf0 !important;

        .ivu-tag-text {
          color: #2d8cf0 !important;
        }

        .ivu-icon-ios-close {
          color: #2d8cf0 !important;
        }
      }
      .ivu-tag {
        padding: 0 10px;
        border: none;
        border-radius: 0;
        margin: 0;
        height: 30px;
        line-height: 30px;
        background-color: #ffffff;
      }
    }
  }
  .contextmenu {
    position: absolute;
    margin: 0;
    padding: 5px 0;
    background: #fff;
    z-index: 1000;
    list-style-type: none;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .1);
    li {
      margin: 0;
      padding: 5px 15px;
      cursor: pointer;
      &:hover {
        background: #eee;
      }
    }
  }
}

.menuLocation_top .tags-nav {
  .scroll-outer {
    height: auto;
    .scroll-body {
      height: auto;
      .ivu-tag {
      }
      .ivu-tag {
        padding: 0 10px;
        border: none;
        border-radius: 0;
        margin: 0;
        height: 36px;
        line-height: 36px;
        background-color: transparent;
        border-bottom: 2px solid  transparent;
      }
    }
  }
  .btn-con{
    padding-top: 0;
  }
}
