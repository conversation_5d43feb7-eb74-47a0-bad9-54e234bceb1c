.user{
  &-avatar-dropdown{
    cursor: pointer;
    display: inline-block;
     //height: 60px;
    vertical-align: middle;
    // line-height: 64px;

    .color-white{
      color: #515a6e;
    }
    .ivu-dropdown{
      .ivu-select-dropdown{
        transform-origin: center top 0px;
        position: absolute;
        top: 55px !important;
        right: 2px !important;
        will-change: top;
        text-align: center;
        z-index: 9999;
        .ivu-dropdown-item{
          line-height: 17px;
          //padding: 0px 16px;
        }
      }
    }

  }
}

.menuLocation_top {
  .user-avatar-shadow {
    height: 50px;
    width: 1px;
    background: linear-gradient(270deg, #DFDFDF 0%, #FFFFFF 100%);
    align-self: flex-start;
  }

  .main-user-box {
    padding-left: 16px;
    padding-right: 6px;
    display: flex;
    justify-content: flex-start;
    flex-flow: row;
    align-items: center;
  }
  .main-user-avatar {
    width: 30px;
    height: 30px;
    //font-size: 40px;
    //line-height: 40px;
    padding: 5px;
  }
  .main-user-name {
    margin-left: 8px;
    margin-right: 2px;
    font-size: 16px;
    color: #333333;
    line-height: 20px;
  }

}

.user-info-detail {
  min-width: 150px;
  max-width: 270px;
  padding: 7px 16px;
  color: #333333;

  .user-detail-name {
    padding-bottom: 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
  }

  .user-detail-phone {
    padding-bottom: 8px;
    font-size: 12px;
    line-height: 17px;
  }

  .user-detail-role {
    padding-bottom: 4px;

    span {
      display: inline-block;
      margin-right: 8px;
      margin-bottom: 4px;
      padding: 2px 8px;
      background: rgba(235, 150, 32, 0.1);
      border-radius: 12px;
      border: 1px solid #F5C88A;
      font-size: 12px;
      color: #EB9620;
    }
  }

  .user-detail-comp {
    padding-bottom: 5px;
  }
  .user-detail-line {
    width: 100%;
    padding-top: 8px;
    border-bottom: 1px solid #D8D8D8;
  }
}
div.userHeaderDropdown {
  &.top {
    //box-shadow: 0 2px 7px 3px rgba(235, 150, 32, 0.1);
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.04);
    .ivu-dropdown-item:hover {
      //background-color: inherit;
      color: #EB9620;
      svg {
        stroke: #EB9620;
        fill: #EB9620;
      }
    }
  }
  .userHeaderIcon {
    display: inline-block;
    padding-right: 4px;
    vertical-align: middle;
    svg {
      width: 16px;
      height: 16px;
      stroke: #666;
      fill: #666;
    }
  }

}

