<template>
    <div :class="classes" :style="styles" @click="back">
        <slot>
            <div :class="innerClasses">
                <i class="ivu-icon ivu-icon-ios-arrow-up"></i>
            </div>
        </slot>
    </div>
</template>
<script>
    import {scrollTop} from '@/libs/menu/util'
    import {off, on} from '@/libs/menu/tools'

    const prefixCls = 'ivu-back-top'

    export default {
        name: 'ABackTop',
        props: {
            height: {
                type: Number,
                default: 400
            },
            bottom: {
                type: Number,
                default: 30
            },
            right: {
                type: Number,
                default: 30
            },
            duration: {
                type: Number,
                default: 1000
            },
            container: {
                type: null,
                default: window
            }
        },
        data() {
            return {
                backTop: false
            }
        },
        mounted() {
            on(this.containerEle, 'scroll', this.handleScroll)
            on(this.containerEle, 'resize', this.handleScroll)
        },
        beforeDestroy() {
            off(this.containerEle, 'scroll', this.handleScroll)
            off(this.containerEle, 'resize', this.handleScroll)
        },
        computed: {
            classes() {
                return [
                    `${prefixCls}`,
                    {
                        [`${prefixCls}-show`]: this.backTop
                    }
                ]
            },
            styles() {
                return {
                    bottom: `${this.bottom}px`,
                    right: `${this.right}px`
                }
            },
            innerClasses() {
                return `${prefixCls}-inner`
            },
            containerEle() {
                return this.container === window ? window : (this.container===''?window:document.querySelector(this.container))
            }
        },
        methods: {
            handleScroll() {
                if(this.container==''){
                    this.backTop = window.pageYOffset >= this.height;
                    this.$store.dispatch('updateScroll', {
                        routerName: this.$route.name,
                        position: window.pageYOffset
                    })
                }else {
                    this.$store.dispatch('updateScroll', {
                        routerName: this.$route.name,
                        position: this.containerEle.scrollTop
                    })
                    this.backTop = this.containerEle.scrollTop >= this.height
                }
            },
            back(event) {
                if (!event) return;
                if(this.container==='') {
                    const sTop = document.documentElement.scrollTop || document.body.scrollTop;
                    scrollTop(window, sTop, 0, this.duration);
                }else {
                    const sTop = this.containerEle.scrollTop
                    scrollTop(this.containerEle, sTop, 0, this.duration)
                }
                this.$emit('on-click');
            },
            reStoreScroll(routerName){
                if(this.$store.state.app.scrollTop[routerName]&&this.$store.state.app.scrollTop[routerName]>0){
                    this.$nextTick(()=>{
                        scrollTop(this.containerEle,0,this.$store.state.app.scrollTop[routerName],100)
                    })
                }
            }
        }
    }
</script>
