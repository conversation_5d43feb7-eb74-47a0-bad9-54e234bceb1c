<template>
    <div></div>
</template>

<script>
import {getUri,webSocketUri} from "@/libs/tools";
import {mapGetters, mapMutations} from "vuex";
import {getRemindCount} from "../../../../projects/afs-core-business/api/home/<USER>";
export default {
    name: "reminder",
    data() {
        return {
            ws:null,
            clientId:"reminder_"+(new Date().getTime()),
            heartBeatConfig:{
                timer:null,
                timeout: 10000,
            }
        };
    },
    methods: {
        ...mapMutations(['updateRemindCount', 'updateReminds']),
        heartReset(){
            clearTimeout(this.heartBeatConfig.timer);
        },
        heartStart(){
            let self = this;
            this.heartBeatConfig.timer = setInterval(()=>{
                self.ws.send("ping");
                console.log("heart data")
            }, self.heartBeatConfig.timeout)
        },
        connectWs(self){
            self.ws = new WebSocket(`${webSocketUri()}/${getUri('template')}/websocket/${this.userInfo.username}`);
            // self.ws = new WebSocket(`wss://leasingtest.byd.com:9800/leasing/approve_uat/websocket/case/websocket/${this.userInfo.username}`);
            if(self.ws) {
                self.ws.onopen = function () {
                    self.heartReset();
                    self.heartStart();
                };
                self.ws.onclose = function () {
                    console.log("closed")
                    self.ws = null;
                    setTimeout(()=>{self.connectWs(self);},10000)
                };
                self.ws.onerror = function () {
                    self.ws.close();
                };
                self.ws.onmessage = function (evt) {
                    const random = evt.data + Math.random().toString(36).substring(2, 12)
                    console.log('message', evt.data)
                    localStorage.setItem('remindData', random)
                    if (evt.data.length !== 0) {
                        self.$Message.info({
                            content: evt.data,
                            duration: 6
                        })
                    }
                    self.$nextTick(() => {
                        self.getRemindCount();
                    })
                };
            }
        },
        getRemindCount() {
            getRemindCount(this.userInfo.username).then(res => {
                const data = res.data
                let reminds = []
                for (let key in data) {
                    if (data[key] !== '0') {
                        reminds.push({id: key, count: data[key]})
                    }
                }
                this.updateReminds(reminds)
                const numbers = Object.values(data)
                let count = 0
                numbers.map(res => {
                    count = count + Number(res)
                })
                this.updateRemindCount(count)
            })
        }
    },
    computed: {
        ...mapGetters({userInfo: "userInfo"})
    },
    created(){
        this.connectWs(this);
        this.getRemindCount();
    },
    beforeDestroy(){
        if(this.ws){
            this.ws.close();
        }
    }
};
</script>


<style>
</style>
