<template>
    <Submenu :padding-left="24" :name="`${parentName}`" >
        <template slot="title">
            <common-icon :type="parentItem.icon || ''"/>
            <span>{{ showTitle(parentItem) }}</span>
            <Badge :count="parentItem.badgeNumber"></Badge>
        </template>
        <template v-for="item in children">
            <template v-if="item.children && item.children.length === 1">
                <side-sub :deep="deep+1" :key="`menu-${item.name}`" :parent-item="item" v-if="showChildren(item)"></side-sub>
                <menu-item :padding-left="24" :context-menu="bookMarkFLag" :key="`menu-${item.children[0].name}`" :name="getNameOrHref(item, true)" v-else>
                    <common-icon :type="item.children[0].icon || ''"/>
                    <span>{{ showTitle(item.children[0]) }}</span>
                    <Badge :count="item.badgeNumber"></Badge>
                    <template slot="contextMenu" v-if="bookMarkFLag">
                        <DropdownItem v-show="!item.bookMark" @click.native="bookMark(item,true)">收藏</DropdownItem>
                        <DropdownItem v-show="item.bookMark"  @click.native="bookMark(item,false)">取消收藏</DropdownItem>
                    </template>
                </menu-item>
            </template>
            <template v-else>
                <side-sub :deep="deep+1" :key="`menu-${item.name}`" :parent-item="item" v-if="showChildren(item)"></side-sub>
                <menu-item :padding-left="24" :context-menu="bookMarkFLag" :key="`menu-${item.name}`" :name="getNameOrHref(item)" v-else>
                    <common-icon :type="item.icon || ''"/>
                    <span>{{ showTitle(item) }}</span>
                    <Badge :count="item.badgeNumber"></Badge>
                    <template slot="contextMenu" v-if="bookMarkFLag">
                        <DropdownItem v-show="!item.bookMark" @click.native="bookMark(item,true)">收藏</DropdownItem>
                        <DropdownItem v-show="item.bookMark"  @click.native="bookMark(item,false)">取消收藏</DropdownItem>
                    </template>
                </menu-item>
            </template>
        </template>
    </Submenu>
</template>
<style scoped>

</style>
<script>
    import mixin from '../minxin/mixin'
    import itemMixin from '../minxin/item-mixin'
    import {addBookMark,removeBookMark} from '@/projects/basic/api/admin/bookMark.js'
    export default {
        name: 'SideSub',
        mixins: [mixin, itemMixin],
        methods:{
          bookMark(item,flag){
              if(flag){
                  addBookMark(item.id).then(res=>{
                      if(res.code==='0000'){
                          this.$store.commit('updateBookMark', {item:item,flag:flag})
                          this.$store.commit('reCallMenuBadge');
                          this.$Message.success("收藏成功");
                      }else {
                          this.$Message.error("收藏失败请重试");
                      }
                  }).catch(e=>{
                      console.log(e)
                      this.$Message.error("收藏失败请重试");
                  })
              }else {
                  removeBookMark(item.id).then(res=>{
                      if(res.code==='0000'){
                          this.$store.commit('updateBookMark', {item:item,flag:flag})
                          this.$store.commit('reCallMenuBadge');
                          this.$Message.success("取消成功");
                      }else {
                          this.$Message.error("取消失败请重试");
                      }
                  }).catch(e=>{
                      console.log(e)
                      this.$Message.error("取消失败请重试");
                  })
              }
          }
        },
        props:{
            deep:{
                type:Number,
                default:()=>{
                    return 0
                }
            },
            bookMarkFLag:{
                type:Boolean,
                default:false
            }
        }
    }
</script>
