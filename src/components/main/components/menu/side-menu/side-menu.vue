<template>
    <div class="side-menu-wrapper">
        <slot></slot>
        <Menu :accordion="accordion" @on-open-change="handleOpenChange" :active-name="activeName" :open-names="openedNames" :theme="theme" @on-select="handleSelect"
              ref="menu" v-if="!collapsed" width="auto">
            <searchMenu id="search-menu" v-if="!collapsed" />
            <template v-for="item in menuList">
                <template>
                    <RuiMenuItem :key="`menu-${item.name}`" :book-mark-f-lag="bookMark" :parent-item="item"
                                 v-if="showChildren(item)"></RuiMenuItem>
                    <menu-item :padding-left="24" :key="`menu-${item.name}`" :name="getNameOrHref(item)" v-else>
                        <common-icon :type="item.icon || ''"/>
                        <span>{{ showTitle(item) }}</span>
                        <Badge :count="item.badgeNumber"></Badge>
                    </menu-item>
                </template>
            </template>
        </Menu>
        <div :list="menuList" class="menu-collapsed" v-if="collapsed">
            <template v-for="item in menuList">
                <collapsed-menu :icon-size="iconSize" :key="`drop-menu-${item.name}`" :parent-item="item"
                                :root-icon-size="rootIconSize" :theme="theme" @on-click="handleSelect" hide-title
                                v-if="item.children && item.children.length > 1"></collapsed-menu>
                <Tooltip :content="showTitle(item.children && item.children[0] ? item.children[0] : item)" :key="`drop-menu-${item.name}`"
                         placement="right"
                         transfer v-else>
                    <a :style="{textAlign: 'left'}" @click="handleSelect(getNameOrHref(item, true))"
                       class="drop-menu-a">
                        <common-icon :color="textColor" :size="rootIconSize"
                                     :type="item.icon || (item.children && item.children[0].icon)"/>
                    </a>
                </Tooltip>
            </template>
        </div>
    </div>
</template>
<script>
import RuiMenuItem from '../rui-menu-item.vue'
import CollapsedMenu from './collapsed-menu.vue'
import {getUnion} from '@/libs/tools'
import mixin from '../minxin/mixin'
import searchMenu from '../search-menu/'

export default {
    name: 'SideMenu',
    mixins: [mixin],
    components: {
        RuiMenuItem,
        CollapsedMenu,
        searchMenu
    },
    props: {
        menuList: {
            type: Array,
            default() {
                return []
            }
        },
        collapsed: {
            type: Boolean
        },
        theme: {
            type: String,
            default: 'light'
        },
        rootIconSize: {
            type: Number,
            default: 20
        },
        iconSize: {
            type: Number,
            default: 16
        },
        accordion: Boolean,
        bookMark:Boolean,
        activeName: {
            type: String,
            default: ''
        },
        openNames: {
            type: Array,
            default: () => []
        },
    },
    data() {
        return {
            openedNames: this.bookMark?['afsbookmark']:[],
            topLevelMenuNames:[],
            lastOpenedArray:[]
        }
    },
    methods: {
        getMenuPath(menuName,menu,findObj){
            if(menuName===menu.name){
                findObj.pathArray.push(menu.name);
                findObj.hasFind = true;
            }else{
                if(menu.children&&menu.children.length>0){
                    for (var index = 0; index < menu.children.length; index++) {
                        this.getMenuPath(menuName,menu.children[index],findObj);
                        if(findObj.hasFind){
                            findObj.pathArray.push(menu.name);
                            console.log(findObj)
                            break;
                        }
                    }
                }
            }
        },
        handleSelect(name) {
            this.$emit('on-select', name)
        },
        handleOpenChange(name){
            debugger;
            if(this.bookMark) {
                let openedMenu = name.filter(o => {
                    return o !== 'afsbookmark';
                })
                let _openedMenu = openedMenu.filter(o => {
                    return !this.lastOpenedArray.includes(o);
                })
                let hasTopMenu = openedMenu.filter(o=>{
                    return this.topLevelMenuNames.includes(o);
                })
                let opened = [], findObj = {
                    pathArray: [],
                    hasFind: false
                };
                if(hasTopMenu.length>0){
                    if (_openedMenu.length === 0 && this.lastOpenedArray.length>2) {
                        _openedMenu.push(this.lastOpenedArray[this.lastOpenedArray.length - 1])
                        console.log(_openedMenu)
                    }
                    if (_openedMenu.length > 0){
                        for (var index = 0; index < this.$store.getters.menuList.length; index++) {
                            findObj.pathArray = [];
                            findObj.hasFind = false;
                            this.getMenuPath(_openedMenu[0], this.$store.getters.menuList[index], findObj);
                            if (findObj.hasFind) {
                                break;
                            }
                        }
                        opened = findObj.pathArray;
                    }else {
                        opened = openedMenu;
                    }
                }
                opened.unshift('afsbookmark');
                this.openedNames.splice(0,this.openedNames.length);
                this.openedNames.push(...opened);
            }
        },
        getOpenedNamesByActiveName(name) {
            let names = this.$store.getters.breadCrumbList.map(item=>item.name)
            names.splice(0,1);
            return names.reverse()
        },
        updateOpenName(name) {
            if (name === this.$config.getHomeName()) {
                this.openedNames = []
                if(this.bookMark&&!this.openedNames.includes("afsbookmark")) {
                    this.openedNames.unshift("afsbookmark")
                }
            } else {
                this.openedNames = this.getOpenedNamesByActiveName(name)
                if(this.bookMark&&!this.openedNames.includes("afsbookmark")) {
                    this.openedNames.unshift("afsbookmark")
                }
            }
        }
    },
    computed: {
        textColor() {
            return this.theme === 'dark' ? '#fff' : '#495060'
        }
    },
    watch: {
        activeName(name) {
            if (this.accordion) this.openedNames = this.getOpenedNamesByActiveName(name)
            else this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
        },
        openNames(newNames) {
            this.openedNames = newNames
            if(this.bookMark&&!this.openedNames.includes("afsbookmark")){
                this.openedNames.unshift("afsbookmark")
            }
        },
        openedNames(val) {
            this.lastOpenedArray.splice(0,this.lastOpenedArray.length);
            this.lastOpenedArray.push(...val)
            this.$nextTick(() => {
                if(this.$refs.menu) {
                    this.$refs.menu.updateOpened()
                }
            })
        },
        menuList:{
            handler(){
                this.topLevelMenuNames.splice(0);
                this.menuList.forEach(menu=>{
                    if(menu.name!=='afsbookmark') {
                        this.topLevelMenuNames.push(menu.name);
                    }
                })
                console.log(this.topLevelMenuNames,"menu-top-names")
                this.$nextTick(() => {
                    if(this.$refs.menu) {
                        this.$refs.menu.updateOpened()
                    }
                })
            },
            immediate: true
        }
    },
    mounted() {
        this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
        if(this.bookMark&&!this.openedNames.includes("afsbookmark")) {
            this.openedNames.push("afsbookmark")
        }
    }
}
</script>
<style lang="less" scoped>
@import 'side-menu.less';
.side-menu-wrapper /deep/.ivu-menu-item {
    padding: 10px 15px 10px 5px;
}
.side-menu-wrapper /deep/.ivu-menu-submenu-title {
    padding: 10px 15px 10px 5px;
}
</style>
