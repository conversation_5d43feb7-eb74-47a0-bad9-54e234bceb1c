<template>
    <Dropdown :class="rootDeep?'collased-menu-dropdown':'collapsed-drop-down-menu'" :placement="placement" :transfer="hideTitle" transfer-class-name="afs-header-transfer collapsed-menu"
              @on-click="handleClick" ref="dropdown">
        <a v-if="rootDeep" :style="{textAlign: 'center'}" @mouseover="handleMousemove($event, children)" class="drop-menu-a"
           type="text">
            <common-icon :color="textColor" :size="rootIconSize" :type="parentItem.icon" class="menu-collapsed-icon"/>
            <span class="menu-title" v-if="!hideTitle">{{ showTitle(parentItem) }}</span>
            <Icon :size="16" style="" type="ios-arrow-forward" v-if="!hideTitle"/>
            <Badge status="error" v-show="parentItem.badgeNumber>0"></Badge>
        </a>
        <DropdownItem v-else class="collapsed-drop-down-menu">
            <common-icon :color="textColor" :size="rootIconSize" :type="parentItem.icon" class="menu-collapsed-icon"/>
            <span class="menu-title" v-if="!hideTitle">{{ showTitle(parentItem) }}</span>
            <Icon :size="16" style="" type="ios-arrow-forward" v-if="!hideTitle"/>
            <Badge status="error" v-show="parentItem.badgeNumber>0"></Badge>
        </DropdownItem>
        <DropdownMenu ref="dropdown" slot="list" >
            <template v-for="child in children">
                <collapsed-menu :rootDeep="false" :icon-size="iconSize" :key="`drop-${child.name}`" :parent-item="child" v-if="showChildren(child)"></collapsed-menu>
                <DropdownItem :key="`drop-${child.name}`" :name="child.name" v-else>
                    <common-icon :size="iconSize" :type="child.icon" class="menu-collapsed-icon" />
                    <span class="menu-title">{{ showTitle(child) }}</span>
                    <Badge status="error" v-show="child.badgeNumber>0"></Badge>
                </DropdownItem>
            </template>
        </DropdownMenu>
    </Dropdown>
</template>
<script>
import mixin from '../minxin/mixin'
import itemMixin from '../minxin/item-mixin'
import {findNodeUpperByClasses} from '@/libs/menu/util'

export default {
    name: 'CollapsedMenu',
    mixins: [mixin, itemMixin],
    props: {
        hideTitle: {
            type: Boolean,
            default: false
        },
        rootIconSize: {
            type: Number,
            default: 16
        },
        rootDeep:{
            type:Boolean,
            default:()=>{
                return true;
            }
        }
    },
    data() {
        return {
            placement: 'right-end',
            className:''
        }
    },
    methods: {
        handleClick(name) {
            this.$emit('on-click', name)
        },
        handleMousemove(event, children) {
            const {pageY} = event
            const height = children.length * 38
            const isOverflow = pageY + height < window.innerHeight
            this.placement = isOverflow ? 'right-start' : 'right-end'
        }
    },
    mounted() {
        let dropdown = findNodeUpperByClasses(this.$refs.dropdown.$el, ['ivu-select-dropdown', 'ivu-dropdown-transfer'])
        if (dropdown) dropdown.style.overflow = 'visible'
    },
}
</script>
<style lang="less" scoped>
.collased-menu-dropdown{
    width: 100%;
    margin: 0;
    line-height: normal;
    padding: 7px 0 6px 16px;
    clear: both;
    font-size: 12px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    &:hover{
        background: rgba(100, 100, 100, 0.1);
    }
    & * {
        color: #515a6e;
    }
    .ivu-menu-item > i{
        margin-right: 12px !important;
    }
    .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
        margin-right: 8px !important;
    }
}
</style>
