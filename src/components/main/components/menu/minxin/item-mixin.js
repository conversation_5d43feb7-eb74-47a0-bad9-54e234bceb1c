export default {
    props: {
        parentItem: {
            type: Object,
            default: () => {
            }
        },
        theme: String,
        iconSize: Number
    },
    computed: {
        parentName() {
            return this.parentItem.name
        },
        children() {
            return this.parentItem.children
        },
        isLeftMenu() {
            return _AFS_PROJECT_CONFIG.menuLocation == 'left';
        },
        isTopMenu() {
            return _AFS_PROJECT_CONFIG.menuLocation == 'top';
        },
        textColor() {
            return this.theme === 'dark' ? '#fff' : '#495060'
        }
    }
}
