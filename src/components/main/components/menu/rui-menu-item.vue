<template>
    <SideSub :parent-item="parentItem"  :book-mark-f-lag="bookMarkFLag"  v-if="isLeftMenu"/>
</template>
<script>
    import mixin from './minxin/mixin'
    import itemMixin from './minxin/item-mixin'
    import SideSub from './side-menu/side-sub'
    export default {
        name: 'RuiMenuItem',
        components:{
            SideSub
        },
        mixins: [mixin, itemMixin],
        props:{
            deep:{
                type:Number,
                default:()=>{
                    return 0
                }
            },
            bookMarkFLag:{
                type:Boolean,
                default:false
            }
        }
    }
</script>
