<template>
    <span class="topMenuBox">
        <span v-for="item in menuList" class="topSubMenu" :key="`drop-menu-${item.name}`">
            <TopSubMenu :icon-size="iconSize" :key="`drop-menu-${item.name}`" :parent-item="item"
                        :root-icon-size="rootIconSize" :theme="theme" @on-click="handleSelect" hide-title
                        v-if="item.children && item.children.length > 0"></TopSubMenu>
        </span>
    </span>
</template>

<script>
    import {getUnion} from '@/libs/tools'
    import mixin from '../minxin/mixin'
    import TopSubMenu from './top-sub.vue'

    export default {
        name: 'topMenu',
        mixins: [mixin],
        components: {
            TopSubMenu
        },
        props: {
            menuList: {
                type: Array,
                default() {
                    return []
                }
            },
            collapsed: {
                type: Boolean
            },
            theme: {
                type: String,
                default: 'light'
            },
            rootIconSize: {
                type: Number,
                default: 20
            },
            iconSize: {
                type: Number,
                default: 16
            },
            activeName: {
                type: String,
                default: ''
            },
            openNames: {
                type: Array,
                default: () => []
            }
        },
        data() {
            return {
                openedNames: []
            }
        },
        methods: {
            handleSelect(name) {
                this.$emit('on-select', name)
            },
            getOpenedNamesByActiveName(name) {
                let names = this.$store.getters.breadCrumbList.map(item => item.name)
                names.splice(0, 1);
                return names
            },
            updateOpenName(name) {
                if (name === this.$config.getHomeName()) {
                    this.openedNames = []
                } else {
                    this.openedNames = this.getOpenedNamesByActiveName(name)
                }
            }
        },
        computed: {
            textColor() {
                return this.theme === 'dark' ? '#fff' : '#495060'
            }
        },
        watch: {
            activeName(name) {
                if (this.accordion) this.openedNames = this.getOpenedNamesByActiveName(name)
                else this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
            },
            openNames(newNames) {
                this.openedNames = newNames
            },
            openedNames() {
                this.$nextTick(() => {
                    // this.$refs.menu.updateOpened()
                })
            }
        },
        mounted() {
            this.openedNames = getUnion(this.openedNames, this.getOpenedNamesByActiveName(name))
        }
    }
</script>
<style lang="less" scoped>
.topMenuBox {
    width: 100%;
    height: 60px;
    display: flex;
    justify-content: flex-start;
    flex-flow: row wrap;
    align-items: center;
}
.topSubMenu {
    float: none;
    display: inline-block;
    height: 30px;
    line-height: 30px
}
</style>
