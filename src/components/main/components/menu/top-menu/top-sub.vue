<template>
    <Dropdown :class="classes" :placement="placement" :transfer="true" transferClassName="headDropdown afs-header-transfer"
              @on-click="handleClick" ref="dropdown" >
        <a  :style="{textAlign: 'center',height:'30px'}"  type="text" v-if="deep==0">
<!--            <common-icon :color="textColor" :size="deep==0?20:rootIconSize" :type="parentItem.icon"/>-->
            <span  >{{ showTitle(parentItem) }}</span>
            <Icon v-if="deep>0" :size="rootIconSize" style="" type="ios-arrow-forward" />
        </a>
        <DropdownItem v-if="deep>0" name="none">
<!--            <common-icon :color="textColor" :size="deep==0?20:rootIconSize" :type="parentItem.icon"/>-->
            {{ showTitle(parentItem) }}
            <Icon v-if="deep>0" :size="rootIconSize" style="" type="ios-arrow-forward" />
        </DropdownItem>
        <DropdownMenu ref="subDropdown" slot="list" >
            <template v-for="child in children">
                <TopSubMenu :deep="deep+1" :icon-size="rootIconSize" :key="`drop-${child.name}`" :parent-item="child"
                                v-if="showChildren(child)"></TopSubMenu>
                <DropdownItem :key="`drop-${child.name}`" :name="child.name" v-else>
<!--                    <common-icon :size="rootIconSize" :type="child.icon"/>-->
                    <span class="menu-title">{{ showTitle(child) }}</span></DropdownItem>
            </template>
        </DropdownMenu>
    </Dropdown>
</template>
<script>
    import mixin from '../minxin/mixin'
    import itemMixin from '../minxin/item-mixin'
    import {findNodeUpperByClasses} from '@/libs/menu/util'

    export default {
        name: 'TopSubMenu',
        mixins: [mixin, itemMixin],
        props: {
            rootIconSize: {
                type: Number,
                default: 16
            },
            deep:{
                type:Number,
                default: 0
            }
        },
        data() {
            return {
            }
        },
        computed:{
            placement(){
                if(this.deep==0){
                    return "bottom-start";
                }else{
                    return "right-start";
                }
            },
            classes(){
                if(this.deep==0){
                    return "top-sub-menu-dropdown";
                }else{
                    return "top-more-menu-dropdown";
                }
            }
        },
        methods: {
            handleClick(name) {
                if(name!='none') {
                    this.$emit('on-click', name)
                }
            }
        },
        mounted() {
        }
    }
</script>
<style lang="less" scoped>
.top-sub-menu-dropdown {
    width: 100%;
    margin: 0;
    line-height: normal;
    padding: 0px 2px 0px 16px;
    clear: both;
    font-size: 14px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    transition: background 0.2s ease-in-out;
    //& * {
    //    color: #515a6e;
    //}
    span {
        color: #333333;
    }
}

.top-more-menu-dropdown {
    width: 100%;
    margin: 0;
    line-height: normal;
    padding: 0px 0 0px 0px;
    clear: both;
    font-size: 12px !important;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    transition: background 0.2s ease-in-out;

    & * {
        color: #515a6e;
    }

    &:hover {
        background: rgba(100, 100, 100, 0.1);
    }
}

.top-sub-menu-dropdown /deep/ .ivu-select-dropdown {
    width: auto;
}
</style>
