<template>
    <Layout class="LayoutLeftMenu">
        <Sider :collapsed-width="60"
               :style="{overflow: 'auto',paddingTop:'60px'}"
               :width="200"
               class="left-sider"
               collapsible
               hide-trigger
               v-model="collapsed"
        >
            <!-- 需要放在菜单上面的内容，如Logo，写在side-menu标签内部，如下 -->
            <div class="logo-con" style="position: fixed;top: 0">
                 <img :src="leftTopMaxLogo" style="width: 194px" key="max-logo" v-show="!collapsed"/>
                <img :src="minLogo" style="width: auto" key="min-logo" v-show="collapsed"/>
            </div>
            <side-menu
                :active-name="activeName"
                :collapsed="collapsed"
                :menu-list="menuList"
                @on-select="turnToPage"
                :accordion="!bookMark"
                :book-mark="bookMark"
                ref="sideMenu"
            />
        </Sider>
        <Layout>
            <Header class="header-con">
                <div class="header-fixed"></div>
                <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
                    <div class="custom-content-con-left" slot="left">
                    </div>
                    <div class="custom-content-con-right" slot="right">
                        <user :user-avatar="userHeadImg" :username="username"/>
                        <Tooltip content="任务数量" placement="bottom"><span class="TopNum"><div>{{TopNum}}</div></span></Tooltip>
                        <LookScreen style="margin-right: 10px;"></LookScreen>
                        <fullscreen style="margin-right: 10px;" v-model="isFullscreen"/>
                        <reminds style="margin-right: 10px;"></reminds>
                        <span v-for="(item,index) in topHeaders" :key="index" style="margin-right: 10px;">
                            <component :is="item"></component>
                        </span>
                    </div>
                </header-bar>
            </Header>
<!--            <Affix :offset-top="60">-->
                <div class="tag-nav-wrapper">
                    <tags-nav :list="tagNavList" :value="currentTag" @input="handleClick"
                              @on-close="handleCloseTag"/>
                </div>
<!--            </Affix>-->
            <Content>
                <div class="content-wrapper">
                    <div :style="waterMarkStyle" v-watermark="waterMark"></div>
                    <div class="content-box">
                        <lease-keep-alive :updateComponentsKey="setKey" ref="keepAlive">
                            <router-view :key="key"></router-view>
                        </lease-keep-alive>
                    </div>
                    <div class="LeaseIframe">
                        <LeaseIframe :key="item.id" :url="item.meta.url" v-for="item in iframeList" v-show="$route.name === item.name" />
                    </div>
                    <ABackTop :duration="50" ref="backTop" :bottom="80" :height="100" :right="50" container=".content-wrapper" />
                </div>
            </Content>
        </Layout>
    </Layout>
</template>
<script>
import main from './main'
export default {
    name: 'LayoutLeftMenu',
    mixins: [main]
}
</script>
<style scoped>
.TopNum div{ line-height: 20px; padding: 0 10px; margin-right: 10px;color:#ff0000;
display: inline-block;border:1px solid #ddd;border-radius:5px}
</style>
