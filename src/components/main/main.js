import SideMenu from './components/menu/side-menu'
import HeaderBar from './components/header-bar'
import TagsNav from './components/tags-nav'
import User from './components/user'
import ABackTop from './components/a-back-top'
import Fullscreen from './components/fullscreen'
import LookScreen from './components/lockscreen/lockscreen'
import Reminds from './components/reminds/reminds'
import {mapActions, mapGetters} from 'vuex'
import {fullPath} from "@/libs/lazyLoading"
import {
    getDynamicOpenTab,
    getNewTagList,
    getTagNavListFromSessionStorage,
    routeEqual,
    setDynamicOpenTab
} from '@/libs/menu/util'
import wowFinance from '@/assets/svg/WowFinance.svg'
import leftTopMaxLogo from '@/assets/images/cosco/logo.png'
import minLogo from '@/assets/images/cosco/logo-min.png'
import LeaseKeepAlive from '_c/keep-alive/LeaseKeepAlive'
import LeaseIframe from '_c/iframe/iframe'
import {getStore,setStore} from '@/libs/utils/store';
import {validatenull} from '@/libs/utils/validate'
import topMenu from './components/menu/top-menu/top-menu'
import Constants from "@/const/common/Constants";
import {getSysTime} from "_p/basic/api/system-date";
import moment from 'moment'
import config from '@/config'
import { getAmPm } from "../../libs/tools";
export default {
    name: 'Main',
    components: {
        SideMenu,
        topMenu,
        HeaderBar,
        TagsNav,
        Fullscreen,
        LookScreen,
        Reminds,
        User,
        ABackTop,
        LeaseKeepAlive,
        LeaseIframe,
        // ChangePass,
        // AutoOpen
    },
    data() {
        return {
            collapsed: false,
            wowFinance,
            leftTopMaxLogo,
            minLogo,
            isFullscreen: false,
            activeName: '',
            refreshTime: -1,
            ssoRefreshTime:-1,
            userHeadImg: require('@/assets/default-user-head.png'),
            menuTheme: 'light',
            currentTag: {
                name: '',
                params: {},
                query: {},
                meta: {}
            },
            TopNum:0
        }
    },
    computed: {
        ...mapGetters(['expires_in','sso_access_token','refresh_token','access_token','userInfo', 'dynamicTab', 'forceChangePass','lastAccessTime','bookMark']),
        isLeftMenu(){
            return _AFS_PROJECT_CONFIG.menuLocation=='left';
        },
        target(){
            let _target = getStore({name:Constants.afs_login_target_key});
            if(_target){
                return true;
            }
            return false;
        },
        topHeaders(){
            let topHeader  = [];
            if(_AFS_PROJECT_CONFIG.topHeaderComponent){
                let tempArray =_AFS_PROJECT_CONFIG.topHeaderComponent;
                tempArray.forEach(comPath=>{
                    topHeader.push(fullPath(comPath));
                })
            }
            return topHeader;
        },
        isTopMenu(){
            return _AFS_PROJECT_CONFIG.menuLocation=='top';
        },
        // /不同主题下的class
        themeClass(){
            let cls = 'theme_'
            if(_AFS_PROJECT_CONFIG.theme !== undefined) {
                cls += _AFS_PROJECT_CONFIG.theme;
            }
            return cls;
        },
        // 不同定位下的class
        menuLocationClass(){
            let cls = 'menuLocation_';
            if(_AFS_PROJECT_CONFIG.menuLocation !== undefined) {
                cls += _AFS_PROJECT_CONFIG.menuLocation;
            }
            return cls;
        },
        // 不同系统的 class
        systemClass(){
            let cls = 'system_';
            let name = config.getProjectName();
            if(name !== undefined) {
                cls += name;
            }
            return cls;
        },
        username() {
            return this.userInfo.userRealName;
        },
        userId() {
            return this.userInfo.id;
        },
        tagNavList() {
            return this.$store.state.menu.tagNavList
        },
        menuList() {
            return this.$store.getters.menuList
        },
        local() {
            return this.$store.state.menu.local
        },
        hasReadErrorPage() {
            return this.$store.state.menu.hasReadErrorPage
        },
        iframeList() {
            return this.$store.state.menu.tagNavList.filter(item => {
                if (item && item.meta && item.meta.url != null && item.meta.url != '') {
                    return true
                }
                return false;
            })
        },
        key() {
            return this.$route.name;
        },
        waterMarkStyle() {
            return {
                height: 'calc(100% - 100px)',
                width: this.collapsed ? 'calc(100% - 60px)' : 'calc(100% - 220px)',
                position: 'absolute',
                top: '100px',
                left: this.collapsed ? '60px' : '220px',
                zIndex: 99999,
                pointerEvents: 'none'
            }
        },
        waterMark() {
            return {
                text: this.userInfo.username + ' ' +
                    moment().format('YYYY/MM/DD') +
                    getAmPm() +
                    moment().format('hh:mm:ss')
            }

        }
    },
    methods: {
        ...mapActions(['LogOut']),
        backToTop(){
            this.$refs.backTop.back();
        },
        turnToPage(route) {

            let {name, params, query} = {}
            if (typeof route === 'string') {
                name = route
            } else {
                name = route.name
                params = route.params
                query = route.query
            }
            if (this.$route.name != name) {
                this.$router.push({
                    name,
                    params,
                    query
                })
            }
        },
        linkHome(){
            this.$router.push({
                name: "home_index"
            }).catch(err => {err});
        },
        handleCollapsedChange(state) {
            this.collapsed = state
            setStore({name:Constants.collapsed_key,content:state})
        },
        handleCloseTag(res, type, route) {
            if (type !== 'others') {
                if (type === 'all') {
                    setDynamicOpenTab([])
                    this.turnToPage(this.$config.getHomeName())
                } else {
                    if (routeEqual(this.currentTag, route)) {
                        this.$store.commit('closeTag', route);
                    }else{
                        this.$store.commit('closeOtherTag', route);
                    }
                }
            } else {
                setDynamicOpenTab(getDynamicOpenTab().filter(tab => {
                    return route.name === tab.name
                }))
            }
            this.$store.commit('setTagNavList', res)
        },
        handleClick(item) {
            this.turnToPage(item)
        },
        refreshToken() {
            this.refreshTime = setInterval(() => {
                console.log("check token expired")
                const token = getStore({
                    name: 'access_token'
                });
                if(this.lastAccessTime&&(Date.now()-this.lastAccessTime)/1000/60>this.userInfo.autoLogOutTime){
                    clearInterval(this.refreshTime)
                    this.$Message.error({
                        background: true,
                        content: '您已经[ '+this.userInfo.autoLogOutTime+' ]分钟没操作系统，将自动退出',
                        duration: 3,
                        onClose:()=>{
                            this.LogOut().then(() => {
                                this.$store.commit("clearTagNavList");
                                let tenantId = this.$store.getters.tenantId;
                                if (tenantId) {
                                    this.$router.push({
                                        path: '/' + tenantId + '/login'
                                    });
                                } else {
                                    this.$router.push({
                                        name: "login_base"
                                    });
                                }
                            })
                        }
                    })
                    return ;
                }
                if (validatenull(token)) {
                    return;
                }

                if ((this.expires_in - Date.now()) / 1000 <= 300 && !this.refreshLock) {
                    this.refreshLock = true;
                    this.$store
                        .dispatch('RefreshToken')
                        .then((tokenInfo)=>{
                            // this.afs.afsGlobalEmit(this,Constants.tokenEvent,60,tokenInfo)
                        })
                        .catch(() => {
                            //不再超过5s不再刷新
                            if((Date.now()-this.expires_in)>=1000*5){
                                clearInterval(this.refreshTime)
                            }
                        });
                    this.refreshLock = false
                }
            }, 10000);
            if (this.sso_access_token&&this.sso_access_token!=='') {
                this.ssoRefreshTime = setInterval(()=>{
                    this.refreshLock = true;
                    this.$store
                        .dispatch('RefreshOssToken')
                        .catch(() => {
                            clearInterval(this.ssoRefreshTime)
                        });
                    this.refreshLock = false
                }, 1000*60*5)
            }
        },
        setKey(key) {
            //目前不做任何操作
        },
    },
    watch: {
        '$route'(newRoute) {
            const {name, query, params, meta} = newRoute
            this.currentTag.name = name;
            this.currentTag.params = params;
            this.currentTag.query = query;
            this.currentTag.meta = meta;
            if(meta.menusParams){
                this.currentTag.params = Object.assign({},params||{},meta.menusParams)
            }
            this.$store.commit('addTag', {
                route: {name, query, params:this.currentTag.params, meta},
                type: 'push'
            });
            let openedName = newRoute.name, breadCrumbList = undefined;
            if (newRoute.meta.dynamicTab) {
                let dynamicTabs = getDynamicOpenTab();
                dynamicTabs.forEach(tab => {
                    if (tab.meta.name === newRoute.meta.name) {
                        if (!tab.meta.breadCrumbList) {
                            tab.meta.breadCrumbList = this.$store.getters.breadCrumbList
                            tab.meta.openedName = this.activeName;
                            openedName = this.activeName;
                            breadCrumbList = this.$store.getters.breadCrumbList;
                        } else {
                            openedName = tab.meta.openedName
                            breadCrumbList = tab.meta.breadCrumbList
                        }
                    }
                })
                setDynamicOpenTab(dynamicTabs)
            }

            this.activeName = openedName;
            this.$store.commit('setBreadCrumb', {route: newRoute, breadCrumbList: breadCrumbList});
            if(this.$refs.sideMenu){
                this.$refs.sideMenu.updateOpenName(openedName)
            }
            this.$store.commit('setTagNavList', getNewTagList(this.tagNavList, newRoute));
            this.$refs['backTop'].reStoreScroll(name);
        },
        'dynamicTab.uuid'() {
            this.$nextTick(() => {
                let tabIndex = this.dynamicTab.tabIndex;
                const dynamicTabs = getDynamicOpenTab();
                let dRouter = dynamicTabs.filter(tab => {
                    return tabIndex === tab.meta.dIndex;
                })[0]
                if (dRouter) {
                    this.turnToPage({name: dRouter.name, params: dRouter.meta.params, query: {}});
                }
            })

        },
        '$store.state.app.hasLogin': {
            handler(val,old) {
                if(val&&(this.refreshTime===-1||this.ssoRefreshTime===-1)){
                    this.$nextTick(()=> {
                        this.refreshToken();
                    })
                }
                if(!val){
                    if(this.refreshTime!==-1) {
                        clearInterval(this.refreshTime);
                    }
                    if(this.ssoRefreshTime!==-1) {
                        clearInterval(this.ssoRefreshTime);
                    }
                }
            },
            immediate:true
        },
        '$afs.TopNum.TopNum'(){
            debugger
            this.TopNum=this.afs.TopNum.TopNum
        },
        'afs.TopNum.TopNum'(){
            debugger
            this.TopNum=this.afs.TopNum.TopNum
        },
    },

    mounted() {

        /**
         * @description 初始化设置面包屑导航和标签导航
         */
        this.$store.commit('setTagNavList');
        this.$store.commit('setHomeRoute');
        this.$store.commit('updateRef', this.$refs.keepAlive);
        let {name, params, query, meta} = this.$route;
        let activeName = name;

        getTagNavListFromSessionStorage().forEach(tag => {
            if (tag.name === name) {
                name = tag.name;
                params = tag.meta.params;
                query = tag.query;
                meta = tag.meta;
            }
        })
        if (meta.dynamicTab) {
            getDynamicOpenTab().forEach(tag => {
                if (tag.name === name) {
                    meta = tag.meta;
                    activeName = tag.meta.openedName;
                }
            })
        }
        this.currentTag.name = name;
        this.currentTag.params = params;
        this.currentTag.query = query;
        this.currentTag.meta = meta;
        this.activeName = activeName;
        this.$store.commit('addTag', {route: {name, params, query, meta}});
        this.$store.commit('setBreadCrumb', {route: this.$route, breadCrumbList: meta.breadCrumbList});
        this.$store.commit('setLocal', this.$i18n.locale);
        // 设置初始语言
        // 如果当前打开页面不在标签栏中，跳到homeName页
        if (!this.tagNavList.find(item => item.name === this.$route.name)) {
            this.$router.push({
                name: this.$config.getHomeName()
            })
        }
        this.$nextTick(() => {
            let dTabs = getDynamicOpenTab().filter(dynamicTab => {
                return getTagNavListFromSessionStorage().filter(tab => {
                    return tab.name === dynamicTab.name;
                }).length > 0;
            })
            setDynamicOpenTab(dTabs);
        })
        const timer = setInterval(() => {
            this.TopNum=this.afs.TopNum.TopNum;
        },500);

    },
    created() {
        if (!this.userInfo.userRealName || this.userInfo.userRealName == '') {
            this.$store.dispatch('GetUserInfo')
        }
        this.$store.commit("setMainComponent",this);
        getSysTime().then(res => {
            if (res.code === "0000" && res.data) {
                this.time = moment(res.data.bhDate).format('YYYY-MM-DD');
                this.$store.commit('updateBatchDate',this.time);
            }
        })
        let _collapsed = getStore({name:Constants.collapsed_key});
        this.collapsed = !!_collapsed;
    },
    destroyed() {
        clearInterval(this.refreshTime)
    }
}
