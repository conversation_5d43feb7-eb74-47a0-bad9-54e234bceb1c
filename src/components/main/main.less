.main {
  .ivu-layout-header {
    background-color: #fff;
    padding: 0 25px;
  }

  .logo-con {
    padding: 8px;
    background: #fff;
    height: 60px;
    border-bottom: 1px solid #c3c3c3;

    img {
      height: 44px;
    }
  }

  .ivu-layout-header {
    height: 60px;
  }

  .header-con {
    background: #fff;
    padding: 0 0;
    border-bottom: 1px #c3c3c3 solid;
    width: 100%;
  }

  .main-layout-con {
    height: 100%;
    //width: 100%;
    overflow: hidden;
  }

  .main-content-con {
    height: ~"calc(100% - 60px)";
    overflow: hidden;
  }

  .tag-nav-wrapper {
    padding: 0;
    height: 30px;
    background: #F0F0F0;
  }

  .content-wrapper {
    padding: 10px;
    //height: ~"calc(100% - 80px)";
    background: #FCFCFC;
    overflow: auto;
  }

  .left-sider {
    background-color: #fff !important;
  }

  .left-sider /deep/ .ivu-menu-item > i {
    margin-right: 12px !important;
  }

  .left-sider /deep/ .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }

}

// menuLocationClass  不同位置下特定的样式
.menuLocation_left {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .content-wrapper {
    height: calc(100vh - 90px);
  }
}

.menuLocation_top {
  width: 100%;
  height: 100%;
  background: #FCFCFC;

  .LayoutTopMenu {
    background: #FCFCFC;
  }

  .content-box.footer {
    padding: 0 16px;
  }

  .content-box:not(.footer) {
    padding: 8px 16px 0 !important;
  }

  @media screen and (min-width: 1280px) {
    .content-box.footer {
      padding: 0 40px;
    }

    .content-box:not(.footer) {
      padding: 8px 40px 0 !important;
    }
  }

  .header-bar, .tag-nav-wrapper {
    //max-width: 1600px;
    margin: auto;
    //height: 90px;
    padding: 0 15px;
    background-color: #fff;
  }

  .header-con {
    position: fixed;
    top: 0;
    width: 100vw;
    border-bottom-color: transparent;
    background-color: #fff;
    z-index: 1001;
  }

  .header-bar {
    border-bottom: 1px solid #eeeeef;
  }

  .ivu-layout-header {
    //height: auto;
    height: 50px;
    line-height: 25px;
  }

  .header-bar {
    .custom-content-con-left {
      line-height: 50px;
      height: 50px;
    }

    .topMenuBox {
      height: 50px;
    }

    .topSubMenu {
      height: 25px;
      line-height: 25px;
    }
  }


  .head_padding {
    height: 86px;
  }

  .main-content-con {
    height: 100%;
    width: 100%;
  }

  .topMenu {
    //position: absolute;
    //top: 0;
    //left: 230px;
    //height: 45px
  }

  .content-wrapper {
    //overflow: hidden;
    width: 100%;
    height: 100%;
  }

  .topMenuLogo {
    flex: 0 0 auto;
    margin-top: 5px;
    width: 210px;
    height: 45px;

    img {
      max-width: 100%;
      max-height: 100%;
      cursor: pointer;
    }
  }

  .custom-content-con-right {
    height: 48px;
    background-color: #fff;
    padding-right: 0;
    line-height: 48px;

    .full-screen-btn-con i, .lock-screen-btn-con i {
      font-size: 20px !important;
      line-height: 48px;
    }
  }

  .tag-nav-wrapper {
    height: 36px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.04);
  }

  .tags-nav {
    //max-width: 1600px;
    //margin: auto;
    .close-con {
      height: 36px;
    }

    .ivu-dropdown {
      line-height: 36px !important;
    }

    .btn-con {
      height: 34px;
      line-height: 34px;
    }
  }

  .content-wrapper {
    //max-width: 1600px;
    //min-width: 900px;
    //margin: auto;
  }

  .topMenuLogo {
    width: 95px !important;
    height: 40px !important;
  }

  .home-logo-box {
    //position: fixed;
    //left: 50%;
    //bottom: 20px;
    //transform: translateX(-50%);
    //width: 155px;
    padding-top: 20px;
    padding-bottom: 10px;
    //margin-bottom: -4px;
    text-align: center;
    z-index: 1;

    img {
      max-width: 100%;
      height: 36px;
    }
  }

  .search .operation {
    margin-bottom: 2vh;
  }
}

.system_afsContract {
  .ivu-input[disabled],
  fieldset[disabled] .ivu-input,
  .ivu-input-number-input[disabled],
  .ivu-select-disabled .ivu-select-selection {
    cursor: default !important;
    user-select: auto !important;
  }

  .ivu-input[disabled], .ivu-select-input[disabled], .ivu-input-number-input[disabled] {
    color: #515a6e;
    opacity: 1;
  }

  .ivu-radio-disabled .ivu-radio-inner:after {
    background-color: #515a6e;
  }
}

.LayoutTopMenu .content-box {
  min-width: 1200px !important;
  max-width: 1600px !important;
  margin: auto !important;
  //height: 100%;
}


.ivu-tabs-tab .afsDynamicLink{
  color: #515a6e;
}
.ivu-tabs-tab-active .afsDynamicLink{
  color: #2D8cF0;
}
.menu-collapsed-icon{
  margin-right: 5px
}
.headDropdown {
  max-height: 500px;
}


.afs-header-transfer{
  top:0;
}

.collapsed-menu{
  & * {
    color: #515a6e;
  }
  .ivu-menu-item > i{
    margin-right: 12px !important;
  }
  .ivu-menu-submenu > .ivu-menu > .ivu-menu-item > i {
    margin-right: 8px !important;
  }
  max-height: unset !important;
}
.collapsed-drop-down-menu{
  display: block !important;
}

