<template>
    <Layout class="LayoutTopMenu">
        <Header class="header-con">
            <header-bar :collapsed="collapsed" @on-coll-change="handleCollapsedChange">
                <div class="custom-content-con-left" slot="left">
                    <div class="topMenuLogo">
                        <img :src="wow" @click="linkHome"/>
                    </div>
                    <div class="topMenu">
                        <topMenu :icon-size="24" :active-name="activeName" :menu-list="menuList"
                                 @on-select="turnToPage"/>
                    </div>
                </div>
                <div class="custom-content-con-right" slot="right">
                    <user :user-avatar="userHeadImg" :username="username"/>
                    <LookScreen style="margin-right: 10px;"></LookScreen>
                    <fullscreen style="margin-right: 10px;" v-model="isFullscreen"/>
                    <reminds style="margin-right: 10px;"></reminds>
                    <span v-for="(item,index) in topHeaders" :key="index" style="margin-right: 10px;">
                        <component :is="item"></component>
                    </span>
                </div>
            </header-bar>
            <div class="tag-nav-wrapper">
                <tags-nav :list="tagNavList" :value="currentTag" @input="handleClick"
                          @on-close="handleCloseTag"/>
            </div>
        </Header>
        <div class="head_padding"></div>
        <Content>
            <div :style="waterMarkStyle" v-watermark="waterMark">
            </div>
            <div class="content-box">
                <lease-keep-alive :updateComponentsKey="setKey" ref="keepAlive">
                    <router-view :key="key"></router-view>
                </lease-keep-alive>
            </div>
            <div>
                <LeaseIframe
                    :key="item.id"
                    :url="item.meta.url"
                    v-for="item in iframeList"
                    v-show="$route.name === item.name"
                />
            </div>
            <ABackTop v-show="!isTopMenu" :duration="50" ref="backTop" :bottom="80" :height="100" :right="50"
                      :container="isTopMenu?'':'.content-wrapper'"></ABackTop>
            <div class="home-logo-box">
                <img :src="wowFinance">
            </div>
        </Content>
    </Layout>
</template>
<script>
import main from './main'

export default {
    name: 'LayoutTopMenu',
    mixins: [main],
}
</script>
