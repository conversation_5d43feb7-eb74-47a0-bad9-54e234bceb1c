<template>
    <div>
    </div>
</template>
<script>
import {Base64} from 'js-base64'
import {tokenValidate, getAuthorizeUrl} from "@/projects/basic/api/csleasing-sso"
import util from '@/libs/util';
import {getStore, setStore} from "@/libs/utils/store";
import Constants from "@/const/common/Constants";
import {mapActions} from "vuex";

export default {
    name: 'csleasing-sso',
    data() {
        return {
            component: null,
            componentPath: '',
            isFull: false,
            params: null,
            init: false,
            refreshTime: -1,
            refreshLock: false,
            tenantId: null,
        }
    },
    computed: {},
    methods: {
        ...mapActions([
            'updateTenantId'
        ]),
        loginFun() {
            let self = this;
            if (self.$route.query.pageData) {
                let pageDate = JSON.parse(Base64.decode(self.$route.query.pageData, true));
                let params = {};
                for (let key in self.$route.query) {
                    if (key !== "token" && key !== "pageData") {
                        params[key] = self.$route.query[key];
                    }
                }
                let data = {
                    type: Constants.auto_open_type_dlink,
                    token: {},
                    pageData: {
                        params: params,
                        component: pageDate.component,
                        isFull: pageDate.isFull,
                        pageTitle: pageDate.pageTitle,
                        eventHashKey: md5(self.$route.name + this.$store.state.app.sessionKey)
                    }
                };
                setStore({
                    name: Constants.afs_login_target_key,
                    type: Constants.session_store_type,
                    content: data
                });
            }
            const tenantId = self.tenantId || 'system';
            self.$router.replace(`/${tenantId}/login`);
        },
        errorProcess() {
            let self = this;
            this.$Spin.hide();
            const token = getStore({
                name: 'access_token'
            });

            if (self.$store.getters.access_token !== '' || (token && token !== '')) {
                self.loginFun();
            } else {
                this.$Message.error({
                    content: 'token校验失败，即将跳转到登录页',
                    duration: 3,
                    onClose: () => {
                        self.loginFun();
                    }
                })
            }
        },
        ssoAuthorize(state) {
            getAuthorizeUrl(state).then(res => {
                if (res.code === '0000') {
                    window.location = res.data;
                } else {
                    this.errorProcess();
                }
            }).catch(() => {
                this.errorProcess()
            })
        },
        initToken(code) {
            tokenValidate(code).then(res => {
                if (res.code === '0000') {
                    if (res.data.validate && res.data.token) {
                        this.$store.commit('SET_ACCESS_TOKEN', res.data.token.access_token)
                        this.$store.commit('SET_REFRESH_TOKEN', res.data.token.refresh_token)
                        this.$store.commit('SET_EXPIRES_IN', res.data.token.expires_in)
                        this.$store.commit('SET_SSO_ACCESS_TOKEN', res.data.ssoAccessToken)
                        this.$store.commit('SET_SSO_REFRESH_TOKEN', res.data.ssoRefreshToken)
                        this.$store.commit('SET_SSO_EXPIRES_IN', res.data.tokenExpireTime)
                        this.$store.commit('SET_SSO_LOGIN', true)

                        this.$store.dispatch('GetUserInfo').then(() => {
                            this.$Spin.hide();
                            //获取用户信息后进行页面跳转或者组件加载判断
                            if (!this.$route.query.pageData) {
                                console.log("无，pageData参数，进入home页");
                                util.initSystem(this);
                                this.$router.replace("/");
                                return
                            }
                            // 其他情况加载页面数据,跳转到BlankMain组件，
                            let pageDate = JSON.parse(Base64.decode(this.$route.query.pageData, true));
                            let params = {};
                            for (let key in this.$route.query) {
                                if (key !== "token" && key !== "pageDate") {
                                    params[key] = this.$route.query[key];
                                }
                            }
                            const token = {
                                accessToken: res.data.token.access_token,
                                refreshToken: res.data.token.refresh_token,
                                expiresIn: res.data.token.expires_in * 1000 + (Date.now())
                            };
                            let data = {
                                token: token,
                                pageData: {
                                    params: params,
                                    component: pageDate.component,
                                    isFull: pageDate.isFull,
                                    pageTitle: pageDate.pageTitle,
                                    eventHashKey: md5(this.$route.name + this.$store.state.app.sessionKey)
                                }
                            };
                            console.log("aaa7"+Base64.encode(JSON.stringify(data), true));

                            this.$router.replace('/d/afsLink?_link=' + Base64.encode(JSON.stringify(data), true));

                        })
                    } else {
                        this.errorProcess();
                    }
                } else {
                    this.errorProcess();
                }
            }).catch(() => {
                this.errorProcess();
            })
        }
    },
    created() {
        // 参数 token--统一登录平台token，pageData-页面信息JSON 串base64 编码后结果 json 包含，component:组件路径，isFull:是否全路径;tilte-页面title,其他参数--业务组件自己约定，符合uri 链接 a=b&c=d格式
        this.$Spin.show();
        const code = this.$route.query.code;
        this.$store.commit('updateSessionKey', Date.now());
        // code为空，跳转SSO单点登录页面
        if (!code || code === '') {
            const tenantId = this.$route.params.tenantId || 'system';
            this.tenantId = tenantId;
            this.updateTenantId(tenantId);
            this.ssoAuthorize(tenantId);
        } else {
            const tenantId = this.$route.query.state || 'system';
            this.updateTenantId(tenantId);
            this.tenantId = tenantId;
            this.initToken(code);
        }
    },
    mounted() {

    }
}
</script>
