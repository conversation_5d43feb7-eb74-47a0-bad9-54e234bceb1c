const hasPermission = {
    install (Vue, options) {
        Vue.directive('has', {
            bind (el, binding, vnode) {
                let permTypes =Object.keys(vnode.context.$store.getters.permissions)
                if (!permTypes||!permTypes.includes(binding.value)) {
                    if(el.parentNode) {
                        Vue.nextTick(() => el.parentNode.removeChild(el));
                    }else if(el){
                        Vue.nextTick(()=>{
                            el.remove();
                        });
                    }
                }
            }
        });
    }
};

export default hasPermission;
