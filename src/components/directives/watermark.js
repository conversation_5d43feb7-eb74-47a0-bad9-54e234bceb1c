
function addWaterMarker(str,parentNode,font,textColor){
    let can = document.createElement('canvas');
    parentNode.appendChild(can);
    can.width =300;
    can.height = 200;
    can.style.display = 'none';
    let cans = can.getContext('2d');
    cans.rotate(-20 * Math.PI / 180);
    cans.font = font || "14px Microsoft JhengHei";
    cans.fillStyle = textColor || "rgba(180, 180, 180, 0.6)";
    cans.textAlign = 'left';
    cans.textBaseline = 'middle';
    cans.fillText(str, 0, can.height / 2);
    parentNode.style.backgroundImage = "url(" + can.toDataURL("image/png") + ")";
}
const waterMark= {
    install(Vue, options) {
        Vue.directive('watermark', (el,binding)=>{
            addWaterMarker(binding.value.text, el, binding.value.font, binding.value.textColor)
        });
    }
}


export default waterMark
