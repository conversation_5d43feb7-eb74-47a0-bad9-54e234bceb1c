const hasRole = {
    install (Vue, options) {
        Vue.directive('hasRole', {
            bind (el, binding, vnode) {
                let roles = vnode.context.$store.getters.roles;
                if (!roles||!roles.includes(binding.value)) {
                    if(el.parentNode) {
                        Vue.nextTick(() => el.parentNode.removeChild(el));
                    }else if(el){
                        Vue.nextTick(()=>{
                            el.remove();
                        });
                    }
                }
            }
        });
    }
};

export default hasRole;
