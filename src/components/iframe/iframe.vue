<style lang="less">
    @import "iframe.less";
</style>

<template>
    <div ref="iframeWapper" class="iframe" >
        <Row>
            <Col>
                <Card>
                    <Row  >
                        <iframe :src="url" frameborder="0" width="100%" :style="{height:calHeight}" scrolling="auto"></iframe>
                    </Row>
                </Card>
            </Col>
        </Row>
    </div>
</template>

<script>
    export default {
        name: "lease-iframe",
        props:{
          showNative:{
              type:Boolean,
              required:false,
              default:false
          },
            url:{
                type:String,
                required:true
            }
        },
        methods:{

        },
        data() {
            return {
            };
        },
        computed : {
            calHeight(){
                return (window.innerHeight - 100) + 'px';
            }
        }

    };
</script>
