.singleUpload .upload{
    width: 100px;
    height: 100px;
    border: 1px dotted #ccc;
    position: relative;
    margin-bottom: 10px;
}
.singleUpload .upimg{
    //width: 100%;
    //height: 100%;
    //line-height: 30px;
    //text-align: center;
    //border-radius: 5px;
    //position: absolute;
    //top:50%;
    //left: 50%;
    //transform: translate(-50%,-50%);
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #EB9620;
  border-radius: 5px;
  color: #FFFFFF;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

}
.singleUpload .upimg input{
    position: absolute;
    left: 0;
    right:0;
    width: 100%;
    top: 0;
    bottom: 0;
    height: 100%;
    filter: alpha(opacity=0);
    opacity: 0;
    cursor: pointer;
    overflow: hidden;
    z-index: 99;
}
.singleUpload .upimg .ivu-icon-ios-add{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 50px;
    color: #ccc;
}
.singleUpload .showImg{
    width: 80%;
}
.singleUpload .showImg>li{
    width: 100px;
    height: 100px;
    position: relative;
    border: 1px dotted #ccc;
    margin-left: 10px;
    margin-bottom: 10px;
}
.singleUpload .showImg>li img{
    width: 100%;
    height: 100%;
    margin-bottom: 10px;
}
.singleUpload .icon-box {
    width: 100%;
    height: calc(100% + 1px);
    position: absolute;
    top: -1px;
    overflow: hidden;

    &:hover .iconTypeMore {
      &.delImg {
        top: 0;
        right: 0;
      }
    }
  }
  .iconTypeMore {
    position: absolute;
    width: 20px;
    height: 20px;
    margin-right: 0;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    transition: all .2s linear;

    &.delImg {
      top: -15px;
      right: -15px;
      border-radius: 0 0 0 20px;
      background-color: rgba(0, 0, 0, .5);

      i {
        top: -8px;
        left: 2px;
      }

      &:hover {
        color: #EB9620;
      }
    }
    > i {
        position: relative;
        margin-right: 0;
      }
  }
