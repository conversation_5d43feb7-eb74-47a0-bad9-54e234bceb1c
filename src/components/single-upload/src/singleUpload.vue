<style lang="less">
    @import "singleUpload.less";
</style>
<template>
    <div  class="over mt10 singleUpload" style="margin-left: 65px;border: 0.5px solid #e2e2e2;">
        <div class="upload fl" style="    margin-top: 10px;margin-left: 10px;" v-show="diffType!='details'">
            <div class="upimg pr">
                <input type="file" name="file1" @change="uploadImg($event)" ref="uploadInput">
<!--                <Icon type="ios-add" />-->
                上传图片
            </div>
        </div>
        <ul class="showImg fl" style="min-height:98px;    margin-top: 10px;">
            <li v-for="(item,index) in imgurlList"  class="fl icon-box">
                <img  :src="item.imgurl" alt="" width="100" height="100" @click="viewSingleFile(index)"/>
                <!-- <Icon type="ios-trash-outline" style="font-size:20px;cursor: pointer;" @click="deleteImg(index)"/> -->
                <span  class="iconTypeMore delImg" @click.stop="deleteImg(index,item.fileStatus)" v-show="diffType!='details'"><Icon type="ios-close" /></span>
            </li>
        </ul>
        <div v-if="isShowModel">
            <filePreview :showFileList="imgurlList"
            :path="path"
             @close-img-model="closeImgModel"
             :isSomebtn="isSomebtn"
             :currentIndex="currentIndex"
             ></filePreview>
        </div>
    </div>
</template>
<script>
    import {uploadFile, uploadRealFile,getBaseByMD5} from "_p/afs-contract/api/contract/entry/contract-img-upload";
    import filePreview from "../../../projects/basic/pages/image/upload-file/filePreview";
    import pdfImg from "_p/basic/assets/img/pdf.png";
    export default {
        components: {
            filePreview
        },
        name:"",
        data(){
            return {
                imgurlList:[],
                isSomebtn:true,
                path:"apply",
                isShowModel: false,//是否展示model
            }
        },
        props:{
            imgtype:{
                type:String
            },
            diffType:{
                type:String
            }
        },

        methods:{
            uploadImg(event){
                let files = event.target.files;
                let AllUpExt = ".jpg|.bmp|.jpeg|.png|.gif|.pdf|";
                let extName = files[0].name.substring(files[0].name.lastIndexOf(".")).toLowerCase();
                console.log(extName,"extName")
                if(AllUpExt.indexOf(extName + "|") == "-1"){
                    this.$Message.warning("文件格式不正确！");
                    return;
                }

                let size = files[0].size;
                size = size/(1024*1024);
                console.log(size)
                if(size > 5){  // 5M
                    this.$Message.warning("文件过大");
                    return;
                }

                let file = files[0];
                let formData = new FormData();//创建 formdata对象
                formData.append('file',file);
                uploadFile(formData).then(res=>{
                    if (res.code === "0000") {
                       this.uploadFiles(res.data)
                    } else {
                        this.$Message.error("图片转md5失败");
                    }
                });
            },
            uploadFiles(data){
                let params = {
                    "map": data,
                }
                uploadRealFile(params).then(res => {
                    if (res.code === "0000") {
                        let obj={
                            // imgurl:'data:image/jpeg;base64,'+res.data,
                            map:data,
                        }
                        if(data.suffix=='pdf'){
                            obj.imgurl=pdfImg;
                            let url = `${_AFS_PROJECT_CONFIG.apiUri}/${this.path}/upload/getBlob/${data.suffix}/${data.md5}?adScope=approve&token=${this.$store.getters.access_token}`;
                            obj.realurl=url
                        }else{
                            let url = `${_AFS_PROJECT_CONFIG.apiUri}/${this.path}/upload/getBlob/${data.suffix}/${data.md5}?adScope=approve&token=${this.$store.getters.access_token}`;
                            // obj.imgurl='data:image/jpeg;base64,'+res.data;
                            obj.imgurl=url;
                            obj.realurl=url;
                        }
                        this.imgurlList.push(obj);
                        console.log(this.imgurlList,"imgurlList")
                        this.$Message.success("图片上传成功");
                        this.$refs.uploadInput.value="";
                    } else {
                        this.$Message.error("图片上传失败");
                    }
                });
            },
            deleteImg(index,fileStatus){
                if(fileStatus){
                    if(fileStatus=='draft'){
                        this.imgurlList.splice(index,1)
                    }else{
                        this.$Message.warning("只有草稿状态才能删除");
                    }
                }else{
                    this.imgurlList.splice(index,1)
                }
            },
            showCarImg(val){
                let params = {
                    "front":this.imgtype,
                    "busiNo": val,
                }
                getBaseByMD5(params).then(res => {
                    if (res.code === "0000") {
                        if(res.data){
                            if(res.data.front.length>0){
                                let tempUrl=[];
                                res.data.front.forEach((item,index)=>{
                                    let obj={
                                        map:{
                                            fileName:item.fileName,
                                            isFastDFS:"0",
                                            md5:item.fileId,
                                            size:"",
                                            suffix:item.fileType,
                                            type:this.imgtype,
                                        },
                                    }
                                    if(item.fileType=='pdf'){
                                        obj.imgurl=pdfImg;
                                        let url = `${_AFS_PROJECT_CONFIG.apiUri}/${this.path}/upload/getBlob/${item.fileType}/${item.fileId}?adScope=approve&token=${this.$store.getters.access_token}`;
                                        obj.realurl=url;
                                    }else{
                                        let url = `${_AFS_PROJECT_CONFIG.apiUri}/${this.path}/upload/getBlob/${item.fileType}/${item.fileId}?adScope=approve&token=${this.$store.getters.access_token}`;
                                        obj.imgurl=url;
                                        obj.realurl=url;
                                    }
                                    obj.fileStatus=item.fileStatus;
                                    tempUrl.push(obj);
                                })
                                this.imgurlList=tempUrl;
                            }
                        }
                    } else {
                        this.$Message.error("图片获取失败");
                    }
                });
            },
            singleimglist(){
                return this.imgurlList;
            },
            resetimgurl(){
                this.imgurlList=[];
            },
            //单个文件预览
            viewSingleFile(index){
                let filelist = this.collectChecked();
                this.currentIndex = index;
                if (filelist.length > 0) {
                    this.imgurlList = filelist;
                    this.isShowModel = true;
                    this.$emit("open-img-model")
                } else {
                    this.$Message.warning('请选择需要预览的图片');
                }
            },
            collectChecked() {
                let checkedFiles = [];
                for(let i=0;i<this.imgurlList.length;i++){
                    let type = this.imgurlList[i].map.suffix;
                    checkedFiles.push(Object.assign({},this.imgurlList[i],{thumSrc:this.imgurlList[i].imgurl,imgSrc:this.imgurlList[i].realurl,type,imageFlow:'',fileType:type}));
                }
                return checkedFiles;
            },
            closeImgModel(value) {
                this.isShowModel = value;
            },

        }
    }
</script>
