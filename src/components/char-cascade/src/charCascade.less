/* 字母联动 */
.charCascade .ivu-modal-footer{
   border-top: none;
}
 .cascadeModel .ivu-modal{
   width: auto  !important;
   max-width: 68%;
}
.cascadeModel .ivu-select-dropdown{
   // max-height: 530px;
}
.pop-wrapper.pop-two--series{
    min-width: 600px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow:0 8px 20px 0 rgba(17,30,54,.09);
    position: relative;
    font-size: 12px;
}
 .pop-wrapper .brand{
    position: relative;
    width: 200px;
    box-sizing: border-box;
    border-radius: 4px;
    list-style: none;
    min-height: 530px;
}
 .pop-wrapper .brand>.index{
    float: left;
    width: 30px;
    height: 100%;
    line-height: 20px;
    text-align: center;
    color: #fff;
    background-color: #111e36;
    border-radius: 4px 0 0 4px;
    display: flex;
    padding: 5px;
    box-sizing: border-box;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column;
    -webkit-box-pack: justify;
    justify-content: space-between;
}
.pop-wrapper .brand>.index li{
    cursor: pointer;
}
.pop-wrapper .brand>.index li:hover{
    background-color:#05f;
}
 .pop-wrapper .brand>.index li.active{
    color: #fff;
    border-radius: 4px;
    background-color: #05f;
 }
 .pop-wrapper .brand>.brandList{
    max-height: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 30px;
    border-radius: 0 4px 4px 0;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;
 }
 .pop-wrapper .brand>.brandList .list__box{
    color: #111e36;
    font-weight: 700;
    line-height: 30px;
    padding: 0 12px;
    background-color: #f1f3f6;
    display: block;
 }

 .pop-wrapper .brand>.brandList p{
    position: relative;
    line-height: 30px;
    padding: 0 12px;
    cursor: default;
    white-space: nowrap;
 }
 .pop-wrapper .brand>.brandList p.active{
    color: #05f;
    font-weight: 700;
    background-color: #f8f9fc;
 }
 .pop-wrapper .brand>.brandList p:hover{
    background-color:  #f8f9fc;
 }
 .pop-wrapper .brand .brandList p::after{
    content: "\e662";
    color: #c5cad4;
    font-family: "iconfont" !important;
    font-size: 20px;
    font-weight: 400;
    position: absolute;
    top: 0;
    right: 12px;
}
.pop-wrapper .brand .brandList p.active::after,
.pop-wrapper .series p.active::after{
    color: #05f;
}
 .pop-wrapper .series,
 .pop-wrapper .style{
    position: absolute;
    top: 0;
    box-sizing: border-box;
    border-radius: 4px;
    left: 200px;
    bottom: 0;
 }
 .pop-wrapper .series{
    width: 200px
 }
 .pop-wrapper .style{
    left: 400px;
    min-width: calc(100% - 400px);
 }
 .pop-wrapper .series>.seriesList,
 .pop-wrapper .style>.styleList{
    max-height: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    border-radius: 0 4px 4px 0;
    border-left: 1px solid #f1f3f6;
    overflow-x: hidden;
    overflow-y: auto;
    background-color: #fff;
 }
 .pop-wrapper .series p
 {
    white-space: normal;
    line-height: 22px;
    padding: 4px 12px 4px;
    cursor: default;
    position: relative;
 }
 .pop-wrapper .style p{
   white-space: normal;
   line-height: 22px;
   padding: 4px 80px 4px 12px;
   cursor: default;
   position: relative;
 }
 .pop-wrapper .series p::after{
    content: "\e662";
    color: #c5cad4;
    font-family: "iconfont" !important;
    font-size: 20px;
    font-weight: 400;
    position: absolute;
    right: 12px;
 }
 .pop-wrapper .series p:hover,
 .pop-wrapper .style p:hover
 {
    background-color:  #f8f9fc;
 }
 .pop-wrapper .series p.active,
 .pop-wrapper .style p.active
 {
    color: #05f;
    font-weight: 700;
    background-color: #f8f9fc;
 }
 .pop-wrapper .style p>span{
   position: absolute;
   right: 0;
   padding: 0 10px 0 5px;
   color: #f60;
   top: 50%;
   transform: translateY(-50%);
 }
