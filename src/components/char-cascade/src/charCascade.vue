<style lang="less">
    @import "charCascade.less";
</style>
<template>
    <div class="charCascade">
        <Modal  class-name="cascadeModel" v-model="formModel" :open-model="formModel=true" @on-cancel="cancel" :footer-hide="true">
            <div>
                <Form ref="vehicleDataForm" :model="vehicleDataForm" :rules="vehicleDataFormValidate">
                    <Row>
                        <Col span="10">
                            <FormItem label="车型" prop="searchModel">
                                <Select
                                v-model="vehicleDataForm.searchModel"
                                placeholder="请输入搜索"
                                filterable
                                remote
                                :remote-method="remoteModel"
                                :loading="loadModel"
                                @on-change="searchSelectModel"
                                >
                                   <Option :value="item.modelId" v-for="(item,index) in searchModelList" :key="index">{{item.modelName}}</Option>
                               </Select>
                            </FormItem>
                        </Col>
                        <Col span="10">
                            <FormItem label="款式" prop="searchStyle">
                                <Select
                                v-model="vehicleDataForm.searchStyle"
                                placeholder=""
                                filterable
                                clearable
                                remote
                                :remote-method="remoteStyle"
                                :loading="loadStyle"
                                @on-change="searchSelectStyle"
                                >
                                   <Option :value="item.styleId" v-for="(item,index) in searchStyleList" :key="index">{{item.styleName}}</Option>
                               </Select>
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
                <div class="pop-wrapper pop-two pop-two--series">
                    <div class="brand">
                        <ol class="index">
                            <li v-for="(item,index) in lettersList" :data-target="'jump-'+item.letter" :class="{active:curLetter==item.letter}" @click="toggle($event,item.letter)">{{item.letter}}</li>
                        </ol>
                        <ul class="brandList" @scroll="scrollList">
                            <li v-for="(item,index) in brandList">
                                <span class="list__box" :id="'jump-'+item.letter">{{item.letter}}</span>
                                <p v-for="(itemChild,index) in item.charBrandList" :data-value="itemChild.brandId" :data-text="itemChild.brandName" data-target="brand" :data-indeterminate="itemChild.indeterminate" @click="selectBrand($event)" :class="{active:curBrandId==itemChild.brandId}">{{itemChild.brandName}}</p>
                            </li>
                        </ul>
                    </div>
                    <div class="series" v-if="isShowSeries">
                        <ul class="seriesList">
                            <li v-for="(item,index) in modelList">
                                <p :data-value="item.modelId" data-target="series" :id="'series-'+item.modelId" :data-text="item.modelName" @click="selectModel($event)" :class="{active:curModelId==item.modelId}">{{item.modelName}}</p>
                            </li>
                        </ul>
                    </div>
                    <div class="style" v-if="isShowStyle">
                        <ul class="styleList">
                            <li v-for="(item,index) in stylelList">
                                <p :data-value="item.styleId" data-target="style" :data-text="item.styleName" :id="'series-'+item.styleId"  @click="selectStyle($event)" :data-price="item.guidePrice" :data-carLength="item.carLength" :data-wight="item.wight"  :data-wheelBase="item.wheelBase"  :data-modelId="item.modelId" :class="{active:curStyleId==item.styleId}" >{{item.styleName}}<span>{{item.guidePrice}}元</span></p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </Modal>
    </div>
</template>
<script>
    import {getBrand,getModel,getStyle,getNewBrand,getNewModel,getNewStyle,getStyleByCode,getModelByCode,getBrandByCode} from '_p/basic/api/common/common'
    import * as utils from "@/projects/basic/assets/js/utils.js"
    import {deepClone} from "@/libs/utils/ObjectClone";
    export default {
        name:"",
        data(){
            return{
                    modelInfo:{},
                    brandInfo: {},
                    styleInfo:{},
                    indeterminate:"",
                    formModel: false,
                    loadModel:false,
                    loadStyle:false,
                    searchStyle:false,
                    searchModelList:[],
                    searchStyleList:[],
                    searchModel:"",
                    // 字母，品牌，车型，款式选中的字段
                    curLetter:"A",
                    curBrandId:"",
                    curModelId:"",
                    curStyleId:"",
                    // 保存品牌,车型，款式，价格的名称
                    curBrandName:"",
                    curModelName:"",
                    curStyleName:"",
                    curGuidePrice:"",
                    carLength:"", // 车长
                    wight:"", // 车重
                    wheelBase:"", //轴距
                    modelId:"", // 唯一值

                    //  车型跟款式初始为隐藏
                    isShowSeries:false,
                    isShowStyle:false,
                    // 弹窗的表单form
                    vehicleDataForm:{
                        brandModel:"",
                        style:"",
                    },
                    vehicleDataFormValidate:{},
                    // 首字母列表字段
                    lettersList:[
                    ],
                    // 品牌列表字段
                    brandList:[
                    ],
                    // 车型列表字段
                    modelList:[
                    ],
                    stylelList:[],
                    // isDisabled:true,
                }
        },
        props:{
            modelLibType:{
                type:String
            },
            businessTypeForm:{
                type:Object
            }
        },
        watch: {

        },
        created(){
            this.getBrandData();
        },
        mounted(){

        },
        methods:{
            // 远程搜索车型
            remoteModel(value){
                // this.loadModel=true;
                let params="";
                params={
                    level:2,
                    modelName:value
                }
                if(this.modelLibType=="afsApply"){
                    params.businessType=this.businessTypeForm.businessType;
                    params.carType=this.businessTypeForm.carType;
                    params.carNature=this.businessTypeForm.carNature;
                    params.operateWay=this.businessTypeForm.operateWay;
                    params.affiliatedWay=this.businessTypeForm.affiliatedWay;
                    params.carPurpose=this.businessTypeForm.carPurpose;
                    params.dealerId=this.businessTypeForm.dealerId;
                    getNewModel(params).then(res=>{
                        this.dealBrandSearchRes(res)
                    })
                    // getNewSearchModel(params).then(res=>{
                    //     this.dealBrandSearchRes(res)
                    // })
                }else{
                    getModel(params).then(res=>{
                        this.dealBrandSearchRes(res)
                    })
                }
            },
            dealBrandSearchRes(res){
                if(res.code=="0000"){
                    //this.searchModelList=searchModelList;
                    this.searchModelList=res.data;
                    // console.log(this.searchModelList,"modelist")
                }
            },
            searchSelectModel(value){
                // 车型选中款式的搜索框放开
                // this.isDisabled=false;
                console.log(value,"value");
                console.log(this.searchModelList,"searchModelList")
                this.searchModelList.forEach(item=>{
                    if(item.modelId==value){
                        this.curBrandId=item.brandId;
                        this.curLetter=item.firstChar;
                        this.curBrandName=item.brandName;
                        this.indeterminate=item.indeterminate||'';
                        let getBrandHeightEl=document.getElementById("jump-"+this.curLetter);
                        console.log(getBrandHeightEl,"getBrandHeightEl");
                        // 选中品牌字母滑动的最顶部
                        this.carScrollTop(getBrandHeightEl,"brandList");
                        // 查询车型列表
                        this.curModelId=value;
                        this.curModelName=item.modelName;
                        // 查询款式列表
                        this.queryModelList(this.curBrandId).then((data)=>{
                            if(data){
                                console.log("series-"+this.curModelId)
                                let getModelHeightEl=document.getElementById("series-"+this.curModelId);
                                console.log(getModelHeightEl,"getModelHeightEl")
                                // 选中车型滑动到最顶部
                                this.carScrollTop(getModelHeightEl,"seriesList");
                                this.queryStyleList(this.curBrandId,this.curModelId)
                            }
                        })
                    }
                })
            },
            getBrandData(){
                // 初始化字母列表
                this.lettersList=[];
                let params="";
                params={
                    level:'1',
                    brandName:this.vehicleDataForm.brandModel,
                }
                if(this.modelLibType=="afsApply"){
                    params.businessType=this.businessTypeForm.businessType;
                    params.carType=this.businessTypeForm.carType;
                    params.carNature=this.businessTypeForm.carNature;
                    params.operateWay=this.businessTypeForm.operateWay;
                    params.affiliatedWay=this.businessTypeForm.affiliatedWay;
                    params.carPurpose=this.businessTypeForm.carPurpose;
                    params.dealerId=this.businessTypeForm.dealerId;
                    getNewBrand(params).then(res=>{
                        this.dealBrandRes(res)
                    })
                }else{
                    getBrand(params).then(res=>{
                        this.dealBrandRes(res)
                    })
                }
            },
             // 处理品牌返回数据
            dealBrandRes(res){
                if(res.code=="0000"){
                    //let lists=brandList;
                    let lists=res.data;
                    let tempArr=[];
                    // 构造首字母lsit
                    lists.forEach(item => {
                        tempArr.push(item.firstChar);
                    });
                    let uniqueArr=utils.unique(tempArr);
                    uniqueArr.forEach(item=>{
                        let obj={
                            letter:item,
                        }
                        this.lettersList.push(obj)
                    })
                    // 构造品牌的list
                    let copyLettresList=deepClone(this.lettersList)
                    for(let i=0;i<copyLettresList.length;i++){
                        copyLettresList[i].charBrandList=[];
                        lists.forEach(item=>{
                            let obj={
                                brandId:item.brandId,
                                brandName:item.brandName,
                                indeterminate:item.indeterminate
                            }
                            if(copyLettresList[i].letter==item.firstChar){
                                copyLettresList[i].charBrandList.push(obj)
                            }
                        })
                    }
                    this.brandList=copyLettresList;
                }
            },
            cancel(){
                this.$emit("close-model",false);
            },
            // 选中品牌
            selectBrand(event){
                // 重新选中品牌时,款式初始化
                if(this.isShowStyle){
                    this.isShowStyle=false;
                    this.stylelList=[];
                }
                let curEl=event.currentTarget;
                this.curBrandId=curEl.getAttribute("data-value");
                this.curBrandName=curEl.getAttribute('data-text');
                this.indeterminate=curEl.getAttribute('data-indeterminate');
                // console.log(this.indeterminate,"indeterminate")
                this.queryModelList(this.curBrandId)
            },
            // 查询车型列表
            queryModelList(curBrandId){
                this.isShowSeries=true;
                let params="";
                params={
                    level:2,
                    brandId:curBrandId,
                }
                if(this.modelLibType=="afsApply"){
                    params.indeterminate=this.indeterminate;
                    params.businessType=this.businessTypeForm.businessType;
                    params.carType=this.businessTypeForm.carType;
                    params.carNature=this.businessTypeForm.carNature;
                    params.operateWay=this.businessTypeForm.operateWay;
                    params.affiliatedWay=this.businessTypeForm.affiliatedWay;
                    params.carPurpose=this.businessTypeForm.carPurpose;
                    params.dealerId=this.businessTypeForm.dealerId;
                    return new Promise((resolve,reject)=>{
                        getNewModel(params).then(res=>{
                            if(res.code=="0000"){
                                //this.modelList=modelList;
                                this.modelList=res.data;
                                resolve(res.data)
                            }else{
                                reject(res.message)
                            }
                        })
                    })
                }else{
                    return new Promise((resolve,reject)=>{
                        getModel(params).then(res=>{
                            if(res.code=="0000"){
                                //this.modelList=modelList;
                                this.modelList=res.data;
                                resolve(res.data)
                            }else{
                                reject(res.message)
                            }
                        })
                    })
                }
            },
            // 选中车型
            selectModel(event){
                let curEl=event.currentTarget;
                this.curModelId=curEl.getAttribute("data-value");
                this.curModelName=curEl.getAttribute('data-text')
                console.log(this.curModelId,this.curModelName)
                this.queryStyleList(this.curBrandId,this.curModelId);
                // this.isDisabled=false;

            },
            // 搜索款式
            remoteStyle(value){
                // this.loadModel=true;
                if(value.length<2){
                    return;
                }
                let params="";
                params={
                    level:3,
                    brandId:this.curBrandId,
                    modelId:this.curModelId,
                    styleName:value
                }
                console.log("模糊搜索"+this.modelLibType)
                if(this.modelLibType=="afsApply"){
                    params.businessType=this.businessTypeForm.businessType;
                    params.carType=this.businessTypeForm.carType;
                    params.carNature=this.businessTypeForm.carNature;
                    params.operateWay=this.businessTypeForm.operateWay;
                    params.affiliatedWay=this.businessTypeForm.affiliatedWay;
                    params.carPurpose=this.businessTypeForm.carPurpose;
                    params.dealerId=this.businessTypeForm.dealerId;
                    getNewStyle(params).then(res=>{
                        if(res.code=="0000"){
                            this.searchStyleList=res.data;
                        }
                    })
                }else{
                    getStyle(params).then(res=>{
                        if(res.code=="0000"){
                            this.searchStyleList=res.data;
                        }
                    })
                }
            },

            searchSelectStyle(value){
                console.log("点击模糊搜索"+value)
                this.curStyleId=value
                this.searchStyleList.forEach(item=>{
                    if(item.styleId==value){
                        console.log(item)
                        //查询款式信息
                        let params={
                            code :item.modelId
                        }
                        this.getStyleByCode(params);
                        let getModelHeightEl=document.getElementById("series-"+this.curStyleId);
                        // 选中款式滑动到最顶部
                        this.carScrollTop(getModelHeightEl,"styleList");
                    }
                })
            },

            getStyleByCode(v){
                getStyleByCode(v).then(res=>{
                    if(res.code==='0000'&&res.data){
                       this.styleInfo=res.data;
                        //查询车型信息
                        let modelParams={
                            code:this.styleInfo.modelCode,
                        }
                        this.getModelByCode(modelParams)
                    }else {
                        this.$Message.error("不可选择此车型");
                    }
                });
            },

            getModelByCode(v){
                getModelByCode(v).then(res=>{
                    if(res.code==='0000'&&res.data){
                        this.modelInfo=res.data;
                        console.log("赋值modelInfo"+JSON.stringify(this.modelInfo))
                        //查询品牌信息
                        let brandParams={
                            code:this.modelInfo.brandCode,
                        }
                        this.getBrandByCode(brandParams);
                    }else {
                        this.$Message.error("不可选择此车型");
                    }
                });
            },

            getBrandByCode(v){
              getBrandByCode(v).then(res=>{
                  if(res.code==='0000'&&res.data){
                    this.brandInfo=res.data;
                    if(this.modelLibType=="afsApply"){
                        let params="";
                      params={
                          level:1,
                          brandName:this.brandInfo.name,
                          code:this.brandInfo.code
                      }
                          params.businessType = this.businessTypeForm.businessType;
                          params.carType = this.businessTypeForm.carType;
                          params.carNature = this.businessTypeForm.carNature;
                          params.operateWay = this.businessTypeForm.operateWay;
                          params.affiliatedWay = this.businessTypeForm.affiliatedWay;
                          params.carPurpose = this.businessTypeForm.carPurpose;
                          params.dealerId = this.businessTypeForm.dealerId;
                          getNewBrand(params).then(res => {
                              if(res.code==='0000'){
                                  if(res.data.length>0){
                                      let passObj={
                                          curBrandId:this.modelInfo.brandCode,
                                          curModelId:this.styleInfo.modelCode,
                                          curStyleId:this.styleInfo.code,
                                          curBrandName:this.brandInfo.name,
                                          curModelName:this.modelInfo.models,
                                          curStyleName:this.styleInfo.name,
                                          curGuidePrice:this.styleInfo.guidePrice.toString(),
                                      }
                                      console.log(passObj,"passObj");
                                      this.$emit('carinfo',passObj)
                                      this.cancel();
                                  }else {
                                      this.$Message.error("不可选择此车型");
                                  }
                              }
                          })
                    }else{
                        console.log("不是进件")
                        console.log("品牌"+JSON.stringify(this.brandInfo))
                        console.log("车型"+JSON.stringify(this.modelInfo))
                        console.log("款式"+JSON.stringify(this.styleInfo))
                                    let passObj={
                                          curBrandId:this.modelInfo.brandCode,
                                          curModelId:this.styleInfo.modelCode,
                                          curStyleId:this.styleInfo.code,
                                          curBrandName:this.brandInfo.name,
                                          curModelName:this.modelInfo.models,
                                          curStyleName:this.styleInfo.name,
                                          curGuidePrice:this.styleInfo.guidePrice.toString(),
                                          carLength:this.styleInfo.carLength,
                                          wight : this.styleInfo.wight,
                                          wheelBase : this.styleInfo.wheelBase,
                                          carUniqueId : this.styleInfo.modelId,
                                      }
                                      console.log(passObj,"passObj");
                                      this.$emit('carinfo',passObj)
                                      this.cancel();

                    }

                  }else {
                      this.$Message.error("不可选择此车型");
                  }
              });
            },

            // 查询款式列表
            queryStyleList(curBrandId,curModelId){
                this.isShowStyle=true;
                let params="";
                params={
                    level:3,
                    brandId:curBrandId,
                    modelId:curModelId
                }
                if(this.modelLibType=="afsApply"){
                    params.businessType=this.businessTypeForm.businessType;
                    params.carType=this.businessTypeForm.carType;
                    params.carNature=this.businessTypeForm.carNature;
                    params.operateWay=this.businessTypeForm.operateWay;
                    params.affiliatedWay=this.businessTypeForm.affiliatedWay;
                    params.carPurpose=this.businessTypeForm.carPurpose;
                    params.dealerId=this.businessTypeForm.dealerId;
                    getNewStyle(params).then(res=>{
                        if(res.code=="0000"){
                            //this.stylelList=stylelList;
                            this.stylelList=res.data;
                            // console.log(this.modelList,"modelist")
                            this.$nextTick(function(){
                                let scorllEl=document.getElementsByClassName('styleList')[0];
                                scorllEl.scrollTop=0;
                            })
                        }
                    })
                }else{
                    getStyle(params).then(res=>{
                        if(res.code=="0000"){
                            //this.stylelList=stylelList;
                            this.stylelList=res.data;
                            // console.log(this.modelList,"modelist")
                            this.$nextTick(function(){
                                let scorllEl=document.getElementsByClassName('styleList')[0];
                                scorllEl.scrollTop=0;
                            })
                        }
                    })
                }
            },
            selectStyle(event){
                let curEl=event.currentTarget;
                this.curStyleId=curEl.getAttribute("data-value");
                this.curStyleName=curEl.getAttribute('data-text');
                this.curGuidePrice=curEl.getAttribute('data-price');
                this.carLength=curEl.getAttribute('data-carLength');
                this.wight=curEl.getAttribute('data-wight');
                this.wheelBase=curEl.getAttribute('data-wheelBase');
                this.modelId = curEl.getAttribute('data-modelId');
                let passObj={
                    curBrandId:this.curBrandId,
                    curModelId:this.curModelId,
                    curStyleId:this.curStyleId,
                    curBrandName:this.curBrandName,
                    curModelName:this.curModelName,
                    curStyleName:this.curStyleName,
                    curGuidePrice:this.curGuidePrice,
                    carLength:this.carLength,
                    wight:this.wight,
                    wheelBase:this.wheelBase,
                    carUniqueId: this.modelId,
                }
                console.log(passObj,"passObj");
                 this.$emit('carinfo',passObj)
                this.cancel();
            },
            // 滚动品牌滚动条与侧边栏对应
            scrollList(){
                let scorllEl=document.getElementsByClassName('brandList')[0];
                let scrollTop=scorllEl.scrollTop;
                let els=document.getElementsByClassName('list__box');
                let heightArr=new Array();
                for(let i=0;i<els.length;i++){
                    heightArr.push(els[i].offsetTop);
                }
                for(let i=0;i<els.length;i++){
                    if(heightArr[i+1]>scrollTop&&heightArr[i]<=scrollTop){
                        // console.log(els[i])
                        let activeletter=els[i].getAttribute("id");
                        let index=activeletter.indexOf('-');
                        let brandId=activeletter.substring(index+1);
                        this.curLetter=brandId;
                    }
                }

            },
            // 点击侧边栏字母与品牌中字母对应
            toggle(event,brandId){
                this.curLetter=brandId;
                let el=event.currentTarget;
                let dataTarget=el.getAttribute("data-target");
                let getHeightEl=document.getElementById(dataTarget);
                this.carScrollTop(getHeightEl,"brandList")
            },
            carScrollTop(getHeightEl,type){
                if(getHeightEl){
                    let scorllHeight=getHeightEl.offsetTop;
                    let scorllEl=document.getElementsByClassName(type)[0];
                    scorllEl.scrollTop=scorllHeight;
                }
            }
        }
    }
</script>
