import {deepClone} from "@/libs/utils/ObjectClone";
import dateUtil from '_c/iview/utils/date';
export default {
    data() {
        return {
            dicKeysMap: {},
            allTypes: [
                'text', 'password', 'textarea', 'url', 'email', 'mobile',//原始
                'radio', 'checkbox', 'date', 'dateRange', 'datetime', 'datetimeRange',
                'time', 'timeRange',//原始
                'address', // 地址组件
                'singleSelect', 'multipleSelect', 'number', 'currency',//自己添加
            ],
            textTypes: ['text', 'password', 'textarea', 'url', 'email', 'mobile'],
            arrayTypes:['checkbox','dateRange','datetimeRange','timeRange','multipleSelect'],
            numberTypes:['number','currency'],
            refComponent:null,
        }
    },
    inject: ['RuiPage'],
    methods: {
        clone(data) {
            return deepClone(data);
        },
        prepareDicInfo(item){
            let self = this;
            //数据字典处理
            item.globalDic = false;
            //字典key配置字典值没有则取全局字典
            //字典key配置但给出字典值，则使用配置字典值
            // 其他情况取配置字典
            if(item.dicKey&&item.dicKey!=''&&(!item.dicData||!item.dicData.length)){
                //取全局字典
                if(!self.dicKeysMap[item.dicKey]){
                    self.dicKeysMap[item.dicKey] = [];
                }
                self.dicKeysMap[item.dicKey].push(item.key);
                item.globalDic = true;
            }
            //设置空字典值
            if(!item.dicData){
                item.dicData = [];
            }
        },
        _getDicData(item){
            if(item) {
                if(item.globalDic){
                    return this.getDicByType(item.dicKey);
                }
                return item.dicData;
            }else{
                return [];
            }
        },
        getDicByType(type){
            return this.RuiPage.dataDic[type]?this.RuiPage.dataDic[type]:[];
        },
        parseSimpleStr(item,value){
            if(item._type==='number'||item._type==='currency'){
                if (isNaN(value) || value == null) {
                    return '';
                }
            }else if(isNaN(value)&&(!value||value===''||value.length===0)){
                return '';
            }
            if(item._type==='currency'){
                return '￥'+this.currencyFormat(value,item.scale);
            }else if(item._type==='radio'||item._type==='checkbox'||
                item._type==='singleSelect'||item._type==='multipleSelect'){
                let dics = this._getDicData(item);
                if(dics&&dics.length>0){
                    let rtn =[];
                    dics.forEach(dic=>{
                        if(value instanceof Array){
                            value.forEach(val => {
                                if (val === dic.value) {
                                    rtn.push(dic.title);
                                }
                            })
                        }else {
                            if (value === dic.value) {
                                rtn.push(dic.title);
                            }
                        }
                    })
                    return rtn.join(',');
                }else{
                    return value.join(',')
                }
            }else if(item._type==='dateRange'||item._type==='datetimeRange'){
                if(value&&Array.isArray(value)){
                    let temp = [];
                    if(value[0]&&value[0] instanceof Date){
                        temp.push(dateUtil.format(value[0],item.format))
                    }else if(value[0]&&value[0]!=''){
                        temp.push(value[0])
                    }
                    if(value[1]&&value[1] instanceof Date){
                        temp.push(dateUtil.format(value[1],item.format))
                    }else if(value[1]&&value[1]!=''){
                        temp.push(value[1])
                    }
                    return  temp.length==2?temp.join(' 至 '):'';
                }else{
                    return '';
                }
            }else if(item._type==='date'||item._type==='datetime'){
                if(value&&value instanceof Date){
                    return dateUtil.format(value,item.format);
                }else{
                    return value;
                }
            }else if(item._type==='timeRange'){
                return value.join(' 至 ');
            }else if(item._type==='number'){
                return this.numberFormat(value,item.scale);
            }
            return value;
        },
        setRefComponent(ref){
            this.refComponent = ref;
        }
    },
    computed:{

    }
}
