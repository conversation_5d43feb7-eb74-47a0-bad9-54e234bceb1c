export default {
    data() {
        return {
            dependDefine:[],
            defineFinished:false,
        }
    },
    inject: ['RuiPage'],
    methods: {

    },
    computed:{
        _defineIdsFinished(){
            return this.defineFinished;
        }
    },
    watch:{
        'RuiPage.finishedDefine':{
            immediate: true,
            handler() {
                let self = this;
                if(self.RuiPage.finishedDefine.length==0)
                    return;
                let flag = self.RuiPage.finishedDefine.filter(id=>{
                    return self.dependDefine.includes(id);
                }).length==self.dependDefine.length;
                if(flag&&!this.defineFinished){
                    self.init();
                    self.defineFinished = true;
                }
            }
        }
    },
    created(){
        this.dependDefine.push(...this.calDependIds())
    }
}
