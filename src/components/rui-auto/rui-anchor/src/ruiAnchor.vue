<!--
    ruiAnchor 锚点
    props: List<Map>[{id: DOM ID ; title: 锚点标题 ; hide: 是否隐藏 true 是}]
    例： [
            {id:'loanAnalyze',title: '1111', hide: true},
        ]
-->
<template>
    <div class="rui-anchor">
        <template v-for="(item,index) in list">
            <!--            <Tooltip v-show="item.hide" :key="index" :content="item.title" placement="top">-->
            <div
                v-show="!item.hide"
                :key="index"
                :title="item.title"
                @click="() =>onClickAnchor(item.id,index)"
                class="ivu-anchor-link-title notwrap cursor_pointer"
            >

                <div v-if="currentIndex==index" class="hong_select">
                    {{ item.title }}
                </div>
                <div v-else class="hong_normal">
                    {{ item.title }}
                </div>
            </div>
            <!--            </Tooltip>-->
        </template>
    </div>
</template>
<script>

export default {
    name: 'ruiAnchor',
    props: {
        list: {
            validator: function (value) {
                return Array.isArray(value) && value.every(e => !!e.id)
            }
        }
    },
    data() {
        return {
            currentIndex: 0
        }
    },
    methods: {
        onClickAnchor(id,index) {
            console.log('id-----', id);
            this.currentIndex = index;
            if (id) {
                document.querySelector("#" + id).scrollIntoView({behavior: "smooth", block: "center"});
            }
        },
    }

}
</script>
<style lang="less">
.rui-anchor {
    padding: 0;
}

.cursor_pointer {
    cursor: pointer
}
.hong_normal{
    color: #000000;
    text-align: center;
}
.hong_select{
    background-color: #2d8cf0;
    color: #FFFFFF;
    text-align: center;
}
</style>
