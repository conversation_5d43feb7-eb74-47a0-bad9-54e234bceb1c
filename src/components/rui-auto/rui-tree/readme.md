## 属性

|  参数  | 说明  | 类型  |  默认值  |
|  ----  | ----  | ---  | ---  |
| setting  | ztree 配置 | Object |  `{view: {showIcon: false}}`  |
| nodes  | ztree 数据 | Array |  `[]`  |

## 事件

完全移植[zTree API](http://www.treejs.cn/v3/api.php)中`callback`支持的事件，除了：

- 不支持所有 `before` 开头的事件。可以通过`setting.callback.beforeXXX`自行配置
- 不支持 `onNodeCreated` 事件。，如果需要可以通过 `setting.callback.onNodeCreated` 自行传入
- 增加 `onCreated` 事件。每此实例初始化完成时触发，回调参数接收ztree实例，通过ztree实例可以使用所有实例方法

|  参数  | 说明  | 
|  ----  | ----  |
| onAsyncError  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onAsyncSuccess  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onCheck  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onClick  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onCollapse  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onDblClick  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onDrag  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onDragMove  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onDrop  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onExpand  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onMouseDown  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onMouseUp  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onRemove  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onRename  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onRightClick  | 参考 [zTree API](http://www.treejs.cn/v3/api.php) | 
| onCreated  | 初始化渲染完成后触发，回调参数接收ztree实例 | 

## 扩展

zTree没有提供给整个实例更新数据的方法，基于Vue的组件通信机制扩展实现了响应式数据，只要`nodes`属性的值发生变化，ztree实例就会随之更新。

