import language from '../language'
import {getListByLevel} from '@/projects/basic/api/admin/location'

import {
    GROUP,
    PROVINCE_LEVEL, CITY_LEVEL, AREA_LEVEL, TOWN_LEVEL, ROAD_LEVEL, DETAIL_LEVEL,
    LEVEL_LIST, LEVELS
} from '../constants'

import {getRegionByModel, validModel, getLoader, queryTown} from '../helper'

export default {
    methods: {
        modelChange(val) {
            if (validModel(val) && this.differentModel(val)) {
                this.regionArr = val
                this.clearRegion(PROVINCE_LEVEL)
                // console.log(val[TOWN_LEVEL])
                if (val[TOWN_LEVEL]) {
                    this.loadTownData(val)
                } else {
                    this.afterModelChange(val)
                }
            }
        },
        afterModelChange(val) {
            this.region = getRegionByModel(val, this.availableLevels, this.listTown,this.defaultValue)
            this.resetCurrentStr()
            this.change(true)
        },
        /**
         * Region contents change
         *
         * @param {boolean} [initialize=false]
         */
        change(initialize = false) {
            this.regionHandle()
            this.emit(initialize)
        },
        /**
         * Plugin data change events
         * below plugin types are in use
         *
         * - Select
         * - Group
         * - Column
         *
         * @param {boolean} [input=false]
         */
        emit(input = false) {
            if (!input) {
                const model = []
                Object.entries(this.region)
                    .forEach(([key, value]) => {
                        if ((value)){
                            model.push(value.key)
                        }else {
                            if(this.defaultValue[key]){
                                model.push(this.defaultValue[key].key)
                                this.region[key] = Object.assign({},this.defaultValue[key]);
                            }else {
                                model.push(null)
                            }
                        }
                    })
                this.regionArr = model
                this.$emit('input', model)
            }
            console.log(this.region)
            this.$emit('on-change', JSON.parse(JSON.stringify(this.region)))
        },
        /**
         * Check if model and region data are equal
         *
         * @param {Array} model
         * @returns
         */
        differentModel(model) {
            if (!model) return false

            const {province, city, area, town, road, detail} = this.region
            const [modelProvince, modelCity, modelArea, modelTown, modelRoad, modelDetail] = model
            let levelResult = [
                this.isDifferent(province, modelProvince),
                this.isDifferent(city, modelCity),
                this.isDifferent(area, modelArea),
                this.isDifferent(town, modelTown),
                this.isDifferent(road, modelRoad),
                this.isDifferent(detail, modelDetail),
            ]
            return levelResult.some(val => val === false)
        },
        isDifferent(regionItem, modalItem) {
            return Boolean((!regionItem && !modalItem) || (regionItem && regionItem.key) === modalItem)
        },
        regionHandle() {
            // eslint-disable-next-line no-unused-vars
            for (const level of LEVELS.map(val => val.index)) {
                if (!this.levelHandle(level, getLoader(level))) break
            }
        },
        levelHandle(level, load) {
            const key = LEVEL_LIST[level]
            const parentKey = level === PROVINCE_LEVEL ? null : LEVEL_LIST[level - 1]
            const listName = 'list' + key.charAt().toUpperCase() + key.substring(1)

            if (level === PROVINCE_LEVEL || this[key]) {
                if (level === TOWN_LEVEL || level === ROAD_LEVEL || level === DETAIL_LEVEL) {
                    return false
                } else if (this.region[parentKey]) {
                    this[listName] = load(this.region[parentKey])
                }
                if (!this.levelCheck(this[listName], this.region[key])) {
                    this.clearRegion(level)
                    return false
                }
            }
            return true
        },
        loadTownData(val) {
            this.isLoading = true
            queryTown(val[AREA_LEVEL]).then(towns => {
                if (towns.length) {
                    this.listTown = towns
                    this.afterModelChange(val)
                }
                this.isLoading = false
            })
        },
        levelCheck(list, attr) {
            if (!list.length || !attr) return false
            return list.some(val => val.key === attr.key)
        },
        /**
         * Clear region fields
         *
         * @param {number} level
         */
        clearRegion(level) {
            const fields = LEVEL_LIST.slice(level)
            Object.keys(this.region).forEach(val => {
                if (fields.includes(val)) this.region[val] = null
            })
            /* eslint-disable no-fallthrough */
            switch (level) {
                case PROVINCE_LEVEL:
                    this.listCity = []
                case CITY_LEVEL:
                    this.listArea = []
                case AREA_LEVEL:
                    this.listTown = []
            }

        }
    },
    created() {
        const {srcProvince} = getListByLevel();
        // sort by length and code
        this.listProvince = srcProvince.slice().sort((a, b) => {
            const gap = a.value.length - b.value.length
            return gap === 0 ? Number(a.key) - Number(b.key) : gap
        })

        this.lang = language[this.i18n]
        if (Array.isArray(this.value) && this.value.length) this.modelChange(this.value)
    }
}
