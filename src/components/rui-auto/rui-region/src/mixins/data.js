import {availableLevels} from '../helper'
import {oneOf} from "_c/iview/utils/assist";
import {PROVINCE_KEY, CITY_KEY, AREA_KEY, TOWN_KEY, ROAD_KEY, DETAIL_KEY} from '../constants'

export default {
    props: {
        maxLevel: {
            type: Number,
            validator: function (v) {
                return v > 0 && v <= 6
            },
            default: 6
        },
        [CITY_KEY]: {
            type: Boolean,
            default: true
        },
        [AREA_KEY]: {
            type: Boolean,
            default: true
        },
        [TOWN_KEY]: {
            type: Boolean,
            default: true
        },
        [ROAD_KEY]: {
            type: Boolean,
            default: true
        },
        [DETAIL_KEY]: {
            type: Boolean,
            default: true
        },
        disabled: {
            type: Boolean,
            default: false
        },
        readonly: {
            type: Boolean,
            default: false
        },
        search: {
            type: Boolean,
            default: true
        },
        placement: {
            validator(value) {
                return oneOf(value, ['top', 'bottom', 'top-start', 'bottom-start', 'top-end', 'bottom-end']);
            },
            default: 'bottom-start'
        },
        transfer: {
            type: Boolean,
            default: false
        },
        transferClassName: {
            type: String,
            default: ''
        },
        i18n: {
            type: String,
            default: 'cn'
        },
        value: Array,
        defaultValue:{
            type:Object,
            default:()=>{
                return {town:{key:'99999999',value:''},road:{key:'99999999',value:''}}
            }
        }
    },
    data() {
        return {
            [PROVINCE_KEY]: true,
            // city: true,
            // area: true,
            // town: true,
            // road: true,
            // detail: true,
            // levels list data
            listProvince: [],
            listCity: [],
            listArea: [],
            listTown: [],

            lang: {},
            regionArr: [],
            region: {
                province: null,
                city: null,
                area: null,
                town: null,
                road: null,
                detail: null
            }
        }
    },
    watch: {
        value: {
            handler: 'modelChange',
            deep: true
        }
    },
    computed: {
        selectedText() {
            const arr = []
            const {province, city, area, town, road, detail} = this.region
            if (this.province&&province&&province.value!==null&&province.value!=='') arr.push(province.value)
            if (this.city&&city&&city.value!==null&&city.value!=='') arr.push(city.value)
            if (this.area&&area&&area.value!==null&&area.value!=='') arr.push(area.value)
           if (this.town&&town&&town.value!==null&&town.value!=='') arr.push(town.value)
            if (this.road&&road&&road.value!==null&&road.value!=='') arr.push(road.value)
            if (this.detail&&detail&&detail.value!==null&&detail.value!=='') arr.push(detail.value)
            return arr.join('/')
        },
        availableLevels() {
            return availableLevels(this.city, this.area, this.town, this.road, this.detail)
        },
        currentLevels() {
            return Object.entries(this.region)
                .filter(([key, value]) => value)
                .map(([key, value]) => key)
        }
    }
}
