import {
    LEVEL_LIST,
    PROVINCE_KEY, CITY_KEY, AREA_KEY, TOWN_KEY,
    CITY_LEVEL, AREA_LEVEL, TOWN_LEVEL, ROAD_LEVEL, DETAIL_LEVEL, ROAD_KEY, DETAIL_KEY
} from './constants'
import {getListByLevel, listByParentCode} from '@/projects/basic/api/admin/location'

/**
 * load city list by province data
 *
 * @param province
 * @returns {Array}
 */
export function loadCity(province) {
    if (province && Object.keys(province).length) {
        const {srcCity} = getListByLevel();
        const list = srcCity.filter(val => {
            const num = Number.parseInt(province.key)
            return (val.key - num) < 1e4 && (val.key % num) < 1e4
        })
        // Municipalities directly under the central government
        return list.length ? list : [province]
    } else return []
}

/**
 * load area list by city data
 *
 * @param city
 * @returns {Array}
 */
export function loadArea(city) {
    if (city && Object.keys(city).length) {
        const cityKey = Number.parseInt(city.key)
        const isNotProvince = cityKey % 1e4
        const calcNum = isNotProvince ? 100 : 1e4
        const {srcArea} = getListByLevel();
        const list = srcArea.filter(val => {
            return (val.key - cityKey) < calcNum && val.key % cityKey < calcNum
        })
        // Prefecture-level city
        return list.length ? list : [city]
    } else return []
}

/**
 * load town list by area data
 *
 * @param area
 * @returns {Promise}
 */
export function queryTown(area) {
    return new Promise((resolve, reject) => {
        listByParentCode({parentCode: area}).then(res => {
            res.code === '0000' && res.data
                ? resolve(Object.keys(res.data).map(e => ({key: e, value: res.data[e]})))
                : reject(res)
        }).catch(e => {
            reject(e)
        })
    })
}

/**
 * Get level list loader
 *
 * @export
 * @param {number} level
 * @returns
 */
export function getLoader(level) {
    switch (level) {
        case CITY_LEVEL:
            return loadCity
        case AREA_LEVEL:
            return loadArea
        // case TOWN_LEVEL:
        //     return queryTown
    }
}

/**
 * Get available region levels
 *
 * @export
 * @param {boolean} city
 * @param {boolean} area
 * @param {boolean} town
 * @param {boolean} road
 * @param {boolean} detail
 */
export function availableLevels() {
    const result = [PROVINCE_KEY]
    const switchs = Array.from(arguments)

    for (let i = 0; i < switchs.length; i++) {
        if (switchs[i]) {
            result.push(LEVEL_LIST[i + 1])
        }
    }

    return result
}

/**
 * Check model format valid
 *
 * @export
 * @param {Array} model
 * @returns {boolean}
 */
export function validModel(model) {
    return Array.isArray(model)
}

/**
 * Get detail data by key
 *
 * @param {string} key
 */
const getDetail = key => {
    const {srcList} = getListByLevel();
    const item = srcList.find(val => val.key === key)
    if (item && Object.keys(item).length) {
        return {
            key: item.key,
            value: item.value
        }
    } else return null
}

/**
 * Get region raw data from model
 *
 * model format:
 * [province, city, area, town, ...]
 *
 * region raw data format:
 * {
 *   province: { key: 'xxx', value: 'yyy' },
 *   city: { key: 'xxx', value: 'yyy' },
 *   area: { key: 'xxx', value: 'yyy' },
 *   town: { key: 'xxx', value: 'yyy' },
 *   ...
 * }
 *
 * @export
 *
 * @param {Array} model
 * @param {array} levels
 * @param {array} towns
 *
 * @returns {object} region raw data
 */
export function getRegionByModel(model, levels, towns,defaultValue={}) {
   // debugger
    const region = {
        province: defaultValue['province']?Object.assign({},defaultValue['province']):null,
        city: defaultValue['city']?Object.assign({},defaultValue['city']):null,
        area: defaultValue['area']?Object.assign({},defaultValue['area']):null,
        town: defaultValue['town']?Object.assign({},defaultValue['town']):null,
        road: defaultValue['road']?Object.assign({},defaultValue['road']):null,
        detail: null
    }
    const [modelProvince, modelCity, modelArea, modelTown, modelRoad, modelDetail] = model

    const inLevel = key => levels.some(val => val === key)

    if (modelProvince&&inLevel(PROVINCE_KEY)){
        region.province = getDetail(modelProvince)
    }

    if (modelCity && inLevel(CITY_KEY)) {
        region.city = getDetail(modelCity)
    }
    if (modelArea && inLevel(AREA_KEY)) {
        region.area = getDetail(modelArea)
    }
    if (modelTown && inLevel(TOWN_KEY)) {
        region.town = towns && towns.find(val => val.key === modelTown)
    }
    if (modelRoad && inLevel(ROAD_KEY)) {
        region.road = {key: modelRoad, value: modelRoad}
    }
    if (modelDetail && inLevel(DETAIL_KEY)){
        region.detail = {key: modelDetail, value: modelDetail}
    }
    return region
}
