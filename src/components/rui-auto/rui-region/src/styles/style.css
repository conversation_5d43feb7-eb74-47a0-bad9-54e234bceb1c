.rg-region-box .rg-dropdownMenu {
    position: relative;
}
.rg-region .rg-region-box {
    min-width:100%;
 }

.rg-region-box .rg-input-box {
    width: 100%;
}
.rg-region .ivu-input-wrapper{
    width: 100%;
}
.w200 .rg-region-box {
    width:200px !important;
 }
.tw100{
    width:calc(50% + 270px) !important;
}
.tw200 .ivu-input-wrapper,.tw200 .ivu-select,.tw200 .ivu-cascader{
    width:calc(50% + 270px) !important;
}

.ivu-form-item-error-tip{min-width:200px;}
.ivu-dropdown.rg-region {
    display: block;
    position: relative;
}
.ivu-dropdown.rg-region .ivu-input-wrapper {
    width: 100%;
}
.rg-region-width-slot {
    display: flex;
}
.rg-region-width-slot .rg-region {
    flex-grow: 1;
}
.rg-region.ivu-dropdown-transfer {
    max-height: 100%;
}

div.rg-header {
    min-width: 500px;
    background-color: #fff;
}

div.rg-header > h3 {
    padding: 6px 80px 10px 10px;
    margin: 0;
    text-align: left;
    color: #24292e;
    font-size: 16px;
    white-space: nowrap;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

div.rg-header > h3 .rg-header-selected {
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    font-size: 14px;
    max-width: 310px;
    display: inline-block;
}

div.rg-header button {
    position: absolute;
    -webkit-appearance: none;
    padding: 0;
    cursor: pointer;
    background: 0 0;
    border: 0;
    outline: none;
    line-height: 100%;
    color: #999;
    top: 3px;
    font-size: 20px;
}

div.rg-header button.rg-close-button {
    right: 10px;
    font-size: 26px;
    top: 0;
    font-weight: 300;
}

div.rg-header button.rg-removeall-button {
    right: 32px;
}

div.rg-header button.rg-done-button {
    /*right 54px*/
    right: 8px;
    top: 4px;
}

div.rg-header button.rg-done-button i {
    font-size: 18px;
}

div.rg-header button:hover {
    color: #000;
}

div.rg-search {
    padding: 2px 10px 10px;
    background-color: #fff;
}

div.rg-level-tabs {
    padding: 0 10px;
    background-color: #fff;
}

div.rg-level-tabs ul {
    padding: 0;
    margin: 0;
    line-height: 1.5;
    border-bottom: 1px solid #eee;
}

div.rg-level-tabs ul li {
    display: inline-block;
    position: relative;
}

div.rg-level-tabs ul li.active a {
    color: #2960F2;
    background-color: #fff;
    font-weight: 600;
}

div.rg-level-tabs ul li.active:after {
    content: "";
    display: block;
    position: absolute;
    bottom: 0;
    height: 0.2rem;
    width: 100%;
    background-color: #2960F2;
}

div.rg-level-tabs ul li a {
    display: block;
    padding: 0.2rem 1rem 0.6rem;
    font-size: 14px;
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
    line-height: 1.43;
    font-family: "Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", Calibri, Helvetica, Arial;
}

div.rg-results-container {
    background-color: #fff;
    list-style: none;
    margin: 0;
    padding: 5px;
    position: relative;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    clear: both;
    border-bottom-left-radius: 2px;
    border-bottom-right-radius: 2px;
}

div.rg-results-container ul.rg-results {
    background-color: #fff;
    list-style: none;
    margin: 0;
    padding: 5px;
    /*height 300px*/
    width: 500px;
    line-height: 1.5;
}

div.rg-results-container ul.rg-results li.rg-item {
    display: inline-block;
    /*border 1px solid white*/
    border-radius: 2px;
    margin-right: 5px;
    color: #777;
}

div.rg-results-container ul.rg-results li.rg-item:hover {
    /*border 1px solid #DDDDDD*/
    /*box-shadow 0 1px 8px rgba(0,0,0,0.2)*/
    /*-moz-box-shadow 0 1px 8px rgba(0,0,0,0.2)*/
    /*-webkit-box-shadow 0 1px 8px rgba(0,0,0,0.2)*/
    color: #000;
    background-color: #f5f5f5;
}

div.rg-results-container ul.rg-results li.rg-item.active {
    background-color: #e4eaee;
    color: #000;
}

div.rg-results-container ul.rg-results > li {
    /*height auto
          line-height 1*/
    margin: 0;
    overflow: hidden;
    padding: 3px 10px;
    position: relative;
    text-align: left;
    white-space: nowrap;
    font-size: 14px;
    color: #000;
    cursor: pointer;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

div.rg-results-container ul.rg-results > li.rg-message-box {
    height: 30px;
    line-height: 30px;
    text-align: center;
    box-sizing: content-box;
    font-size: 14px;
    cursor: default;
}

div.rg-results-container ul.rg-results > li.rg-message-box i {
    font-size: 18px;
}


div.v-region select {
    width: auto;
    display: inline-block;
    overflow: hidden;
    box-sizing: content-box;
    padding: 0 12px;
    margin-right: 5px;
}

div.rg-caller-container {
    display: inline-block;
}

.rg-default-btn {
    display: inline-block;
    position: relative;
    padding: 6px 12px;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.42857143;
    outline: 0 !important;
    color: #666;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.rg-default-btn span.rg-caret-down {
    transition: transform 0.2s ease;
}

.rg-default-btn span.rg-clear-btn {
    margin-left: 5px;
}

.rg-default-btn span.rg-clear-btn:hover {
    font-weight: bold;
}

.rg-default-btn.rg-opened {
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.08);
    border: 1px solid #aaa;
    color: #000;
}

.rg-default-btn.rg-opened span.rg-caret-down {
    transform: rotate(180deg);
}

.rg-default-btn.rg-opened:hover {
    border: 1px solid #aaa;
}

.rg-default-btn:hover {
    border: 1px solid #aaa;
    color: #000;
}

.rg-caret-down {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 5px;
    border-top: 4px solid;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    /*float right*/
    /*margin-top 3px*/
    vertical-align: middle;
    content: "";
    /*
      position absolute
      top 7px
      right 10px
      */
}

.rg-input {
    display: block;
    background-color: #f5f5f5;
    margin: 0 !important;
    border: 0;
    width: 100%;
    font-size: 14px;
    line-height: 1.42;
    padding: 4px 6px;
    vertical-align: middle;
    box-sizing: border-box;
    outline: none !important;
    border-radius: 2px;
}
.rg-results-container .rg-results .ivu-spin-fix.rg-container-spin {
    position: absolute;
}
