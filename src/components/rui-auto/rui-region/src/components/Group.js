import data from '../mixins/data'
import method from '../mixins/method'
import search from '../mixins/selectorWithSearch'
import selector from '../mixins/selector'
import language from '../language'
import {
    PROVINCE_LEVEL,
    CITY_LEVEL,
    AREA_LEVEL,
    TOWN_LEVEL,
    ROAD_LEVEL,
    DETAIL_LEVEL,
    LEVELS,
    LEVEL_LIST
} from '../constants'
import '../styles/style.css'
import {queryTown} from "_c/rui-auto/rui-region/src/helper";

export default {
    name: 'RuiRegion',
    mixins: [data, method, search, selector],
    inheritAttrs: false,
    data() {
        return {
            isEnterInput: false,
            isLoading: false,
            currentStr: '',
            list: [],
            query: '',
            level: -1
        }
    },
    watch: {
        /**
         * region search
         * search region description first, if no result, then search region key
         * @param value
         */
        query(value) {
            const list = this.getList(this.level)
            let tmp = []
            tmp = list.filter(val => val.value.toLowerCase().includes(value.toLowerCase()))
            if (tmp.length === 0) tmp = list.filter(val => val.key.includes(value))
            this.list = tmp
        },
        /**
         * current region group
         */
        level(val) {
            if (val === ROAD_LEVEL || val === DETAIL_LEVEL) {
                this.currentStr = this.regionArr[val] ? this.regionArr[val] : ''
            } else if (val === TOWN_LEVEL) {
                this.list = []
                this.getTownListData()
            } else {
                console.log('111')
                this.list = this.getList(val)
            }
            // this.show && this.adjust()
        }
    },
    render(h) {
        const lang = language[this.i18n]
        const boxChild = []
        const child = []
        const boxCls = ['rg-region-box']
        if (this.readonly) {
            boxChild.push(h('span', {class: 'rg-region-readonly-text'}, this.selectedText))
            boxCls.push('rg-region-readonly')
        } else {
            // child.push(this.buildCaller(h))
            // child.push(this.buildHeader(h))
            child.push(this.buildSearch(h))
            child.push(this.buildTabs(h))
            child.push(this.buildContent(h))

            const icon = h('Icon', {
                props: {type: 'ios-close-circle'},
                slot: 'suffix',
                style: {display: this.isEnterInput && this.selectedText && !this.disabled ? 'block' : 'none'},
                on: {
                    click: this.clear
                }
            })
            const input = h('Input', {
                props: {
                    value: this.selectedText,
                    placeholder: lang.pleaseSelect,
                    readonly: true,
                    disabled: this.disabled
                },
                on: {
                    'on-focus': () => {
                        this.show = true
                    },
                },
                nativeOn: {
                    mouseenter: () => {
                        this.isEnterInput = true
                    },
                    mouseleave: () => {
                        this.isEnterInput = false
                    }
                }
            }, [icon])
            const menu = h('DropdownMenu', {
                slot: 'list',
                class: 'rg-dropdownMenu'
            }, child)

            const dropdown = h('Dropdown', {
                ref: 'drop',
                props: {
                    trigger: 'custom',
                    visible: this.show && !this.disabled,
                    border: false,
                    placement: this.placement,
                    transfer: this.transfer,
                    transferClassName: this.transferClassName + ' rg-region'
                },
                class: 'rg-region',
                on: {
                    'on-clickoutside': ($event) => {
                        //console.log($event)
                        this.show = false
                    }
                    // click: this.showChange
                }
            }, [input, menu])
            boxChild.push(dropdown)
        }
        if ('address-right' in this.$scopedSlots) {
            boxChild.push(this.$scopedSlots["address-right"]({
                value: this.regionArr
            }))
            boxCls.push('rg-region-width-slot', 'slot-address-right')
        }

        return h('div', {class: boxCls}, boxChild)
    },
    methods: {
        buildHeader(h) {
            const child = []

            child.push(h('h3', [
                h('span', {
                    class: {
                        'rg-header-selected': this.selectedText
                    }
                }, this.selectedText || this.lang.defaultHead)
            ]))
            return h('div', {class: 'rg-header'}, child)
        },
        buildSearch(h) {
            if (!this.search) return
            return h('div', {class: 'rg-search'}, [
                h('input', {
                    ref: 'search',
                    class: 'rg-input',
                    attrs: {
                        type: 'text',
                        autocomplete: 'off'
                    },
                    domProps: {
                        value: this.query
                    },
                    on: {
                        input: e => {
                            this.query = e.target.value.trim()
                        }
                    }
                })
            ])
        },
        buildTabs(h) {
            const child = []
            LEVELS.forEach(val => {
                if (this.levelAvailable(val.index)) {
                    child.push(h('li', {
                        key: val.index,
                        class: {
                            active: val.index === this.level
                        }
                    }, [
                        h('a', {
                            attrs: {
                                href: 'javascript:void(0);'
                            },
                            on: {
                                click: () => {
                                    this.pickTabChange(val)
                                }
                            }
                        }, val.title)
                    ]))
                }
            })
            return h('div', {class: 'rg-level-tabs'}, [
                h('ul', child)
            ])
        },
        buildContent(h) {
            const child = [];
            if (this.level === ROAD_LEVEL || this.level === DETAIL_LEVEL) {
                let dom;
                if ((this.region.area&&this.area)||(this.region.city&&this.city)) {
                    dom = h('Input', {
                        class: 'rg-input-box',
                        props: {
                            value: this.currentStr,
                            icon: 'ios-arrow-forward'
                        },
                        on: {
                            'on-enter': this.pickEnter,
                            'on-click': this.pickEnter,
                            input: (event) => {
                                this.pickChange(event)
                            }
                        }
                    })
                } else {
                    dom = h('li', {class: 'rg-message-box'}, this.lang.finishPre)
                }
                child.push(dom)
            } else if (this.list.length) {
                child.push(...this.list.map((val, idx) => {
                    return h('li', {
                        class: {
                            'rg-item': true,
                            active: this.match(val)
                        },
                        key: idx,
                        on: {
                            mouseup: () => {
                                this.pick(val)
                            }
                        }
                    }, val.value)
                }))
            } else {
                child.push(h('li', {
                    class: 'rg-message-box'
                }, this.lang.noMatch))
            }
            if (this.isLoading) {
                child.push(h('Spin', {props: {fix: true, size: "large"}, class: 'rg-container-spin'}))
            }
            return h('div', {class: 'rg-results-container'}, [
                h('ul', {class: 'rg-results'}, child)
            ])
        },
        // check level available
        levelAvailable(level) {
            switch (level) {
                case PROVINCE_LEVEL:
                    return true
                case CITY_LEVEL:
                    return this.city
                case AREA_LEVEL:
                    return this.city && this.area
                case TOWN_LEVEL:
                    return this.city && this.area && this.town
                case ROAD_LEVEL:
                    return this.city && this.area && this.town && this.road
                case DETAIL_LEVEL:
                    return  this.detail
            }
        },
        // load list when switch to next level
        getList(val) {
            switch (val) {
                case PROVINCE_LEVEL:
                    return this.listProvince
                case CITY_LEVEL:
                    return this.listCity
                case AREA_LEVEL:
                    return this.listArea
                case TOWN_LEVEL:
                    return this.listTown
            }
            return []
        },
        getTownListData() {
            if (!this.regionArr[AREA_LEVEL]) return;
            this.isLoading = true
            queryTown(this.regionArr[AREA_LEVEL]).then(towns => {
                this.isLoading = false
                if (towns.length) this.list = towns
            }).catch(e => {
                this.isLoading = false
            })
        },
        match(item) {
            if (!item || !Object.keys(item).length) return false
            const R = this.region
            const key = item.key
            switch (this.level) {
                case PROVINCE_LEVEL:
                    return R.province && R.province.key === key
                case CITY_LEVEL:
                    return R.city && R.city.key === key
                case AREA_LEVEL:
                    return R.area && R.area.key === key
                case TOWN_LEVEL:
                    return R.town && R.town.key === key
            }
        },
        pickTabChange(val) {
            this.level = val.index;
            this.$refs.drop.update();
        },
        nextLevel(level) {
            if (level === DETAIL_LEVEL) {
                return level
            } else {
                if(this.$props[LEVELS[level + 1].key]){
                    return level+1;
                }else{
                    return this.nextLevel(level+1);
                }
            }
        },
        pick(item) {
            const nextLevel = this.nextLevel(this.level)
            const attr = LEVEL_LIST[this.level]
            this.region[attr] = item
            this.change()
            console.log(nextLevel,this.level,"======")
            if (this.levelAvailable(nextLevel) && this.level !== nextLevel) {
                this.level = nextLevel
                this.$refs.drop.update();
            } else {
                this.close()
            }
        },
        pickEnter(e) {
            const nextLevel = this.nextLevel(this.level)
            this.regionHandle()
            if (this.levelAvailable(nextLevel) && this.level !== nextLevel) {
                e.target.value = ''
                this.level = nextLevel
            } else {
                this.close()
            }
        },
        pickChange(e) {
            const attr = LEVEL_LIST[this.level]
            this.region[attr] = {key: e, value: e}
            this.currentStr = e
            this.emit()
        },
        resetCurrentStr() {
            this.currentStr = ''
        },
        clear() {
            this.clearRegion(PROVINCE_LEVEL)
            this.level = PROVINCE_LEVEL
            this.change()
        },
    },
    beforeMount() {
        this.level = PROVINCE_LEVEL
    }
}
