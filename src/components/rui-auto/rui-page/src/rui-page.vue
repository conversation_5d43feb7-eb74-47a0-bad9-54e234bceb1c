<template>
    <Row >
        <Col span="24">
            <Card v-if="hasCardBox">
                <slot></slot>
            </Card>
            <div v-else class="cardBox">
                <slot></slot>
            </div>
            <Spin size="large" fix v-if="!init"></Spin>
        </Col>
    </Row>
</template>
<script>
    import {getByTypes} from "@/projects/basic/api/admin/datadic";
    import {deepClone} from "@/libs/utils/ObjectClone";

    export default {
        name:'rui-page',
        props:{
            hasCardBox: {
                type: Boolean,
                default: true
            },
            defines:{
                type:Array,
                require: true
            },
            innerPage:{
                type:Boolean,
                require: false,
                default: false
            }
        },
        data(){
            return {
                pageData:{},
                dicKeys: [],
                dataDic: {},
                init:false,
                ruiRefs:[],
                finishedDefine:[]
            }
        },
        provide () {
            return {
                RuiPage: this
            };
        },
        methods:{
            initData(ids=[]){
                // console.log("初始化页面组件 defineIds =>"+ids.join(','))
                let self = this;
                if(!this.defines){
                    this.$Message.error('pages组件defines 不能为空');
                    return;
                }
                if(this.defines.length==0){
                    return;
                }
                let dicKeys=[];
                this.defines.forEach(define => {
                    if(ids.includes(define.id)) {
                        self.$set(self.pageData, define.id, {
                            fields: define.fields,
                        })
                        define.fields.forEach(field => {
                            if (field.dicKey && field.dicKey !== '' && !self.dicKeys.includes(field.dicKey)) {
                                self.dicKeys.push(field.dicKey);
                                dicKeys.push(field.dicKey)
                            }
                        })
                    }
                });
                this.initDataDic(dicKeys,ids);
            },
            getColumnDefine(defineId,defineKey){
              let rtn = null;
              for(let i=0;i<this.getPageData(defineId).fields.length;i++){
                  if(defineKey===(this.getPageData(defineId).fields[i]).key){
                      rtn = deepClone(this.getPageData(defineId).fields[i])
                      break;
                  }
              }
              return rtn;
            },
            getPageData(defineKey){
                if(this.pageData[defineKey]){
                    return this.pageData[defineKey];
                }else{
                    return [];
                }
            },
            isInit(){
                return this.init;
            },
            changeIds(ids){
                let self = this;
                ids.forEach(id=>{
                    if(!self.finishedDefine.includes(id)){
                        self.finishedDefine.push(id);
                    }
                })
            },
            initDataDic(dics,ids){
                let self = this;
                if (dics.length > 0) {
                    const dicSource = this.afs.getMenuParams(this).dicSource;
                    getByTypes(dics,dicSource).then(res => {
                        if (res.code === "0000" && res.data) {
                            Object.keys(res.data).forEach(key=>{
                                if(!self.dataDic[key]){
                                    self.dataDic[key] = res.data[key];
                                }
                            })
                            self.changeIds(ids)
                            self.$nextTick(()=>{
                                self.onBuildFinish(ids)
                                self.init = true;
                            })
                        }
                    });
                } else {
                    // debugger
                    self.changeIds(ids)
                    self.$nextTick(()=>{
                        self.onBuildFinish(ids)
                        self.init = true;
                    })
                }

            },
            onBuildFinish(defineIds){
              this.$emit('finished',defineIds);
            },
            getDefineIds(defines=[]){
                let ids = [];
                defines.forEach(define=>{
                    ids.push(define.id)
                })
                return ids;
            },
            makeRefs(refKey){
                // console.log(this.ruiRefs)
               return this.ruiRefs[refKey];
            }
        },
        created(){
            // this.initData(this.getDefineIds(this.defines));
            let self=this,temp = this;
            if(this.innerPage){
                while (temp.$parent) {
                    if(temp.$options.methods&&temp.$options.methods.afsComponentInfo&&temp.$options.methods.afsComponentInfo()._ruiInnerPage){
                        self.ruiRefs = temp.$refs;
                        break;
                    }
                    temp = temp.$parent;
                }
            }else {
                while (temp.$parent) {
                    if (temp.$parent.$options._componentTag === 'lease-keep-alive' || temp.$parent.$options._componentTag === 'TabPane') {
                        self.ruiRefs = temp.$refs;
                        break;
                    }
                    temp = temp.$parent;
                }
            }
        },
        watch:{
            defines:{
                immediate: true,
                handler(newVal) {
                    if(newVal.length==0) return
                    let rebuildIds = []
                    newVal.forEach(define=>{
                        if(!this.finishedDefine.includes(define.id)){
                            rebuildIds.push(define.id)
                        }
                    })
                    // console.log('init for '+ rebuildIds.join(','))
                    if(rebuildIds.length>0)
                        this.initData(rebuildIds);
                }
            }
        }
    }
</script>
