export default {
    name: 'FormSlot',
    functional: true,
    inject: ['RuiForm'],
    props: {
        slotName: {
            type:String,
            require:true
        },
        item: {
            type: Object,
            default: null
        },
        data: {
            type: Object,
            default: null
        }
    },
    render: (h, ctx) => {
        return h('span', ctx.injections.RuiForm.$scopedSlots[ctx.props.slotName]({
            item: ctx.props.item,
            data: ctx.props.data,
            key:ctx.props.item.key,
            dataDic:ctx.props.item.globalDic?ctx.injections.RuiForm.getDicByType(ctx.props.item.dicKey):ctx.props.item.dicData
        }));
    }
};
