<template>
    <!--  address类型 只读状态在组件实现 -->
    <div v-if="formSimpleItem.includes(item.key)&&item.type!=='address'">
        <span v-if="formHasSlotKeys.includes(item.key)">
            <fromSlot :item="item" :data="formData" :slotName="formSlots[item.key]"/>
        </span>
        <template v-else>
            {{parseSimpleStr(item,formData[item.key])}}
        </template>
    </div>
    <div v-else>
        <span v-if="formHasSlotKeys.includes(item.key)">
            <fromSlot :item="item" :data="formData" :slotName="formSlots[item.key]"/>
        </span>
        <span v-else>
            <span v-if="textTypes.includes(item.type)">
                <Input :autosize="{minRows: 2, maxRows: 4}"
                       :clearable="true"
                       :maxlength="item.maxlength?item.maxlength:150"
                       :password="item.type=='password'"
                       :placeholder="item.placeholder"
                       :rows="2"
                       :disabled="item.disabled"
                       :show-word-limit="false"
                       :style="{width:(item.width?item.width:200)+'px'}"
                       :type="item.type=='mobile'?'tel':item.type"
                       autocomplete="false"
                       size="small"
                       v-model="formData[item.key]"/>
            </span>
            <span v-else-if="item.type=='radio'">
                <RadioGroup v-model="formData[item.key]" :disabled="item.disabled">
                    <Radio :false-value="''"
                           :key="radio.id"
                           :label="radio.value"
                           :true-value="radio.value"
                           v-for="(radio) in _getDicData(item)"
                    >
                        {{radio.title}}
                    </Radio>
                </RadioGroup>
            </span>
            <span v-else-if="item.type=='checkbox'">
                <CheckboxGroup :disabled="item.disabled" v-model="formData[item.key]">
                    <Checkbox :key="checkbox.id"
                              :label="checkbox.value"
                              v-for="(checkbox) in _getDicData(item)"
                    >
                        {{checkbox.title}}
                    </Checkbox>
                </CheckboxGroup>
            </span>
            <span v-else-if="item.type=='singleSelect'">
                <Select :disabled="item.disabled" :style="{width:(item.width?item.width:200)+'px'}"
                        :transfer="true"
                        :clearable="true"
                        size="small"
                        v-model="formData[item.key]">
                    <Option :key="singleSelect.id"
                            :value="singleSelect.value"
                            v-for="(singleSelect) in _getDicData(item)">
                        {{singleSelect.title}}
                    </Option>
                </Select>
            </span>
            <span  v-else-if="item.type=='multipleSelect'">
                <Select :disabled="item.disabled" :max-tag-count="2"
                        :multiple="true"
                        :style="{width:(item.width?item.width:200)+'px'}"
                        :transfer="true"
                        size="small"
                        v-model="formData[item.key]">
                    <Option :key="singleSelect.id"
                            :value="singleSelect.value"
                            v-for="(singleSelect) in _getDicData(item)">
                        {{singleSelect.title}}
                    </Option>
                </Select>
            </span>
             <span v-else-if="item.type=='date'">
                 <DatePicker :disabled="item.disabled" :format="item.format?item.format:'yyyy-MM-dd'"
                             :placeholder="item.placeholder"
                             v-model="formData[item.key]"
                             :holiday-model="item.holidayModel"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             type="date"
                 >

                 </DatePicker>
             </span>
            <span v-else-if="item.type=='dateRange'">
                 <DatePicker :disabled="item.disabled" :format="item.format?item.format:'yyyy-MM-dd'"
                             :placeholder="item.placeholder"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             :holiday-model="item.holidayModel"
                             v-model="formData[item.key]"
                             separator=" 至 "
                             type="daterange"
                 >

                 </DatePicker>
             </span>
             <span v-else-if="item.type=='datetime'">
                 <DatePicker :disabled="item.disabled" :format="item.format?item.format:'yyyy-MM-dd HH:mm:ss'"
                             :placeholder="item.placeholder"
                             v-model="formData[item.key]"
                             :holiday-model="item.holidayModel"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             type="datetime"
                 >

                 </DatePicker>
             </span>
            <span v-else-if="item.type=='datetimeRange'">
                 <DatePicker :disabled="item.disabled" :format="item.format?item.format:'yyyy-MM-dd HH:mm:ss'"
                             :placeholder="item.placeholder"
                             v-model="formData[item.key]"
                             :holiday-model="item.holidayModel"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             separator=" 至 "
                             type="datetimerange"
                 >

                 </DatePicker>
             </span>
            <span v-else-if="item.type=='time'">
                 <TimePicker :disabled="item.disabled" :format="item.format?item.format:'HH:mm:ss'"
                             :placeholder="item.placeholder"
                             v-model="formData[item.key]"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             separator=" 至 "
                             type="time"
                 >

                 </TimePicker>
             </span>
            <span v-else-if="item.type=='timeRange'">
                 <TimePicker :disabled="item.disabled" :format="item.format?item.format:'HH:mm:ss'"
                             :placeholder="item.placeholder"
                             v-model="formData[item.key]"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             :transfer="true"
                             @on-change="formData[item.key]=$event"
                             separator=" 至 "
                             type="timerange"
                 >

                 </TimePicker>
             </span>
            <span v-else-if="item.type=='number'">
                <NumberInput :disabled="item.disabled" :placeholder="item.placeholder"
                             :formatter="value => item.dataFormater(value,!Number.isNaN(item.scale)?item.scale:2)"
                             :precision="!Number.isNaN(item.scale)?item.scale:2"
                             :step="item.step?item.step:0"
                             :style="{width:(item.width?item.width:200)+'px'}"
                             v-model="formData[item.key]"></NumberInput>
             </span>
            <span v-else-if="item.type=='currency'">
                <Poptip trigger="focus">
                <NumberInput
                    :disabled="item.disabled"
                    :active-change="true"
                    :formatter="value =>  '￥'+item.dataFormater(value,!Number.isNaN(item.scale)?item.scale:2)"
                    :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                    :placeholder="item.placeholder"
                    :precision="!Number.isNaN(item.scale)?item.scale:2"
                    :step="item.step?item.step:0"
                    :style="{width:(item.width?item.width:200)+'px'}"
                    v-model="formData[item.key]"></NumberInput>
                    <div
                        slot="content">{{ convertCurrencyToChinese(formData[item.key]) }}</div>
                </Poptip>
             </span>
            <span v-else-if="item.type=='address'">
                <rui-region
                    v-model="formData[item.key]"
                    :city="item.city"
                    :area="item.area"
                    :town="item.town"
                    :road="item.road"
                    :detail="item.detail"
                    :disabled='item.disabled'
                    :readonly="formSimpleItem.includes(item.key)"
                    :placeholder="item.placeholder"
                />
            </span>
            <span v-else>
                {{item.key}}暂不支持
            </span>
        </span>
        <slot></slot>
    </div>
</template>
<script>
    import ruiMixin from '../../mixin/rui-global-components';
    import NumberInput from '../../rewrite/number-input'
    import fromSlot from './formSlot';
    export default {
        name: 'rui-form-item',
        mixins: [ruiMixin],
        components: {
            NumberInput,
            fromSlot
        },
        inject: ['RuiForm'],
        props: {
            formData: {
                type: Object,
                require: true
            },
            item: {
                type: Object,
                require: true
            },
            formSimpleItem: {
                type: Array,
                require: true
            },
            formRules: {
                type: Object,
                require: true
            },
            formSlots: {
                type: Object,
                require: true
            },
            formHasSlotKeys: {
                type: Array,
                require: true
            }

        },
        data() {
            return {

            }
        },
        methods: {
        },
        mounted() {
        },
        beforeMount() {
        }
    }
</script>
