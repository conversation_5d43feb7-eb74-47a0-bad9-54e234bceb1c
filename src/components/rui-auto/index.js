import RuiPage from './rui-page'
import RuiTable from './rui-table'
import RuiForm from './rui-form'
import RuiQuery from './rui-query'
import RuiNumber from './rewrite/number-input'
import RuiTree from './rui-tree/src/rui-tree'
import RuiAddress from './rui-address'
import RuiRegion from './rui-region'
import RuiAnchor from './rui-anchor'

const components = {
    RuiAnchor,
    RuiRegion,
    RuiAddress,
    RuiPage,
    RuiTable,
    RuiQuery,
    RuiForm,
    RuiNumber,
    RuiTree
}

const install = function(Vue, opts = {}) {
    if (install.installed) return;

    Object.keys(components).forEach(key => {
        Vue.component(key, components[key]);
    });
}

export default {
    install
};
