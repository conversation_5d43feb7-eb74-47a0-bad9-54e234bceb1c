<template>
    <div class="rui-address">
        <Cascader
            v-model="loadSelect"
            :load-data="loadData"
            :data="load"
            placeholder="下拉选择省市"
            class="rui-address-cascade"
            :disabled="disabled"
            @on-change="handleCascaderChange"
        />
        <Input
            v-model="currentValue.town"
            @on-change="handleDetailChange"
            :disabled="disabled"
            placeholder="请输入村(路)"
        />
        <span class="rui-address-desc">&nbsp;村/路 &nbsp;</span>
        <Input
            v-model="currentValue.detailAddress"
            @on-change="handleDetailChange"
            :disabled="disabled"
            placeholder="请输入详细地址"
        />
        <slot name="address-right"></slot>
    </div>
</template>
<script>
import initAddress from './initAddress.json'
import {getAddressList} from "_p/afs-core-business/api/afs-case/infomationDetail/applyCustomerDetail";

const filterAddress = function (res) {
    if (res.code !== "0000") {
        return [];
    }
    res.data.forEach(item => {
        // 表明是否是父节点
        if (item.isParent) {
            item.loading = false;
            item.children = [];
        }
    })
    return res.data
}

export default {
    name: "RuiAddress",
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            type: Array,
            require: true,
        },
        disabled: {
            type: Boolean,
            default: false
        }

    },
    data() {
        return {
            currentValue: {
                addressType: '', // 地址类型
                province: '', // 省份
                city: '',  // 城市
                county: '', // 区/县
                street: '', // 街道
                town: '', // 镇
                detailAddress: '', // 详细地址
            },
            loadSelect: [], // 下拉数据
            // load: [], // 初始化数据
            load: filterAddress(initAddress), // 初始化数据
        }
    },
    computed: {
    },
    watch: {
        value: {
            handler(val, old) {
                if (Array.isArray(val) && val.length > 0) {
                    this.onPopValueChange(val)
                }
            },
            immediate: true
        },
    },
    created() {
    },
    mounted() {
    },
    methods: {
        onPopValueChange(val) {
            const [province, city, county, street, town, detailAddress] = val
            this.loadSelect = [province, city, county, street]
            this.currentValue = Object.assign({}, this.currentValue, {
                province,
                city,
                county,
                street,
                town,
                detailAddress
            })
        },
        handleCascaderChange(value, selectedData) {
            const [province, city, county, street] = value
            this.currentValue = Object.assign({}, this.currentValue, {province, city, county, street})
            this.emitValue()
        },
        handleDetailChange() {
            this.emitValue()
        },
        emitValue() {
            const {province, city, county, street, town, detailAddress} = this.currentValue
            this.$emit('input', [province, city, county, street, town, detailAddress])
        },
        loadData(item, callback) {
            item.loading = true;
            getAddressList({upperCode: item.value}).then((res) => {
                if (res.code === "0000" && res.data) {
                    res.data.forEach(itm => {
                        if (itm.isParent) {
                            itm.loading = false;
                            itm.children = [];
                        }
                    })
                    item.children = res.data;
                    item.loading = false;
                    callback();
                }
            })
        },
        initGetLoadData() {
            this.queryLoadData({level: '1', upperCode: '1'}).then(data => {
                this.load.push(...data);
            })
        },
        // 获取地址
        queryLoadData(param) {
            return new Promise((resolve, reject) => {
                getAddressList(param).then((res) => {
                    if (res.code !== "0000") {
                        reject(res)
                        return;
                    }
                    res.data.forEach(item => {
                        // 表明是否是父节点
                        if (item.isParent) {
                            item.loading = false;
                            item.children = [];
                        }
                    })
                    resolve(res.data)
                })
            })
        },
    }
}
</script>
<style lang="less" scoped>
.rui-address {
    display: flex;

    .rui-address-cascade {
        width: 400px;

        /deep/ .ivu-input-wrapper {
            width: 400px;
        }
    }

    .rui-address-desc {
        height: 24px;
        line-height: 24px;
    }
}
</style>
