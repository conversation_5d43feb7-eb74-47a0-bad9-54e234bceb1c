{"code": "0000", "msg": "success", "data": [{"id": "218", "delFlag": "0", "addrLevel": "1", "value": "11", "label": "北京市", "upperCode": "1", "isParent": true, "postCode": "100000", "spellCode": "Beijing Shi", "expand": true, "checked": false}, {"id": "388", "delFlag": "0", "addrLevel": "1", "value": "12", "label": "天津市", "upperCode": "1", "isParent": true, "postCode": "300000", "spellCode": "Tianjin Shi", "expand": true, "checked": false}, {"id": "338", "delFlag": "0", "addrLevel": "1", "value": "13", "label": "河北省", "upperCode": "1", "isParent": true, "postCode": "050000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "336", "delFlag": "0", "addrLevel": "1", "value": "14", "label": "山西省", "upperCode": "1", "isParent": true, "postCode": "030000", "spellCode": "Shan<PERSON> ", "expand": true, "checked": false}, {"id": "141", "delFlag": "0", "addrLevel": "1", "value": "15", "label": "内蒙古自治区", "upperCode": "1", "isParent": true, "postCode": "010000", "spellCode": "Nei Mongol Zizhiqu", "expand": true, "checked": false}, {"id": "87", "delFlag": "0", "addrLevel": "1", "value": "21", "label": "辽宁省", "upperCode": "1", "isParent": true, "postCode": "110000", "spellCode": "Liaoning Sheng", "expand": true, "checked": false}, {"id": "356", "delFlag": "0", "addrLevel": "1", "value": "22", "label": "吉林省", "upperCode": "1", "isParent": true, "postCode": "130000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "154", "delFlag": "0", "addrLevel": "1", "value": "23", "label": "黑龙江省", "upperCode": "1", "isParent": true, "postCode": "150000", "spellCode": "Heilongjiang Sheng", "expand": true, "checked": false}, {"id": "27", "delFlag": "0", "addrLevel": "1", "value": "31", "label": "上海市", "upperCode": "1", "isParent": true, "postCode": "200000", "spellCode": "Shanghai Shi", "expand": true, "checked": false}, {"id": "125", "delFlag": "0", "addrLevel": "1", "value": "32", "label": "江苏省", "upperCode": "1", "isParent": true, "postCode": "210000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "296", "delFlag": "0", "addrLevel": "1", "value": "33", "label": "浙江省", "upperCode": "1", "isParent": true, "postCode": "310000", "spellCode": "Zhejiang Sheng", "expand": true, "checked": false}, {"id": "119", "delFlag": "0", "addrLevel": "1", "value": "34", "label": "安徽省", "upperCode": "1", "isParent": true, "postCode": "230000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "220", "delFlag": "0", "addrLevel": "1", "value": "35", "label": "福建省", "upperCode": "1", "isParent": true, "postCode": "350000", "spellCode": "<PERSON><PERSON> ", "expand": true, "checked": false}, {"id": "373", "delFlag": "0", "addrLevel": "1", "value": "36", "label": "江西省", "upperCode": "1", "isParent": true, "postCode": "330000", "spellCode": "Jiangxi Sheng", "expand": true, "checked": false}, {"id": "88", "delFlag": "0", "addrLevel": "1", "value": "37", "label": "山东省", "upperCode": "1", "isParent": true, "postCode": "250000", "spellCode": "Shandong Sheng ", "expand": true, "checked": false}, {"id": "318", "delFlag": "0", "addrLevel": "1", "value": "41", "label": "河南省", "upperCode": "1", "isParent": true, "postCode": "450000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "52", "delFlag": "0", "addrLevel": "1", "value": "42", "label": "湖北省", "upperCode": "1", "isParent": true, "postCode": "430000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "369", "delFlag": "0", "addrLevel": "1", "value": "43", "label": "湖南省", "upperCode": "1", "isParent": true, "postCode": "410000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "31", "delFlag": "0", "addrLevel": "1", "value": "44", "label": "广东省", "upperCode": "1", "isParent": true, "postCode": "510000", "spellCode": "Guangdong Sheng", "expand": true, "checked": false}, {"id": "50", "delFlag": "0", "addrLevel": "1", "value": "45", "label": "广西壮族自治区", "upperCode": "1", "isParent": true, "postCode": "530000", "spellCode": "Guangxi Zhuangzu Zizhiqu", "expand": true, "checked": false}, {"id": "195", "delFlag": "0", "addrLevel": "1", "value": "46", "label": "海南省", "upperCode": "1", "isParent": true, "postCode": "570000", "spellCode": "<PERSON><PERSON>", "expand": true, "checked": false}, {"id": "215", "delFlag": "0", "addrLevel": "1", "value": "50", "label": "重庆市", "upperCode": "1", "isParent": true, "postCode": "400000", "spellCode": "Chongqing Shi", "expand": true, "checked": false}, {"id": "324", "delFlag": "0", "addrLevel": "1", "value": "51", "label": "四川省", "upperCode": "1", "isParent": true, "postCode": "610000", "spellCode": "Sichuan Sheng", "expand": true, "checked": false}, {"id": "313", "delFlag": "0", "addrLevel": "1", "value": "52", "label": "贵州省", "upperCode": "1", "isParent": true, "postCode": "550000", "spellCode": "<PERSON><PERSON><PERSON>", "expand": true, "checked": false}, {"id": "301", "delFlag": "0", "addrLevel": "1", "value": "53", "label": "云南省", "upperCode": "1", "isParent": true, "postCode": "650000", "spellCode": "Yunnan Sheng", "expand": true, "checked": false}, {"id": "194", "delFlag": "0", "addrLevel": "1", "value": "54", "label": "西藏自治区", "upperCode": "1", "isParent": true, "postCode": "850000", "spellCode": "<PERSON><PERSON><PERSON>", "expand": true, "checked": false}, {"id": "170", "delFlag": "0", "addrLevel": "1", "value": "61", "label": "陕西省", "upperCode": "1", "isParent": true, "postCode": "710000", "spellCode": "Shan<PERSON> ", "expand": true, "checked": false}, {"id": "198", "delFlag": "0", "addrLevel": "1", "value": "62", "label": "甘肃省", "upperCode": "1", "isParent": true, "postCode": "730000", "spellCode": "<PERSON><PERSON><PERSON>", "expand": true, "checked": false}, {"id": "112", "delFlag": "0", "addrLevel": "1", "value": "63", "label": "青海省", "upperCode": "1", "isParent": true, "postCode": "810000", "spellCode": "Qing<PERSON>", "expand": true, "checked": false}, {"id": "367", "delFlag": "0", "addrLevel": "1", "value": "64", "label": "宁夏回族自治区", "upperCode": "1", "isParent": true, "postCode": "750000", "spellCode": "<PERSON><PERSON><PERSON>", "expand": true, "checked": false}, {"id": "233", "delFlag": "0", "addrLevel": "1", "value": "65", "label": "新疆维吾尔自治区", "upperCode": "1", "isParent": true, "postCode": "830000", "spellCode": "Xinjiang Uygur <PERSON>u", "expand": true, "checked": false}, {"id": "283", "delFlag": "0", "addrLevel": "1", "value": "71", "label": "台湾省", "upperCode": "1", "isParent": true, "postCode": "999079", "spellCode": "TWS", "expand": true, "checked": false}, {"id": "259", "delFlag": "0", "addrLevel": "1", "value": "81", "label": "香港特别行政区", "upperCode": "1", "isParent": true, "postCode": "999077", "spellCode": "XGTBXZQ", "expand": true, "checked": false}, {"id": "132", "delFlag": "0", "addrLevel": "1", "value": "82", "label": "澳门特别行政区", "upperCode": "1", "isParent": true, "postCode": "999078", "spellCode": "AMTBXZQ", "expand": true, "checked": false}]}