export default {
    stringValidator:(rule,value,callback)=>{
        if(!rule.required)
            return  true;
        if(value===null||value===undefined||value===''){
            return false;
        }else{
            if(typeof value == 'number'){
                return (value+'').length>0;
            }
            if(Array.isArray(value)){
                return value.length>0;
            }
            return true;
        }
    },
    arrayValidator:(rule,value,callback)=>{
        if(!rule.required)
            return  true;
        if(!value||value.length==0){
            return false
        }else{
            return true;
        }
    },
    mobileValidator:(rule,value,callback)=>{
        var myreg=/^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        if(!value||value.length==0){
            return true;
        }else{
            return myreg.test(value);
        }
    },
    dateValidator:(rule,value,callback)=>{
        if(!rule.required)
            return  true;
        if(!value||value==null||value==undefined||value==''){
            return false
        }else{
            return true;
        }
    },
    dateRangeValidator:(rule,value,callback)=>{
        if(!rule.required)
            return  true;
        if(!value||value.length==0){
            return false
        }else{
            if(value[0]==''||value[1]==''){
                return false;
            }
            return true;
        }
    },
    addressObjectValidator:(rule,value,callback)=>{
        if(!rule.required)
            return  true;
        return Array.isArray(value) && value.length > 0;
    },
}
