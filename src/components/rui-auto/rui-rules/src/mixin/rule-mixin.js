import {deepClone} from "@/libs/utils/ObjectClone";
import Constants from "_c/rui-auto/rui-rules/src/component/constant/constant";
export default {
    inject: ['RuiRule'],
    data() {
        return {
            ...Constants.operators
        }
    },
    methods: {
        clone(data) {
            return deepClone(data);
        },
        getDicByType(type){
            return this.RuiRule.dataDic[type]?this.RuiRule.dataDic[type]:[];
        },
        getCustDicBy<PERSON>ey(key){
            return this.RuiRule.custDics[key]?this.RuiRule.custDics[key]:[];
        },
        getCascaderFirstLevelData(key){
            return this.RuiRule.cascaderFirstLevelData[key]?this.RuiRule.cascaderFirstLevelData[key]:[];
        },
        getCascaderFun(key){
            return this.RuiRule.cascaderFun[key]?this.RuiRule.cascaderFun[key]:new Promise((resolve, reject)=>{resolve( {code:'7777',msg:'数据加载失败[加载函数未定义]'})});
        },
        randomColor(){
            let r = Math.floor(Math.random()*256);
            let g = Math.floor(Math.random()*256);
            let b = Math.floor(Math.random()*256);
            let color = '#'+r.toString(16)+g.toString(16)+b.toString(16);
            return color;
        }
    }
}
