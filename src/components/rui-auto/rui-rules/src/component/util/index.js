import {deepClone} from '@/libs/utils/ObjectClone'
import { v4 as uuid } from 'uuid';
const ternaryExpression = {
    left: {
        type: '',
        atom: {
            key: '',
            name: '',
            valueExpress: ''
        },
        express: {}
    },
    operator: {
        key: '',
        name: '',
        value: '',
        type: ''
    },
    right: {
        type: '',
        value: '',
        valueNames: '',
        atom: {},
        express: {}
    }
}
const calStrTernaryExpression = (express, depth, expressArr = [],chinese=false) => {
    if (!express||!express.left || !express.right) return
    if (express.left.type == '' && express.operator.key == '' && express.right.type == '') {
        return;
    }
    if (depth > 0) {
        expressArr.push("(")
    }
    if (express.left.type == 'atom') {
        expressArr.push(chinese?express.left.atom.name:express.left.atom.key);
    } else {
        calStrTernaryExpression(express.left.express,depth+1, expressArr,chinese)
    }
    expressArr.push(chinese?express.operator.name:express.operator.value);
    if (express.right.type == 'value') {
        if (express.operator.key == 'in') {
            expressArr.push('(')
            expressArr.push(express.right.valueNames!=''?express.right.valueNames:express.right.value);
            expressArr.push(')')
        } else if (express.operator.key == 'like') {
            expressArr.push('%'+(express.right.valueNames!=''?express.right.valueNames:express.right.value)+'%')
        } else {
            expressArr.push(express.right.valueNames!=''?express.right.valueNames:express.right.value);
        }
    } else if (express.right.type == 'atom') {
        expressArr.push(chinese?express.right.atom.name:express.right.atom.key);
    } else {
        calStrTernaryExpression(express.right.express, depth + 1, expressArr,chinese)
    }
    if (depth > 0) {
        expressArr.push(")")
    }
}
export const calStrExpress = (ruleData, rowIndex = 0,chinese=false, expressArr = []) => {
    if ((ruleData.type == 'express' && rowIndex > 0) || (ruleData.key == 'if' && ruleData.type != 'choice' && ruleData.type != 'condition')) {
        expressArr.push(chinese ? ruleData.label : ruleData.key)
    }
    if (ruleData.type == 'express') {
        expressArr.push('(');
    }

    if (ruleData.rows) {
        let withBlock =ruleData.rows.length > 1;
        ruleData.rows.forEach((row, index) => {
            if (row.type != 'express') {
                if ((index > 0 || row.type == 'condition') && row.type != 'choice')
                    expressArr.push(chinese ? row.label : row.key)
                if (row.not) {
                    expressArr.push(chinese ? '非' : "!")
                }
                if (withBlock || row.not) {
                    expressArr.push('(');
                }
                calStrTernaryExpression(row.express, 0, expressArr, chinese);
                if (row.rows) {
                    calStrExpress(row, index, chinese, expressArr)
                }

                if (withBlock || row.not) {
                    expressArr.push(')');
                }
            } else {
                calStrExpress(row, index, chinese, expressArr)
            }
        })
    }
    if (ruleData.type == 'express') {
        expressArr.push(')');
    }
    return expressArr.join(" ");
}

export const fillUuid=(treeData)=>{
    if(treeData){
        treeData.uuid = uuid();
    }
    if(!treeData.rows){
        treeData.rows = [];
    }
    if(treeData.rows&&treeData.rows.length){
        treeData.rows.forEach(node=>{
            fillUuid(node);
        })
    }
}

export const genTernaryExpression = () => {
    return deepClone(ternaryExpression);
}
export const genOperator = () => {
    return deepClone(ternaryExpression.operator);
}

export const genRight = () => {
    return deepClone(ternaryExpression.right);
}
