<template>
    <div style="float:left">
        <div v-if="!readOnly">
            <Select v-model="values[valueIndex]" v-if="!isInOpt" :label-in-value="true" clearable filterable @on-change="setName">
                <Option v-for="item in getDicByType(inputConfig.dicKey)" :placeholder="inputConfig.placeholder" :value="item.value" :key="item.value">{{ item.title }}</Option>
            </Select>
            <Select v-model="tempValues" v-else multiple :label-in-value="true" filterable @on-change="setName">
                <Option v-for="item in getDicByType(inputConfig.dicKey)" :placeholder="inputConfig.placeholder" :value="item.value" :key="item.value">{{ item.title }}</Option>
            </Select>
        </div>
        <div v-else style="font-size: 12px">
            {{valueNames.join(",")}}
        </div>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    export default {
        name: 'data-dic-type',
        mixins: [ruiMixin],
        props: {
            values: {
                type: Array,
                require:true
            },
            valueNames: {
                type: Array,
                require:true
            },
            valueIndex:{
                type:Number,
                require: true
            },
            inputConfig:{
                type:Object,
                require:true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            isInOpt:{
                type:Boolean,
                default:false
            }
        },
        data() {
            return {
                tempValues:[]
            }
        },
        computed: {
        },
        methods: {
            valueChecks() {
                if(this.isInOpt){
                    if (this.values.filter(val => val=='').length>0||this.values.length===0) {
                        this.$emit('checkError', true, '必须选择');
                        return
                    }
                    this.$emit('checkError', false, '');
                }else {
                    if (this.values[this.valueIndex] === ''||this.values[this.valueIndex] ===undefined) {
                        this.$emit('checkError', true, '必须选择');
                        return
                    }
                    this.$emit('checkError', false, '');
                }
            },
            setName(object){
                if(this.isInOpt){
                    if(object&&object.length>0){
                        this.values.splice(0,this.values.length)
                        this.values.push(...this.tempValues);
                        this.valueNames.splice(0,this.valueNames.length)
                        object.forEach(sel=>{
                            this.valueNames.push(sel.label);
                        })
                    }else {
                        this.valueNames.splice(0,this.valueNames.length)
                        this.values.splice(0,this.values.length)
                    }
                }else {
                    if (object) {
                        this.valueNames[this.valueIndex] = object.label;
                    }else{
                        this.values[this.valueIndex] = '';
                        this.valueNames[this.valueIndex] = '';
                    }
                    this.valueChecks()
                }
            }
        },
        created(){
            if(this.isInOpt) {
                this.tempValues.push(...this.values)
            }
            this.valueChecks()
        },
        watch:{
            tempValues(){
                this.valueChecks()
            },
        }
    }
</script>
