<template>
    <div style="float:left">
        <DatePicker v-if="!readOnly" v-model="tempDate" :style=style size="small" type="date" :placeholder="inputConfig.placeholder"></DatePicker>
        <span v-else style="font-size: 12px">{{values[valueIndex]}}</span>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    import dateUtil from '_c/iview/utils/date';
    export default {
        name: 'date-type',
        mixins: [ruiMixin],
        props: {
            values: {
                type: Array,
                require:true
            },
            valueIndex:{
                type:Number,
                require: true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            inputConfig:{
                type:Object,
                require:true
            }
        },
        data() {
            return {
                tempDate:null
            }
        },
        computed: {
            style(){
                let width=(this.inputConfig.placeholder?(this.inputConfig.placeholder===''?200:(this.inputConfig.placeholder.length*15)):200);
                if(width<150){
                    width=150;
                }
                return {
                    width:width+'px'
                }
            }
        },
        methods: {
            valueChecks(value) {
                if(this.values[this.valueIndex]==''){
                    this.$emit('checkError',true,'必须输入');
                    return
                }
                if(this.inputConfig.minDate&&value<this.inputConfig.minDate){
                    this.$emit('checkError',true,'不能小于'+this.inputConfig.minDate);
                    return
                }
                if(this.inputConfig.maxDate&&value>this.inputConfig.maxDate){
                    this.$emit('checkError',true,'不能大于'+this.inputConfig.maxDate);
                    return
                }
                this.$emit('checkError',false,'');
            }
        },
        watch:{
            tempDate:{
                handler(){
                    if(this.tempDate) {
                        this.values.splice(this.valueIndex, 1, dateUtil.format(this.tempDate,this.inputConfig.format));
                    }else{
                        this.values.splice(this.valueIndex, 1, '');
                    }
                    this.valueChecks(this.values[this.valueIndex])
                },
                immediate:true
            }
        }
    }
</script>
