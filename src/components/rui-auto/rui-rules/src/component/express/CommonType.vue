<template>
    <div style="float:left" :class="classes">
        <TextType @calexp="handle" @checkError="checkError" v-if="inputType=='text'"  :values="values" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <NumberType @calexp="handle" @checkError="checkError" v-if="inputType=='number'"  :values="values" :value-names="valueNames" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <DateType @calexp="handle" @checkError="checkError" v-if="inputType=='date'"  :values="values" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <DataDicType @calexp="handle" @checkError="checkError" :is-in-opt="isInOpt" v-if="inputType=='dic'"  :values="values" :value-names="valueNames" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <CustDicType @calexp="handle" @checkError="checkError" :is-in-opt="isInOpt" :atomKey="atomKey" v-if="inputType=='custDic'"  :values="values" :value-names="valueNames" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <CascaderType @calexp="handle" @checkError="checkError" :atomKey="atomKey" v-if="inputType=='cascader'"  :values="values" :value-names="valueNames" :valueIndex="valueIndex" :inputConfig="inputConfig" :read-only="readOnly"/>
        <div style="float:left" v-if="inputType==='component'" >
            <component ref="comRefs" v-if="!readOnly" :placeholder="inputConfig.placeholder" :is-in-opt="isInOpt" :values="values" :value-names="valueNames" :params="inputConfig.params" @valueChange="valueChange" :is="component"/>
            <span v-else style="font-size: 12px">{{valueNames.join(",")}}</span>
        </div>
        <Tooltip v-if="validateError&&!checkValue" :content="errorMessage" theme="light">
            &nbsp;&nbsp;<Icon type="ios-alert-outline" color="#ed4014" />
        </Tooltip>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    import TextType from './input/Text'
    import NumberType from './input/Number'
    import DateType from './input/Date'
    import DataDicType from './input/DataDic'
    import CustDicType from './input/CustDic'
    import CascaderType from './input/Cascader'
    import {fullPath, projectView} from "@/libs/lazyLoading";
    export default {
        name: 'common-type',
        mixins: [ruiMixin],
        components:{
            'TextType':TextType,
            'NumberType':NumberType,
            'DateType':DateType,
            'DataDicType':DataDicType,
            'CustDicType':CustDicType,
            'CascaderType':CascaderType,
        },
        props: {
            atomKey:{
                type:String,
                require: true
            },
            checkValue:{
                type:Boolean,
                default:false
            },
            values: {
                type: Array,
                require:true
            },
            valueNames: {
                type: Array,
                require:true
            },
            valueIndex:{
                type:Number,
                require: true
            },
            inputType:{
                type:String,
                require: true
            },
            inputConfig:{
                type:Object,
                require:true
            },
            isInOpt:{
                type:Boolean,
                default:false
            },
            emitValidate:{
                type:Boolean,
                default: false
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            location:{
                type:String,
                require:true
            }
        },
        data() {
            return {
                wrapperClass:'ivu-form-item-error',
                validateError:true,
                errorMessage:'不能为空'
            }
        },
        computed: {
            component(){
                if(this.inputConfig&&this.inputConfig.componentPath){
                    if(this.inputConfig.isFull==='1'){
                        return fullPath(this.inputConfig.componentPath);
                    }else{
                        return projectView(this.inputConfig.componentPath);
                    }
                }else{
                    return null;
                }
            },
            classes(){
                if(this.checkValue){return ''}
                else return this.wrapperClass;
            }
        },
        methods:{
            handle() {
                this.$emit('calexp');
            },
            checkError(validateError,messages){
                this.validateError = validateError;
                this.errorMessage = messages;
                if(validateError){
                    if(this.emitValidate){
                        this.$emit('validateExpress',this.location,false);
                    }
                    this.wrapperClass = 'ivu-form-item-error'
                }else{
                    if(this.emitValidate) {
                        this.$emit('validateExpress',this.location, true);
                    }
                    this.wrapperClass ='';
                }
            },
            valueChange(values,valueNames,validate,messages=''){
                this.errorMessage = messages;
                if(!validate){
                    if(this.emitValidate){
                        this.validateError = true;
                        this.$emit('validateExpress',this.location,false);
                    }
                    this.wrapperClass = 'ivu-form-item-error'
                }else{
                    this.validateError = false;
                    if(this.emitValidate) {
                        this.$emit('validateExpress',this.location, true);
                    }
                    this.wrapperClass ='';
                }
                if(this.isInOpt){
                    if(values){
                        this.values.splice(0,this.values.length)
                        this.values.push(...values)
                    }
                    if(valueNames){
                        this.valueNames.splice(0,this.valueNames.length)
                        this.valueNames.push(...valueNames)
                    }
                }else{
                    if(values){
                        this.values.splice(0,this.values.length)
                        this.values.push(values[0]);
                    }
                    if(valueNames){
                        this.valueNames.splice(0,this.valueNames.length)
                        this.valueNames.push(valueNames[0])
                    }
                }
            }
        },
        watch:{
            values:{
                handler(){
                    if(this.inputType==='component'&&this.values.length===0){
                        if(this.$refs.comRefs&&this.$refs.comRefs.resetValue){
                            this.$refs.comRefs.resetValue.apply();
                        }
                        this.checkError(true,"不能为空")
                    }
                },
                immediate:true
            }
        }
    }
</script>
