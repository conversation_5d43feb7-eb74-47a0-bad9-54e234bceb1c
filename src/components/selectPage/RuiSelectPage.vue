<!--
    下拉分页搜索 组件
    参数：
        value : v-model或v-bind 绑定 *必传
        initLabel : 初始显示值
        getDataFn : 下拉请求api *必传
        itemValue : 循环Option绑定的 value值 *必传
        itemLabel : 循环Option显示的 label值 *必传
        isDisable : 是否禁用
        isInit : 是否组件加载就请求 默认true
        multiple : 多选

    方法:  (参见:  https://www.iviewui.com/components/select#API)
        on-change
        on-select
        on-open-change 

    注意： 
        1. 请求的data为 {
                pageNumber: 1,
                pageSize: 10,
                condition: {
                    [itemLabel]: String,
                }
            }
            api需要使用此格式接收

        2. 额外数据  selectData  (option选中的完整数据) 可以在父组件用$refs 获取

-->
<template>
    <Select
        v-model="selectValue"
        placeholder
        :disabled="isDisable"
        :multiple="multiple"
        @on-change="selectChange"
        @on-select="selectSelect"
        @on-open-change="selectOpenChange"
    >
        <Input class="select-search" search enter-button placeholder="搜索" @on-search="search" />
        <Option v-for="(item,index) in list" :key="index" :value="item[itemValue].toString()">{{item[itemLabel]}}</Option>
        <template v-if="multiple">
            <Option v-for="(itm,idx) in selectValue" :key="itm" :value="itm" :data-value="itm" style="display:none;">{{selectLabel[idx]}}</Option>
        </template>
        <template v-else>
            <Option :value="selectValue" :data-value="selectValue" style="display:none;">{{selectLabel}}</Option>
        </template>
        <Option disabled v-show="list.length <= 0" value style="text-align: center;">无匹配数据</Option>
        <Page :total="total" @on-change="changePage" style="text-align: center;" simple />
        <Spin fix v-if="loading"></Spin>
    </Select>
</template>

<script>

export default {
    // 下拉分页 组件
    name: 'RuiSelectPage',
    props: {
        value: {
            type: [String, Array],
            required: true,
            default: ''
        },
        initLabel: {
            type: [String, Array],
            default: ''
        },
        getDataFn: {
            type: Function,
            required: true
        },
        itemValue: {
            type: String,
            required: true
        },
        itemLabel: {
            type: String,
            required: true
        },
        isDisable: {
            type: Boolean,
            default: false,
        },
        isInit: {
            type: Boolean,
            default: true,
        },
        multiple: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            selectData: null,
            selectValue: "",
            selectLabel: null,
            list: [],
            total: 0,
            loading: false,
            query: {
                pageNumber: 1,
                pageSize: 10,
                condition: {
                    [this.itemLabel]: '',
                }
            }
        }
    },
    computed: {
        // selectValue() {
        //     if (multiple) {
        //         if (Array.isArray(this.value))
        //             return this.value
        //         else
        //             return []
        //     } else {
        //         return this.value
        //     }
        // },

    },
    watch: {
        isInit(val) {
            this.init();
        },
        selectValue(val) {
            this.$emit('input', val)
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            if (this.isInit) {
                this.queryData();
                if (this.multiple) {
                    this.selectData = []
                    Array.isArray(this.value) ? this.selectValue = this.value : this.selectValue = [];
                    Array.isArray(this.initLabel) ? this.selectLabel = this.initLabel : this.selectLabel = [];
                }
            }
        },
        queryData() {
            let getDataFn = this.getDataFn;
            if (typeof getDataFn !== "function") return;
            this.loading = true;
            getDataFn(this.query).then(res => {
                this.loading = false;
                if (res.code === "0000" && res.data) {
                    const { records, total } = res.data
                    this.list = records;
                    this.total = total;
                    // console.log(this.selectValue, 'getDataFn', this.selectLabel)
                }
            });
        },
        changePage(num) {
            if (!num) return;
            this.query.pageNumber = num;
            this.queryData()
        },
        selectChange(v) {
            if (this.multiple) {
                let labs = [],
                    selectData = [];
                Array.isArray(v) && v.forEach(ele => {
                    let item = this.list.find(e => e[this.itemValue].toString() === ele);
                    if (item) {
                        labs.push(item[this.itemLabel])
                        selectData.push(item)
                    }
                })
                this.selectLabel = labs;
                this.selectData = selectData
            } else {
                let item = this.list.find(e => e[this.itemValue].toString() === v);
                console.log(item,"item");
                // console.log(v, typeof v, item);
                if (item) {
                    this.selectLabel = item[this.itemLabel]
                    this.selectData = item;
                }
            }
            // console.log(this.selectLabel);
            this.$emit("on-change", v)
        },
        search(v) {
           console.log(v,"v")
            this.query.condition[this.itemLabel] = v;
            this.queryData()
        },
        selectSelect(v) {
            this.$emit("on-select", v)
        },
        selectOpenChange(v) {
            this.$emit("on-open-change", v)
        }
    }

}
</script>

<style lang="less" scoped>
.select-search {
    padding: 7px 16px;
}
</style>
