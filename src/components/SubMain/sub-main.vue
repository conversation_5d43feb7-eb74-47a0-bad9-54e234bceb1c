<template>
    <lease-keep-alive :updateComponentsKey="setKey" ref="keepAlive">
        <router-view :key="key" ref="child"></router-view>
    </lease-keep-alive>
</template>
<script>
    import LeaseKeepAlive from '_c/keep-alive/LeaseKeepAlive'

    export default {
        name: 'ParentView',
        components: {
            LeaseKeepAlive
        },
        methods:{
            setKey (key) {
                //目前不做任何操作
            }
        },
        computed: {
            key() {
                return this.$route.name;
            }
        }
    }
</script>
