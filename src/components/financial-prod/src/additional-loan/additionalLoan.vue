<template>
    <div class="additionalLoan">
        <Form ref="additionalLoanForm" :model="additionalLoanForm" :rules="additionalLoanFormValiate" :label-width="90">
            <div class="common_content" style="padding:0px">
                 <Row>
                    <Col>
                        <common-finance
                            :isShowDetails="isShowDetails"
                            :isReconsider="isReconsider"
                            :diffType="diffType"
                            :financeForm="additionalLoanForm"
                            :isAdditional="isAdditional"
                            :isSubProduct="isSubProduct"
                            :selectProductInfo="selectProductInfo"
                            :selectCarProductInfo="selectCarProductInfo"
                            :financeFormValiate="additionalLoanFormValiate"
                            :queryProductInfo="queryProductInfo"
                            @deliverAddAmt="acceptAddAmt"
                            @clearAddAmt="resetAddAmt"
                            ref="commonfinance"
                            :mainProductId="mainProductId"
                            :isInternet="isInternet"
                            :enclosureAmt="enclosureAmt"
                             :inputPrice="inputPrice"
                        ></common-finance>
                        <div  class="addLoan" @click="addLoadDetail" :class="isShowLoanAmt ? 'active':''">
<!--                            <i class="addLoan-more" @click="addLoadDetail">></i>-->
                            <Icon type="ios-arrow-dropright-circle" size="16" color="#EB9620"/>
                            <span>贷款详情</span>
                        </div>
                    </Col>
                    <!-- <Col class="mb10">
                        <div style="padding-left: 140px;" v-if="additionalLoanForm.additionalAmtList.length>0" class="pr">
                            <div v-for="(item,index) in additionalLoanForm.additionalAmtList" style="display: inline-block;vertical-align:top ;">
                                {{item.extrasProjectName}} ({{item.extrasProjectAmt}})<span v-if="index!=additionalLoanForm.additionalAmtList.length-1">+</span>
                               <div v-if="item.itemList.length>0" style="padding-left: 20px;">
                                      <span v-for="(itemChild,indexChild) in item.itemList">
                                            {{itemChild.extrasProjectName}} ({{itemChild.extrasProjectAmt}})<span v-if="indexChild!=item.itemList.length-1">+</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </Col> -->
                </Row>
                <div v-for="(item,index) in additionalLoanForm.loadAmtList" :key="index" v-if="isShowLoanAmt">
                    <Row>
                        <Col span="16">
                            <FormItem label="租赁期限(期)" :prop="'loadAmtList['+index+'].loanLimit'" >
                                <InputNumber
                                :min="0"
                                class="wprecent100"
                                 v-model="item.loanLimit"
                                 :disabled="isShowDetails||diffType=='details'"
                                 @on-blur="inputLoanLimit(item.loanLimit)"
                                 @keyup.native="changeNumber"
                                 ></InputNumber>
                            </FormItem>
                        </Col>
                        <Col span="8" style="text-align: right;">
                            <FormItem label="" :label-width="0" style="margin-left: 10px;">
                                <p style="display:inline-block" class="term">
                                    默认区间:
                                    <span v-for="(item,index) in term" :key="index" :class="{active:curSpan===index}" @click="selectTerm(item,index)"><i>{{item}}</i><b v-show="index!=term.length-1">~</b></span>
                                </p>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row style="display: flex;">
                        <Col span="12">
                            <FormItem label="首付比例(%)" :prop="'loadAmtList['+index+'].downPayScale'">
                                <Input
                                v-model="item.downPayScale"
                                placeholder=""
                                :disabled="isShowDetails||diffType=='details'"
                                @on-blur="blurDownPayScale"
                                class="wprecent100"/>
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="首付金额(元)"  :prop="'loadAmtList['+index+'].downPayAmt'" style="margin-left: 10px;">
                                <Input
                                v-model="item.downPayAmt"
                                placeholder=""
                                :disabled="isShowDetails||diffType=='details'"
                                @on-blur="blurDownPayAmt"
                                class="wprecent100"
                                />
                                <input type="hidden" :value="downPayAmt"/>
                            </FormItem>
                        </Col>
                    </Row>
                    <Row v-if="item.isAdditionalTail" style="display: flex;">
                        <Col span="12">
                            <FormItem label="尾款比例(%)"  :prop="'loadAmtList['+index+'].tailPayScale'">
                                <Input
                                v-model="item.tailPayScale"
                                placeholder=""
                                @on-change="inputPayScale('tailPayScale',index)"
                                :disabled="isShowDetails||diffType=='details'||isReconsider"
                                class="wprecent100"
                                />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="尾款金额(元)"  :prop="'loadAmtList['+index+'].tailPayAmt'" style="margin-left: 10px;">
                                <Input
                                v-model="item.tailPayAmt"
                                placeholder=""
                                @on-change="inputPayAmt('tailPayAmt',index)"
                                @on-blur="blurPayAmt('tailPayAmt',index)"
                                :disabled="isShowDetails||diffType=='details'||isReconsider"
                                class="wprecent100"
                                />
                            </FormItem>
                        </Col>
                    </Row>
                    <Row>
                        <Col>
                            <FormItem label="租赁金额(元)"  :prop="'loadAmtList['+index+'].loanAmt'">
                                <Input
                                v-model="item.loanAmt"
                                placeholder=""
                                :disabled="isShowDetails||diffType=='details'"
                                class="wprecent100"
                                @on-blur="blurLoanAmt"

                                />
                                <input type="hidden" :value="loanAmt"/>
                            </FormItem>
                        </Col>
                    </Row>
                </div>
            </div>
        </Form>
        <Spin size="large" fix v-if="loading" class="apply-spin"></Spin>
    </div>
</template>
<script>
import {
    getProductRate,
    getCustRate
} from "@/projects/basic/api/product/financialProducts.js"
import vueEvent from "_p/basic/assets/js/vueEvent.js"
import {
    multiply,
    divided
} from "_p/basic/assets/js/utils.js"
import * as utils from "@/projects/basic/assets/js/utils.js"
import CommonFinance from "../common-finance/common-finance"
export default {
    name:"additionalLoan",
    data(){
        let validateTerm=(rule,value,callback)=>{
            if(value){
               if(value<this.term[0]||value>this.term[1]){
                    callback(new Error("请输入"+(this.term[0]||"")+"~"+(this.term[1]||"")+"期数"));
                    return;
               }
               if(this.valiateFlag){
                   if(value>this.queryProductInfo.costInfo.loanTerm){
                     callback(new Error("租赁期限不能变长"));
                     return;
                   }
               }
            }
            callback();
        }
        let valiateTailPayScale=(rule,value,callback)=>{
            if(value){
                if(value<=this.endBalancePaymentRange&&value>=this.fromBalancePaymentInterval){
                    if(this.additionalLoanForm.loadAmtList[0].downPayScale){
                        if(utils.add(value,this.additionalLoanForm.loadAmtList[0].downPayScale)>100){
                            callback(new Error("尾付与首付比例之和需小于等于100"))
                            return;
                        }
                    }
                }else if(value>this.endBalancePaymentRange||value<this.fromBalancePaymentInterval){
                    callback(new Error('请输入'+this.fromBalancePaymentInterval+'~'+this.endBalancePaymentRange+'的尾款比例'))
                    return;
                }

            }
            callback();
        }
        let valiateTailPayAmt=(rule,value,callback)=>{
            if(value){
                if(value>this.endBalancePaymentRange||value<this.fromBalancePaymentInterval){
                    callback(new Error('请输入'+this.fromBalancePaymentInterval+'~'+this.endBalancePaymentRange+'的尾款金额'))
                    return;
                }
                if(parseFloat(value)>parseFloat(this.additionalLoanForm.loadAmtList[0].loanAmt)){
                    callback('尾款金额应小于等于租赁金额');
                    return;
                }
            }
            callback();
        }
        let checkTailPayAmt=(rule,value,callback)=>{
            if(value){
                if(parseFloat(value)>parseFloat(this.additionalLoanForm.loadAmtList[0].loanAmt)){
                    callback('尾款金额应小于等于租赁金额');
                    return;
                }
            }
            callback()
        }
        let valiateDownPayScale=(rule,value,callback)=>{
            if(value){
                if(value<=100&&value>=parseFloat(this.downPayRatioMin)){
                    if(this.additionalLoanForm.loadAmtList[0].tailPayScale){
                        if(utils.add(value,this.additionalLoanForm.loadAmtList[0].tailPayScale)>100){
                            callback(new Error("首付与尾款比例之和需小于等于100"))
                            return;
                        }
                    }
                }else if(value>100||value<parseFloat(this.downPayRatioMin)){
                    callback(new Error('最低首付比例为'+this.downPayRatioMin+'%','最高首付比例为100%'))
                    return;
                }
            }
            callback();
        }
        let valiateLoanAmt=(rule,value,callback)=>{
            if(value){
                if(!utils.isTwoDicmal(value)) {
                    callback(new Error("最多为两位小数"))
                    return;
                }
                if(this.valiateFlag){
                    if(parseFloat(value)>parseFloat(this.queryProductInfo.costInfo.loanAmt)){
                        callback(new Error("贷额不能增加"))
                        return;
                    }
                }
                if(this.isReconsider){
                    if(parseFloat(value)>parseFloat(this.queryProductInfo.costInfo.loanAmt)){
                        callback(new Error("租赁金额不能增加"))
                        return;
                    }
                }
                // if(this.additionalLoanForm.loadAmtList[0].tailPayAmt){
                //     if(parseFloat(value)<parseFloat(this.additionalLoanForm.loadAmtList[0].tailPayAmt)){
                //         callback(new Error("租赁金额应大于等于尾款金额"))
                //         return;
                //     }
                // }
            }
            callback();
        }
        let valitateDownPayScale=(rule,value,callback)=>{
            if(value){
                if(!utils.isTwoDicmal(value)) {
                    callback(new Error("最多为两位小数"))
                    return;
                }
                if(this.valiateFlag){
                    console.log(value,this.queryProductInfo,"111")
                    if(parseFloat(value)<parseFloat(this.queryProductInfo.costInfo.downPayScale)){
                        callback(new Error("首付比例不能降低"))
                        return;
                    }
                }
            }
            callback();
        }
        return{
            loading:false,
            tempLoanLimit:0,
            maxAmt:"",
            tempRateList:[],
            fromBalancePaymentInterval:"",
            endBalancePaymentRange:"",
            finalPaymentBenchmark:"",
            downPayRatioMin:"",
            valiateTailPayScale:valiateTailPayScale,
            checkTailPayAmt:checkTailPayAmt,
            valiateTailPayAmt:valiateTailPayAmt,
            valiateDownPayScale:valiateDownPayScale,
            valitateDownPayScale:valitateDownPayScale,
            isTail:"",
            curSpan:"",
            term:[
                //   12,24
            ],
            isShowLoanAmt:true,
            additionalLoanForm:{
                additionalTotalAmt:"0",
                //项目融资列表
                additionalAmtList:[
                ],
                loadAmtList:[
                    {
                        loanLimit:0,
                        downPayScale:"",
                        downPayAmt:"",
                        tailPayScale:"",
                        tailPayAmt:"",
                        loanAmt:"",
                        isAdditionalTail:false,
                    }
                ]
            },
            additionalLoanFormValiate:{
                additionalTotalAmt:[
                    {required: true, message: "附加租赁项金额不能为空", trigger: "change"},
                    {validator:utils.isTwoDicmalValiate,trigger:"change"}
                ],
                "loadAmtList[0].loanLimit":[
                    {required: true, message:"租赁期限不能为空", trigger: "blur",type:"number"},
                    {validator:validateTerm,trigger: "blur",type:"number"},
                ],
                "loadAmtList[0].downPayScale":[
                    {required: true, message: "首付比例不能为空", trigger: "blur"},
                    {validator:valitateDownPayScale,trigger:"blur"}
                ],
                "loadAmtList[0].downPayAmt":[
                   {required: true, message: "首付金额不能为空", trigger: "blur"},
                   {validator:utils.isTwoDicmalValiate,trigger:"blur"}
                ],
                "loadAmtList[0].tailPayScale":[
                    {required: true, message: "尾款比例不能为空", trigger: "blur"},
                    {validator:utils.isTwoDicmalValiate,trigger:"blur"},
                ],
                "loadAmtList[0].tailPayAmt":[
                    {required: true, message: "尾款金额不能为空", trigger: "blur"},
                    {validator:utils.isTwoDicmalValiate,trigger:"blur"},
                ],
                "loadAmtList[0].loanAmt":[
                    {required: true, message: "租赁金额不能为空", trigger: "blur"},
                    {validator:valiateLoanAmt,trigger:"blur"}
                ]
            },

            decorateModel:false,//装潢产品的弹窗
            decorateForm:{
                prodName:"",
                checkedDecorProd:[],
            },
            totalDecorList:[

            ],
        }
    },
    computed:{
        loanAmt:function(){
            let remainAmt=0;
            remainAmt=utils.sub(this.additionalLoanForm.additionalTotalAmt,this.additionalLoanForm.loadAmtList[0].downPayAmt);
            // this.additionalLoanForm.loadAmtList[0].loanAmt=remainAmt.toString();
            vueEvent.$emit('to-additionalLoanAmt',remainAmt,this.uuid);
            // if(this.$refs.additionalLoanForm&&this.additionalLoanForm.loadAmtList[0].loanAmt){
            //     let _this=this;

            //     this.$nextTick(()=>{
            //         _this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanAmt');
            //     })
            // }

            return remainAmt;
        },
        downPayAmt:function(){
            let payAmt=this.additionalLoanForm.loadAmtList[0].downPayAmt;
            vueEvent.$emit('to-additionalDownPayAmt',payAmt,this.uuid);
            return payAmt
        },

    },
    props:{
        selectProductInfo:{
            type:Object,
        },
        selectCarProductInfo:{
            type:Object
        },
        queryProductInfo:{
            type:Object,
        },
        additionalData:{
            type:Object,
        },
        uuid:{
            type:String,
        },
        isShowDetails:{
            type:Boolean,
        },
        isReconsider:{
            type:Boolean,
        },
        diffType:{
            type:String,
        },
        isAdditional:{
            type:String
        },
        isSubProduct:{
            type:String
        },
        addProductId:{
            type:String,
        },
        mainProductId:{
            type:String,
        },
        applyNo:{
            type:String,
        },
        saveStatus:{
            type:Object,
        },
        valiateFlag:{
            type:Boolean,
        },
        queryAddLoanRebate:{
            type:Function
        },
        isInternet:{
            type:String
        }
    },
    components:{
        CommonFinance,
    },
    watch:{
        selectProductInfo(val){
            if(Object.keys(val).length>0){
                let tempPeriod=[];
                tempPeriod[0]=val.detailInfo.loanPeriodMin;
                tempPeriod[1]=val.detailInfo.loanPeriodMax;
                this.term=tempPeriod;
                 // 尾款存在
                if(val.isTail=='1'){
                    this.fromBalancePaymentInterval=val.repaymentData.repaymentMethods.fromBalancePaymentInterval;
                    this.endBalancePaymentRange=val.repaymentData.repaymentMethods.endBalancePaymentRange;
                    this.finalPaymentBenchmark=val.repaymentData.repaymentMethods.finalPaymentBenchmark;
                    this.additionalLoanForm.loadAmtList[0].isAdditionalTail=true;
                    //  比例
                    if(val.repaymentData.repaymentMethods.intervalSelectionFinalPayment=='0'){
                        this.additionalLoanFormValiate['loadAmtList[0].tailPayScale'].push({
                            validator: this.valiateTailPayScale,trigger:'blur'
                        })
                        this.additionalLoanFormValiate['loadAmtList[0].tailPayAmt'].push({
                            validator: this.checkTailPayAmt,trigger:'blur'
                        })

                    }else if(val.repaymentData.repaymentMethods.intervalSelectionFinalPayment=='1'){
                        this.additionalLoanFormValiate['loadAmtList[0].tailPayAmt'].push({
                            validator: this.valiateTailPayAmt,trigger:'blur'
                        })
                    }
                }else if(val.isTail=='0'){
                    this.additionalLoanForm.loadAmtList[0].isAdditionalTail=false;
                }
                this.downPayRatioMin=val.detailInfo.downPayRatioMin;
                this.additionalLoanFormValiate['loadAmtList[0].downPayScale'].push({
                    validator: this.valiateDownPayScale,trigger:'blur'
                })
            }
        },
        queryProductInfo(val){
            if(Object.keys(val).length>0){
                this.queryAdditionalLoadInfo(val);
            }
        },
    },
    creatd(){
    },
    mounted(){
        let _this=this;
    },
    beforeDestroy () {
    },
    methods:{
        acceptAddAmt(val){
            vueEvent.$emit('to-additionalPrice',val,this.uuid);
        },
         resetAddAmt(val){
            this.inputPrice()
            vueEvent.$emit('to-additionalPrice',val,this.uuid);
        },
        changeNumber() {
            document.getElementsByClassName('ivu-input-number')[0].getElementsByTagName('input')[0].addEventListener('keyup', (e) => {
                let str = '' + e.target.value;
                if (str.indexOf('.') != -1) {
                    let arr = str.split('');
                    console.log(arr,"arr")
                    arr.splice(arr.length - 1);
                    let str2 = arr.join('');
                    e.target.value = +str2;
                }
            })
        },
         // 初始化融资列表校验
        initCheckFinanceItem() {
            for (let i = 0; i < this.additionalLoanForm.additionalAmtList.length; i++) {
                this.additionalLoanFormValiate["additionalAmtList[" + i + "].extrasProjectAmt"] = [
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"blur"}
                ];
            }
        },
        // 回显附加租赁项模块信息
        queryAdditionalLoadInfo(val){
            this.additionalLoanForm.additionalTotalAmt=val.costInfo.contractAmt.toString();
            vueEvent.$emit('to-additionalPrice',val.costInfo.contractAmt,this.uuid);
            // this.matchRate(val.costInfo.loanTerm);
            this.additionalLoanForm.loadAmtList[0].loanLimit=val.costInfo.loanTerm;
            this.tempLoanLimit=val.costInfo.loanTerm;
            this.additionalLoanForm.loadAmtList[0].downPayScale=val.costInfo.downPayScale.toString();
            this.additionalLoanForm.loadAmtList[0].downPayAmt=val.costInfo.downPayAmt.toString();
            if(val.isTail=="1"){
                this.additionalLoanForm.loadAmtList[0].tailPayScale=val.costInfo.tailPayScale.toString();
                this.additionalLoanForm.loadAmtList[0].tailPayAmt=val.costInfo.tailPayAmt.toString();
            }
            this.additionalLoanForm.loadAmtList[0].loanAmt=val.costInfo.loanAmt.toString();
            this.isShowLoanAmt=true;
            this.maxAmt=val.costInfo.maxDiscountAmt;
        },
        addLoadDetail(){
            this.isShowLoanAmt=!this.isShowLoanAmt;
        },
        selectTerm(item,index){
            if(!this.isShowDetails&&this.diffType!='details'){
                this.curSpan=index;
                let loanLimit=this.additionalLoanForm.loadAmtList[0].loanLimit;
                this.additionalLoanForm.loadAmtList[0].loanLimit=parseFloat(item);
                this.tempLoanLimit=parseFloat(item);
                this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanLimit',(errorMsg)=>{
                    if(!errorMsg){
                        if(loanLimit!=item){
                            this.matchRate(item).then((data)=>{
                                if(data){
                                    this.enclosureAmt();
                                }
                            });
                        }
                    }
                });
            }
        },
        inputLoanLimit(item){
            console.log(this.tempLoanLimit,item,"9999")
            this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanLimit',(errorMsg)=>{
                if(!errorMsg){
                    if(this.tempLoanLimit!=item){
                        this.matchRate(item).then((data)=>{
                            if(data){
                                this.enclosureAmt();
                            }
                        });
                    }
                }
            });
            this.tempLoanLimit=item;
        },
        resetLoanLimit(){
            this.tempLoanLimit=0;
        },
        // 首付比例失去焦点
        blurDownPayScale(){
            this.$refs.additionalLoanForm.validateField('loadAmtList[0].downPayScale',(errorMsg)=>{
                if(!errorMsg){
                     this.inputPayScale('downPayScale',0)
                }
            });
        },
        // 首付金额失去焦点
        blurDownPayAmt(){
            this.$refs.additionalLoanForm.validateField('loadAmtList[0].downPayAmt',(errorMsg)=>{
                if(!errorMsg){
                     this.inputPayAmt('downPayAmt',0)
                }
            });
        },
        // 租赁金额失去焦点
        blurLoanAmt(){
            this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanAmt',(errorMsg)=>{
                if(!errorMsg){
                    this.inputLoanAmt(this.additionalLoanForm.loadAmtList[0].loanAmt,0);
                }
            });
        },
        // 当租赁金额，跟租赁期限改变都调此接口
       enclosureAmt(){
            let loanRebate=this.queryAddLoanRebate();
            let selectInfo=this.selectProductInfo;
            if(selectInfo.discountOption=="2"){
                if(this.additionalLoanForm.loadAmtList[0].loanLimit==''||(this.additionalLoanForm.loadAmtList[0].loanLimit<this.term[0]||this.additionalLoanForm.loadAmtList[0].loanLimit>this.term[1])){
                    //  this.$Message.warning("请输入"+(this.term[0]||"")+"~"+(this.term[1]||"")+"期数")
                    return false;
                }
                if(this.additionalLoanForm.loadAmtList[0].loanAmt==''||!utils.isTwoDicmal(this.additionalLoanForm.loadAmtList[0].loanAmt)){
                    //  this.$Message.warning("租赁金额不能为空且为两位小数")
                    return false;
                }
                for(let i=0;i<loanRebate.loadList.length;i++){
                    if(!loanRebate.loadList[i].discountId||loanRebate.loadList[i].discountAmt===""){
                        // this.$Message.warning("贴息列表需填写完整")
                        return false;
                    }
                }
                for(let i=0;i<loanRebate.loadList.length;i++){
                    for(let j=0;j<selectInfo.discountInfo.subsidyList.length;j++){
                        // 匹配id校验
                        if(loanRebate.loadList[i].discountId==selectInfo.discountInfo.subsidyList[j].subsidySide){
                            if(selectInfo.discountInfo.subsidyList[j].singleSelect&&(selectInfo.discountInfo.subsidyList[j].singleSelect=='0'||selectInfo.discountInfo.subsidyList[j].singleSelect=='1')){
                                if(loanRebate.loadList[i].discountId!=0){
                                    if(loanRebate.loadList[i].discountAmt>selectInfo.discountInfo.subsidyList[j].subsidyMoney){
                                        this.$Message.warning("贴息额不能大于最高贴息额")
                                        return false;
                                    }
                                }else{
                                    if(loanRebate.loadList[i].discountAmt>loanRebate.maxDisCountAmt){
                                        this.$Message.warning('贴息额不能大于厂商最高贴息额');
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }

                let totalAmt=0;
                let discountList=[];
                // if(type=='loanLimit'&&this.maxAmt){
                if(this.maxAmt){
                    let totalAmt=this.maxAmt;
                    for(let i=0;i<loanRebate.loadList.length;i++){
                        let tempobj={};
                        if(loanRebate.loadList[i].discountId!='0'){
                            totalAmt=utils.add(totalAmt,loanRebate.loadList[i].discountAmt);
                            tempobj.discountAmt=loanRebate.loadList[i].discountAmt;
                        }else{
                            tempobj.discountAmt=this.maxAmt;
                        }
                        tempobj.discountId=loanRebate.loadList[i].discountId
                        discountList.push(tempobj);

                    }
                }else{
                      for(let i=0;i<loanRebate.loadList.length;i++){
                        let tempobj={};
                        totalAmt=utils.add(totalAmt,loanRebate.loadList[i].discountAmt);
                        tempobj.discountId=loanRebate.loadList[i].discountId
                        tempobj.discountAmt=loanRebate.loadList[i].discountAmt;
                        discountList.push(tempobj);
                    }
                }
                let param={};
                param.productId=selectInfo.baseInfo.id;
                param.loanAmt=this.additionalLoanForm.loadAmtList[0].loanAmt;
                param.loanTerm=this.additionalLoanForm.loadAmtList[0].loanLimit;
                param.settleRate=loanRebate.settleRate;
                param.discountAmt=totalAmt;
                param.discountList=discountList;
                getCustRate(param).then(res=>{
                    if(res.code=="0000"){
                        if(res.data.custRate||res.data.custRate==0){
                            loanRebate.custRate=res.data.custRate;
                             if(res.data.discountList.length>0){
                                vueEvent.$emit('to-discountList',res.data.discountList,this.uuid);
                            }
                        }
                    }
                })
            }
        },
         // 匹配rate值
         matchRate(loanLimit){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            let obj={
                applyNo:this.applyNo,
                id:this.mainProductId,
                addId:this.addProductId,
                loanTerm :loanLimit,
                costType:"02",
            }
            this.loading=true;
            return new Promise((resolve, reject) => {
                getProductRate(obj).then(res=>{
                      this.loading=false;
                    if(res.code=="0000"){
                         this.$Message.success("获取客户利率成功");
                        if(res.data){
                            if(res.data.son){
                                let maxdiscountAmt="";
                                if(res.data.sonDiscount.discountOption!=0){
                                    maxdiscountAmt=res.data.sonDiscount.discountInfo.maxdiscountAmt;
                                    this.maxAmt=res.data.sonDiscount.discountInfo.maxdiscountAmt;
                                }
                                vueEvent.$emit('to-additionalLoanRate',res.data.son.custRate,res.data.son.clearRate,res.data.son.addPointValue,maxdiscountAmt,this.uuid);
                            }else{
                                vueEvent.$emit('to-additionalLoanRate','','','','',this.uuid);
                            }
                            resolve(res.data)
                        }
                    }else{
                         this.loading=false;
                         reject(res.message)
                    }
                })
            })
        },
        // 输入比例
        inputPayScale(type,index){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            if(utils.isTwoDicmal(this.additionalLoanForm.loadAmtList[0][type])){
                if(type=="downPayScale"){
                   let  multiplyAmt=multiply(this.additionalLoanForm.additionalTotalAmt,divided(this.additionalLoanForm.loadAmtList[0][type],100));
                //    let payAmt=Math.round(multiplyAmt*100)/100;
                    let payAmt=Math.ceil(multiplyAmt*100)/100
                   this.additionalLoanForm.loadAmtList[0].downPayAmt=payAmt.toString();
                   this.computedLoanAmt(this.additionalLoanForm.loadAmtList[0].downPayAmt)
                }else if(type=="tailPayScale"){
                    let multiplyAmt="";
                    if(this.finalPaymentBenchmark=="0"||this.finalPaymentBenchmark=="2"){
                        this.$refs.additionalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                            if(!totalAmtError){
                                 multiplyAmt=multiply(this.additionalLoanForm.additionalTotalAmt,divided(this.additionalLoanForm.loadAmtList[0][type],100));
                            }
                        })
                    }else  if(this.finalPaymentBenchmark=="1"||this.finalPaymentBenchmark=="3"){
                        this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanAmt',(loanAmtError)=>{
                            if(!loanAmtError){
                                 multiplyAmt=multiply(this.additionalLoanForm.loadAmtList[0].loanAmt,divided(this.additionalLoanForm.loadAmtList[0][type],100));
                             }
                        })
                    }
                    let payAmt=Math.round(multiplyAmt*100)/100;
                    this.additionalLoanForm.loadAmtList[0].tailPayAmt=payAmt.toString();
                }
            }
            if(type=="downPayScale"){
                this.$refs.additionalLoanForm.validateField('loadAmtList[0].downPayAmt');
                if(this.additionalLoanForm.loadAmtList[0].isAdditionalTail){
                    if(this.additionalLoanForm.loadAmtList[0].tailPayScale){
                        if(utils.add(this.additionalLoanForm.loadAmtList[0].tailPayScale,this.additionalLoanForm.loadAmtList[0].downPayScale)<=100){
                            this.$refs.additionalLoanForm.validateField('loadAmtList[0].tailPayScale');
                        }
                    }
                }
            }else if(type=="tailPayScale"){
                this.$refs.additionalLoanForm.validateField('loadAmtList[0].tailPayAmt');
                if(this.additionalLoanForm.loadAmtList[0].downPayScale){
                    if(utils.add(this.additionalLoanForm.loadAmtList[0].downPayScale,this.additionalLoanForm.loadAmtList[0].tailPayScale)<=100){
                        this.$refs.additionalLoanForm.validateField('loadAmtList[0].downPayScale');
                    }
                }
            }
        },
        // 输入金额
        inputPayAmt(type,index){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            if(this.additionalLoanForm.loadAmtList[0][type]){
                if(type=="downPayAmt"){
                    let multiplyScale=multiply(divided(this.additionalLoanForm.loadAmtList[0][type],this.additionalLoanForm.additionalTotalAmt),100);
                    // let payScale=Math.round(multiplyScale*100)/100;
                    let payScale=Math.floor(multiplyScale*100)/100
                    this.additionalLoanForm.loadAmtList[0].downPayScale=payScale.toString();
                    this.computedLoanAmt(this.additionalLoanForm.loadAmtList[0][type]);
                }else if(type=="tailPayAmt"){
                    let multiplyScale=""
                    if(this.finalPaymentBenchmark=="0"||this.finalPaymentBenchmark=="2"){
                        this.$refs.additionalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                            if(!totalAmtError){
                                multiplyScale=multiply(divided(this.additionalLoanForm.loadAmtList[0][type],this.additionalLoanForm.additionalTotalAmt),100);
                            }
                        })
                    }else if(this.finalPaymentBenchmark=="1"||this.finalPaymentBenchmark=="3"){
                        this.$refs.additionalLoanForm.validateField('loadAmtList[0].loanAmt',(loanAmtError)=>{
                            if(!loanAmtError){
                                multiplyScale=multiply(divided(this.additionalLoanForm.loadAmtList[0][type],this.additionalLoanForm.loadAmtList[0].loanAmt),100);
                            }
                        })
                    }
                    let payScale=Math.round(multiplyScale*100)/100;
                    this.additionalLoanForm.loadAmtList[0].tailPayScale=payScale.toString();
                }
            }
            if(type=="downPayAmt"){
                this.$refs.additionalLoanForm.validateField('loadAmtList[0].downPayScale');
            }else if(type=="tailPayAmt"){
                this.$refs.additionalLoanForm.validateField('loadAmtList[0].tailPayScale');
            }
        },
         // 输入附加金额
         inputPrice(){
            if(utils.isTwoDicmal(this.additionalLoanForm.additionalTotalAmt)){
                if(this.additionalLoanForm.loadAmtList[0].downPayScale){
                    this.inputPayScale('downPayScale')
                }
                if(this.additionalLoanForm.loadAmtList[0].tailPayScale){
                    this.inputPayScale('tailPayScale')
                }
            }
        },
        // 当尾款金额与租赁金额不相同，但尾款比例与首付比例为100,触发此事件将尾款金额赋值给租赁金额
        blurPayAmt(tailPayAmt,index){
            if(this.finalPaymentBenchmark=='0'||this.finalPaymentBenchmark=='2'){
                this.$refs.additionalLoanForm.validateField('tailPayAmt',(tailPayAmttError)=>{
                    if(!tailPayAmttError){
                        if(parseFloat(this.additionalLoanForm.loadAmtList[index].tailPayAmt)!=parseFloat(this.additionalLoanForm.loadAmtList[index].loanAmt)){
                            if(utils.add(this.additionalLoanForm.loadAmtList[index].downPayScale,this.additionalLoanForm.loadAmtList[index].tailPayScale)==100){
                                this.inputLoanAmt(this.additionalLoanForm.loadAmtList[index].tailPayAmt);
                            }
                        }
                    }
                })
            }
        },
        // 计算租赁金额
        computedLoanAmt(downPayAmt){
            let remainAmt=0;
            remainAmt=utils.sub(this.additionalLoanForm.additionalTotalAmt,downPayAmt);
            this.additionalLoanForm.loadAmtList[0].loanAmt=remainAmt.toString();
            this.enclosureAmt();
             // 当租赁金额改变时 finalPaymentBenchmark=1 尾款金额改变
            // 当租赁金额改变时首付比例改变，首付比例改变，finalPaymentBenchmark=3，尾款金额改变
            if(this.finalPaymentBenchmark=1||this.finalPaymentBenchmark==3){
                if(this.additionalLoanForm.loadAmtList[0].tailPayScale){
                    this.inputPayScale('tailPayScale')
                }
            }
        },
        // 输入租赁金额
        inputLoanAmt(loanAmt,index){
            this.additionalLoanForm.loadAmtList[index].downPayAmt=utils.sub(this.additionalLoanForm.additionalTotalAmt,loanAmt).toString();
            this.inputPayAmt('downPayAmt',index);
            if(this.finalPaymentBenchmark==1||this.finalPaymentBenchmark==3){
                if(this.additionalLoanForm.loadAmtList[0].tailPayScale){
                    this.inputPayScale('tailPayScale')
                }
            }
        },
        submitAdditionalLoan(){
            let addValiate=false;
            console.log(addValiate,'submitAdditionalLoan');
            console.log(this.$refs.additionalLoanForm,this.$refs.additionalLoanForm.validate(),"additionalLoanForm")
            this.$refs.additionalLoanForm.validate((valid) => {
                console.log(valid,"valid-additionalLoan")
                if(valid){
                    addValiate=valid;
                }else{
                    addValiate=false;
                }
            })
            if(addValiate){
                 return this.additionalLoanForm
            }else{
                return false;
            }
        },
        // 清空数据
        resetAdditionalLoan(){
            this.$refs["additionalLoanForm"].resetFields();
            this.$refs.commonfinance.resetTotalAmt();
            vueEvent.$emit('to-additionalPrice',0,this.uuid);
        }
    }
}
</script>
<style scoped>
.additionalLoan /deep/ .ivu-input-number-handler-wrap{
    display: none;
}
.w400{
    width:400px;
}
.addLoan{
    margin-left: 10px;
    position: absolute;
    top: 4px;
    right: 0px;
    cursor: pointer;
}
.addLoan i {
    transition: all linear .1s;
}
.addLoan.active i {
    transform: rotate(90deg);
}
.arrow_icon{
    line-height: 24px;
}
.arrow_icon>span{
    position: absolute;
    right: 9px;
    top: 2px;
    cursor: pointer;
}
.amt_list_type i,.itemamt_list_type i{
    width: 20px;
    height: 20px;
    background:#EB9620;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;
    line-height: 15px;
    text-align: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
}
.addLoan-more {

}
.amt_total{
    border: 1px solid rgb(235,236,240);
    background: white;
    position: absolute;
    z-index: 99;
    width: 100%;
    padding: 10px;
}
.amt_list_type i{
    position: absolute;
    top: 6px;
    right: 0;
}
.term span{
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    line-height: 20px;
    margin-left: 5px;
    text-align: center;
    cursor: pointer;
}
.term span>i{
    font-style: normal;
    height: 20px;
    width: 20px;
    display: inline-block;
}
.term span.active>i,.term span>i:hover{
    background: #EB9620;
    color: white;
}
.decorateProd>h2{
    font-weight: normal;
    text-align: center;
}
.decorateProd label{
    display: block;
}

</style>
