<template>
    <div class="loadInfo mb10">
        <Card>
            <div class="load_content">
                <h2 class="mb10">租赁信息</h2>
                <div style="display: flex;" class="load_container">
                    <div>
                        <span>车款</span>
                        <car-loan-discount
                        :discountOption="carDiscountOption"
                        :selectCarProductInfo="selectCarProductInfo"
                        :carMonthPay="carMonthPay"
                        :carMonthlyRate="carMonthlyRate"
                        :carMyriad="carMyriad"
                        :queryCarProductInfo="queryCarProductInfo"
                        :discountList="discountList"
                        ref="carLoanDiscount"
                        :carLoan="carLoan"
                        :valiateCarLoan="valiateCarLoan"
                        :valiateCarAddLoan="valiateCarAddLoan"
                        :isAdditional="isAdditional"
                        :isSubProduct="isSubProduct"
                        :uuid="uuid"
                        :saveStatus="saveStatus"
                        :isShowDetails="isShowDetails"
                        :isReconsider="isReconsider"
                        :diffType="diffType"
                        ></car-loan-discount>
                    </div>
                    <div v-if="isAdditional=='1'&&isSubProduct=='1'">
                        <span>附加金额</span>
                        <additional-loan-discount
                        :discountOption="additionalDiscountOption"
                        :selectProductInfo="selectProductInfo"
                        :queryProductInfo="queryProductInfo"
                        :addMonthPay="addMonthPay"
                        :addMonthlyRate="addMonthlyRate"
                        :addMyriad="addMyriad"
                        :discountList="discountList"
                        ref="additionalLoanDiscount"
                        :additionalLoan="additionalLoan"
                        :valiateAdditionalLoan="valiateAdditionalLoan"
                        :uuid="uuid"
                        :saveStatus="saveStatus"
                        :isShowDetails="isShowDetails"
                        :isReconsider="isReconsider"
                        :diffType="diffType"
                        ></additional-loan-discount>
                    </div>
                </div>
            </div>
        </Card>
    </div>
</template>
<script>
import CarLoanDiscount from "./car-loan-discount/carLoanDiscount.vue"
import AdditionalLoanDiscount from "./additional-loan-discount/additonalLoanDiscount.vue"
import {
    getDictDataByType
} from "_p/basic/api/admin/datadic.js";
export default {
    name:"loadInfo",
    data(){
        return{
            carLoan:"car",
            additionalLoan:"additional",
            loanType:[
                {
                    title:"车款",
                },
                {
                    title:"附加金额"
                }
            ],
            discountList:[],
        }
    },
    components:{
        CarLoanDiscount,
        AdditionalLoanDiscount
    },
    props:{
        uuid:{
            type:String,
        },
        isAdditional:{
            type:String
        },
        isSubProduct:{
            type:String,
        },
        carDiscountOption:{
            type:String
        },
        carMonthPay:{
            type:Number,
        },
        carMonthlyRate:{
            type:String
        },
        carMyriad:{
            type:String
        },
        addMonthPay:{
            type:Number,
        },
        addMonthlyRate:{
            type:String,
        },
        addMyriad:{
            type:String
        },
        additionalDiscountOption:{
            type:String
        },
        selectCarProductInfo:{
            type:Object
        },
        selectProductInfo:{
            type:Object
        },
        queryProductInfo:{
            type:Object
        },
        queryCarProductInfo:{
            type:Object
        },
        valiateCarLoan:{
            type:Function
        },
        valiateAdditionalLoan:{
            type:Function
        },
        valiateCarAddLoan:{
            type:Function
        },
        saveStatus:{
            type:Object
        },
        isShowDetails:{
            type:Boolean,
        },
        isReconsider:{
            type:Boolean,
        },
        diffType:{
            type:String
        },
    },
    created(){
    },
    watch:{
    },
    mounted(){
        this.initDiscountList();
    },
    computed: {

    },
    methods:{
        tabLoan(type){
            // this.curLoan=type;
        },
        initDiscountList(){
            let param = {
                type: "subsidySideType"
            }
            getDictDataByType(param.type).then(res => {
                if (res.code === "0000") {
                    this.discountList = res.data;
                    console.log(this.discountList,"discountList")
                }
            });
        },
        submitCarLoanRebate(){
           return  this.$refs.carLoanDiscount.submitCarLoanDiscount();
        },
        submitAdditonalLoanRebate(){
            return this.$refs.additionalLoanDiscount.submitAddLoanDiscount();
        },
        resetCarLoanDis(carDiscountOption){
            return this.$refs.carLoanDiscount.resetCarLoanDiscount(carDiscountOption);
        },
        resetAdditionalLoanDis(additionalDiscountOption){
            return this.$refs.additionalLoanDiscount.resetAddLoanDiscount(additionalDiscountOption);
        },
        backDiscountList(){
            return  this.discountList;
        },
        resetTab(){
            // this.curLoan="car";
        }
    }
}
</script>

<style scoped>
.load_content h2{
    font-size: 18px;
}
.loanTab>ul li{
    float: left;
}
.loanTab>ul li>span{
    font-size: 16px;
    position: relative;
    padding:0 10px 0px 0px;
    cursor: pointer;
}
.loanTab>ul li>span{
    color: #EB9620;
}
/* .loanTab>ul li>span.active::after{
    position: absolute;
    content: "";
    width: 100%;
    height: 2px;
    background-color: #EB9620;
    left: 0px;
    bottom: 0px;
} */
.commonB{
    font-size: 18px;
    font-weight: normal;
}
.loadInfo .load_container>div>span{
    color: #EB9620;
    font-size: 16px;
    margin-bottom: 10px;
}
.load_container>div{
    width: 50%;
}
</style>
