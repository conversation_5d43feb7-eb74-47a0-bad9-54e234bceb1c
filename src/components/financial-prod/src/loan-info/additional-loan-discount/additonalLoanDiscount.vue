<template>
    <div class="loadInfo">
        <div class="load_content">
            <!-- 无贴息-->
            <div class="load_detail" v-if="discountOption==0">
                <div>
                    <Row>
                        <Col :span="11">
                            <p>
                                <span>客户利率 : </span><b class="commonB">{{noDiscountSuppleLoan.custRate}}</b> <span>%</span>
                            </p>
                            <p v-show="noDiscountSuppleLoan.monthlyRate"><span>月利率 : </span><b class="commonB">{{noDiscountSuppleLoan.monthlyRate}}</b> <span>厘</span></p>
                        </Col>
                        <Col :span="13">
                            <p>
                                <span>首期月供 : </span><b class="commonB cRed">{{noDiscountSuppleLoan.monthPayAmt}}</b> <span>元</span>
                            </p>
                            <p v-show="noDiscountSuppleLoan.myriadCoefficient"><span>万元系数 : </span><b class="commonB cRed">{{noDiscountSuppleLoan.myriadCoefficient}}</b></p>
                        </Col>
                    </Row>
                </div>
            </div>
            <!-- 灵活贴息 -->
            <div v-if="discountOption==2" class="load_detail">
               <div class="mt10">
                    <div>
                         <common-loan
                         :formData="flexSuppleLoanForm"
                         :formDataValiate="flexSuppleLoanFormValiate"
                         :selectProductInfo="selectProductInfo"
                         :curLoan="additionalLoan"
                         :valiateAdditionalLoan="valiateAdditionalLoan"
                         :isShowDetails="isShowDetails"
                         :isReconsider="isReconsider"
                         :diffType="diffType"
                         :saveStatus="saveStatus"
                         :placeholderText="placeholderText"
                         :uuid="uuid"
                         ></common-loan>
                    </div>
                </div>
            </div>
            <!-- 正常贴息 -->
            <div v-if="discountOption==1" class="load_detail">
                <div>
                    <Row>
                        <Col :span="11">
                            <p><span>客户利率 : </span><b class="commonB">{{noflexSuppleLoan.custRate}}</b> <span>%</span></p>
                            <p v-show="noflexSuppleLoan.monthlyRate"><span>月利率 : </span><b class="commonB">{{noflexSuppleLoan.monthlyRate}}</b> <span>厘</span></p>
                        </Col>
                        <Col :span="13">
                            <p><span>首期月供 : </span><b class="commonB cRed">{{noflexSuppleLoan.monthPayAmt}}</b><span>元</span></p>
                            <p v-show="noflexSuppleLoan.myriadCoefficient"><span>万元系数 : </span><b class="commonB cRed">{{noflexSuppleLoan.myriadCoefficient}}</b> <span></span></p>
                        </Col>
                    </Row>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import CommonLoan from "../common-loan/commonLoan.vue"
import vueEvent from "_p/basic/assets/js/vueEvent.js"
export default {
    name:"loadInfo",
    data(){
        return{
            loanType:[
                "车款","附加金额"
            ],
            placeholderText:"请输入附加金额贴息额",
            // 无贴息
            noDiscountSuppleLoan:{
                custRate:"",
                monthPayAmt:"",
                monthlyRate:"",
                myriadCoefficient:"",
                settleRate:"",
                addPointValue:"",
            },
            flexSuppleLoanForm:{
                maxDisCountAmt:"",
                monthPayAmt:"",
                custRate:"",
                settleRate:"",
                addPointValue:"",
                loadList:[
                //    {
                //       discountId:"",
                //       discountAmt:"",
                //       discountParty:"",
                //       isCompany:false,
                //    }
                ],
                discountList:[],
            },
            flexSuppleLoanFormValiate:{

            },
            noflexSuppleLoan:{
                custRate:"",
                monthPayAmt:"",
                monthlyRate:"",
                myriadCoefficient:"",
                settleRate:"",
                addPointValue:"",
                maxDisCountAmt:"",
            },

        }
    },
    components:{
        CommonLoan
    },
    props:{
        discountOption:{
            type:String,
        },
        selectProductInfo:{
            type:Object,
        },
        queryProductInfo:{
            type:Object,
        },
        discountList:{
            type:Array
        },
        addMonthPay:{
            type:Number,
        },
        addMonthlyRate:{
            type:String,
        },
        addMyriad:{
            type:String,
        },
        additionalLoan:{
            type:String,
        },
        valiateAdditionalLoan:{
            type:Function
        },
        uuid:{
            type:String,
        },
        saveStatus:{
            type:Object,
        },
        isShowDetails:{
            type:Boolean,
        },
        isReconsider:{
            type:Boolean,
        },
        diffType:{
            type:String
        },
    },
    watch:{
        isShowDetails(val){
            console.log(val,"val-isShowDetails")
        },
        selectProductInfo(val){
            if(Object.keys(val).length>0){
                this.selectLoanInfo(val)
            }
        },
        queryProductInfo(val){
            if(Object.keys(val).length>0){
                this.queryLoanInfo(val)
            }
        },
        addMonthPay(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountSuppleLoan.monthPayAmt=val;
                }else if(this.discountOption==1){
                    this.noflexSuppleLoan.monthPayAmt=val;
                }else if(this.discountOption==2){
                    this.$set(this.flexSuppleLoanForm,'monthPayAmt',val)
                }
            }
        },
        addMonthlyRate(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountSuppleLoan.monthlyRate=val;
                }else if(this.discountOption==1){
                    this.noflexSuppleLoan.monthlyRate=val;
                }
            }
        },
        addMyriad(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountSuppleLoan.myriadCoefficient=val;
                }else if(this.discountOption==1){
                    this.noflexSuppleLoan.myriadCoefficient=val;
                }
            }
        }

    },
    mounted(){
         let _this=this;
        vueEvent.$on('to-additionalLoanRate',function(rate,clearRate,addPointValue,maxAmt,id){
            if(id==_this.uuid){
                if(_this.discountOption==0){
                    _this.noDiscountSuppleLoan.custRate=rate;
                    _this.noDiscountSuppleLoan.addPointValue=addPointValue;
                    _this.noDiscountSuppleLoan.settleRate=clearRate;
                }else if(_this.discountOption==1){
                    _this.noflexSuppleLoan.custRate=rate;
                    _this.noflexSuppleLoan.settleRate=clearRate;
                    _this.noflexSuppleLoan.addPointValue=addPointValue;
                    _this.noflexSuppleLoan.maxDisCountAmt=maxAmt;
                }else if(_this.discountOption==2){
                    // 灵活贴息的客户利率是调接口获取
                    //  _this.flexSuppleLoanForm.custRate=rate;
                    _this.flexSuppleLoanForm.settleRate=clearRate;
                    _this.flexSuppleLoanForm.addPointValue=addPointValue;
                    _this.flexSuppleLoanForm.maxDisCountAmt=maxAmt;
                    if(maxAmt){
                        if(_this.flexSuppleLoanForm.loadList.length>0){
                            _this.flexSuppleLoanForm.loadList.forEach((item,index)=>{
                                if(item.discountId=='0'){
                                    _this.flexSuppleLoanForm.loadList[index].discountAmt=maxAmt;
                                }
                            })
                        }
                    }
                }
            }
        });
    },
     // 解决多次绑定,多次触发问题
    beforeDestroy () {
        vueEvent.$off("to-additionalLoanRate");
    },
    computed: {

    },
    methods:{
        selectLoanInfo(val){
            if(val.discountOption==2){
                console.log(val,"val")
                let tempSubsidySide=[];
                if(val.discountInfo.subsidyList.length>0){
                    val.discountInfo.subsidyList.forEach((item,index)=>{
                        tempSubsidySide.push(item.subsidySide);
                    })
                }
                let tempDiscoutList=[];
                this.discountList.forEach((item,index)=>{
                    if(tempSubsidySide.indexOf(item.value)!="-1"){
                        let obj={
                            value:item.value,
                            title:item.title
                        }
                        tempDiscoutList.push(obj);
                    }
                })
                 this.flexSuppleLoanForm.loadList=[];
                if(tempDiscoutList.length>0){
                    tempDiscoutList.forEach((item,index)=>{
                        let tempObj={
                            discountId:item.value,
                            discountAmt:0,
                            discountParty:item.title,
                        }
                        if(item.value=="0"){
                            tempObj.isCompany=true;
                            // this.flexSuppleLoanForm.loadList[0]=tempObj
                             this.flexSuppleLoanForm.loadList.splice(0,0,tempObj);
                        }else{
                            tempObj.isCompany=false;
                            this.flexSuppleLoanForm.loadList.push(tempObj)
                        }
                    })
                    // console.log(this.flexSuppleLoanForm.loadList,"this.flexSuppleLoanForm.loadList")
                }
                this.flexSuppleLoanForm.discountList=tempDiscoutList;
                // this.flexSuppleLoanForm.maxDisCountAmt=val.discountInfo.maxdiscountAmt;
            }
        },
        queryLoanInfo(val){
               // 无贴息
            if(val.discountOption==0){
                this.noDiscountSuppleLoan.custRate=val.costInfo.custRate;
                this.noDiscountSuppleLoan.monthPayAmt=val.costInfo.monthPayAmt;
                if(val.algorithmType=='equalrental'){
                    this.noDiscountSuppleLoan.monthlyRate=val.costInfo.monthlyRate.toString();
                    this.noDiscountSuppleLoan.myriadCoefficient=val.costInfo.myriadCoefficient.toString();
                }
                this.noDiscountSuppleLoan.settleRate=val.costInfo.settleRate;
                this.noDiscountSuppleLoan.addPointValue=val.costInfo.addPointValue;
            }else if(val.discountOption==1){
                this.noflexSuppleLoan.custRate=val.costInfo.custRate;
                this.noflexSuppleLoan.monthPayAmt=val.costInfo.monthPayAmt;
                if(val.algorithmType=="equalrental"){
                    this.noDiscountSuppleLoan.monthlyRate=val.costInfo.monthlyRate.toString();
                    this.noDiscountSuppleLoan.myriadCoefficient=val.costInfo.myriadCoefficient.toString();
                }
                this.noflexSuppleLoan.settleRate=val.costInfo.settleRate;
                this.noflexSuppleLoan.addPointValue=val.costInfo.addPointValue;
            }else if(val.discountOption==2){
                this.flexSuppleLoanForm.custRate=val.costInfo.custRate;
                this.flexSuppleLoanForm.monthPayAmt=val.costInfo.monthPayAmt;
                this.flexSuppleLoanForm.maxDisCountAmt=val.costInfo.maxDiscountAmt;
                this.flexSuppleLoanForm.settleRate=val.costInfo.settleRate;
                this.flexSuppleLoanForm.addPointValue=val.costInfo.addPointValue

                this.flexSuppleLoanForm.loadList=this.queryDiscount(val.discountList);
            }
        },
        queryDiscount(discountList){
            let tempDiscout=[];
            discountList.forEach((item,index)=>{
                this.discountList.forEach((itemDis,indexDis)=>{
                    if(item.discountId=itemDis.value){
                        let   obj={
                            discountId:item.discountId,
                            discountAmt:item.discountAmt,
                            discountParty:itemDis.title,
                        }
                        if(item.discountId=="0"){
                            obj.isCompany=true;
                        }else{
                            obj.isCompany=false;
                        }
                        tempDiscout.push(obj);
                    }
                })

            })
            return tempDiscout;
        },
        submitAddLoanDiscount(){
            if(this.discountOption==0){
                return this.noDiscountSuppleLoan;
            }else if(this.discountOption==1){
                return this.noflexSuppleLoan;
            }else if(this.discountOption==2){
                return this.flexSuppleLoanForm;
            }
        },
        resetAddLoanDiscount(additionalDiscountOption){
            if(additionalDiscountOption=="0"){
                this.noDiscountSuppleLoan={
                    custRate:"",
                    monthPayAmt:"",
                    settleRate:"",
                }
            }else if(additionalDiscountOption=="1"){
                this.noflexSuppleLoan={
                    custRate:"",
                    monthPayAmt:"",
                    settleRate:"",
                }
            }else if(additionalDiscountOption=="2"){
                this.flexSuppleLoanForm={
                    maxDisCountAmt:"",
                    custRate:"",
                    settleRate:"",
                    loadList:[
                        {
                            discountId:"",
                            discountAmt:"",
                        }
                    ],
                    discountList:[],
                }
            }
        }

    }
}
</script>

<style scoped>
.load_content h2{
    font-size: 24px;
    font-weight: normal;
}
.load_detail>p{
    margin:10px 0;
}
.load_detail>p b{
    font-size:18px;
    font-weight: normal;
}
.load_detail>p i{
    font-style: normal;
}

.load_detail>ul li{
    float: left;
    padding-left: 20px;
}
.load_detail>ul span{
    font-size: 16px;
    position: relative;
    padding:0 10px 10px;
    cursor: pointer;
}
.load_detail>ul span.active{
    color: #0D8EF5;
}
.load_detail>ul span.active::after{
    position: absolute;
    content: "";
    width: 100%;
    height: 2px;
    background-color: #0D8EF5;
    left: 0px;
    bottom: 0px;
}
 /* .label_font>>>.ivu-form-item-label{
    font-size: 16px;
}
.loadInfoForm .ivu-select-item{
    font-size:16px !important;
}
.loadInfoForm .ivu-select-selected-value{
    font-size: 16px !important;
} */
.commonB{
    font-size: 18px;
    font-weight: normal;
}
.cRed{
    color: red;
}
</style>
