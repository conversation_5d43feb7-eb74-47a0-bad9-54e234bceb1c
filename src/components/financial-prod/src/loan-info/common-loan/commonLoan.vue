<template>
    <div class="commonLoad">
        <Form ref="formData" :model="formData" :rules="formDataValiate"  :label-width="100" class="formData">
            <Row>
                <Col>
                    <FormItem label="厂商最高贴息额" class="mb0 label_font" style="font-size:16px">
                        <span class="commonB">{{formData.maxDisCountAmt}}</span>
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <Col v-for="(item,index) in formData.loadList" :key="index">
                    <!-- <FormItem label="" :prop="'loadList['+index+'].discountId'" class="inline-block mb0" :label-width="0" style="width: 20%;">
                        <Select v-model="item.discountId"
                        placeholder="请选择"
                        @on-change='((val)=>{changeDiscount(val, index)})'
                        :label-in-value="true"
                        :disabled="isShowDetails||item.isCompany||diffType=='details'||isReconsider"
                        class="wprecent100"
                        >
                            <Option :value="item.value" v-for="(item,index) in showDiscountList(item.discountId)" :key="index" :disabled="item.disabled">{{item.title}}</Option>
                        </Select>
                    </FormItem> -->
                    <FormItem :label="item.discountParty" :prop="'loadList['+index+'].discountAmt'"  class="inline-block mb0" :label-width="75">
                          <Input
                         v-model="item.discountAmt"
                        :placeholder="placeholderText"
                         @on-blur="checkDiscountAmt(item.discountAmt,item.discountId)"
                         :disabled="isShowDetails||diffType=='details'||item.isCompany"
                         />
                   </FormItem>
                    <!-- <div  class="addLoan" v-if="!isShowDetails&&(diffType!='details')&&!isReconsider&&formData.loadList.length>1">
                        <i @click="addLoadDetail()" v-if="index==0">+</i>
                            <i @click="delLoadDetail(index)" v-else>-</i>
                    </div> -->
                </Col>
            </Row>
            <Row>
                <Col>
                    <FormItem label="客户利率" class="mb0 label_font" :label-width="75">
                        <!-- <Input v-model="formData.custRate" placeholder="" :disabled="isShowDetails||diffType=='details'||isReconsider" /> -->
                          <Input v-model="formData.custRate" placeholder="" :disabled="true" />
                    </FormItem>
                </Col>
            </Row>
             <Row>
                <Col>
                    <FormItem label="首期月供" class="mb0 label_font" :label-width="72">
                        <p><b class="commonB cRed">{{formData.monthPayAmt}}</b><span>元</span></p>
                    </FormItem>
                </Col>
            </Row>
        </Form>
    </div>
</template>
<script>
import {
    getCustRate
} from "@/projects/basic/api/product/financialProducts.js"
import * as utils from "@/projects/basic/assets/js/utils.js"
import vueEvent from "_p/basic/assets/js/vueEvent.js"
export default {
    name:"commonLoan",
    data(){
       return {
       }
    },
    props:{
        formData:{
            type:Object,
            required:true,
        },
        formDataValiate:{
             type:Object,
             required:true,
        },
        selectCarProductInfo:{
            type:Object,
        },
        selectProductInfo:{
            type:Object,
        },
        curLoan:{
            type:String,
        },
        valiateCarLoan:{
            type:Function
        },
        valiateCarAddLoan:{
            type:Function
        },
        valiateAdditionalLoan:{
            type:Function
        },
        isAdditional:{
            type:String,
        },
        isSubProduct:{
            type:String
        },
        isShowDetails:{
            type:Boolean
        },
        isReconsider:{
            type:Boolean
        },
        diffType:{
            type:String
        },
        saveStatus:{
            type:Object
        },
        placeholderText:{
            type:String
        },
        uuid:{
            type:String,
        },
    },
    mounted(){
        let _this=this;
        vueEvent.$on("to-discountList",function(discountData,id){
            if(_this.uuid==id){
                for(let i=0;i<discountData.length;i++){
                    for(let j=0;j<_this.formData.loadList.length;j++){
                        if(discountData[i].discountId==_this.formData.loadList[j].discountId){
                            _this.formData.loadList[j].discountAmt=discountData[i].discountAmt;
                            console.log(discountData[i].discountAmt,"discountData[i].discountAmt")
                        }
                    }
                }
            }
        });
    },
    computed:{
         showDiscountList(){
             return (val) => {
                //  console.log(val,"val")
                //讲option的显示数据进行深拷贝
                let newList = JSON.parse(JSON.stringify(this.formData.discountList));
                //处理selectList数据，返回一个新数组arr
                //arr数组就相当于所有Select选中的数据集合（没有选中的为''，不影响判断），只要在这个集合里面，其他的下拉框就不应该有这个选项
                const arr = this.formData.loadList.map(item => {
                //将其格式{value：'NewYork'}变成['NewYork'],方便使用indexOf进行判断
                    return (item = item.discountId);
                });
                //过滤出newList里面需要显示的数据
                newList = newList.filter(item => {
                    //当前下拉框的选中的数据需要显示
                    //val就是当前下拉框选中的值
                    if (val == item.value) {
                        return item;
                    } else {
                        //再判断在arr这个数组中是不是有这个数据，如果不在，说明是需要显示的
                        if (arr.indexOf(item.value) == -1) {
                            return item;
                        }
                    }
                });
                //返回Options显示数据
                return newList;
            };
        },
    },
    // beforeDestroy () {
    //     vueEvent.$off("to-discountList");
    // },
    methods:{
        changeDiscount(val,index){
            if(val){
                this.formData.loadList[index].discountParty=val.label;
            }
            // console.log(this.formData.loadList,"loadList")
        },
        // 校验输入的贴息金额
        checkDiscountAmt(discountAmt,discountId){
            if(this.curLoan=="car"){
                this.enclosureAmt(discountAmt,discountId,this.selectCarProductInfo,this.curLoan);
            }else if(this.curLoan=="additional"){
                console.log(this.selectProductInfo,"this.selectProductInfo")
                this.enclosureAmt(discountAmt,discountId,this.selectProductInfo,this.curLoan);
            }
        },
        // 贴息列表金额失去焦点调接口
        enclosureAmt(discountAmt,discountId,productInfo,type){
             let carLoanResult=false;
             let addLoanResult=false;
            //  校验车贷和附加租赁项
            if(type=="car"){
                if(this.isAdditional=='0'||(this.isAdditional=="1"&&this.isSubProduct=="1")){
                    carLoanResult=this.valiateCarLoan();
                    if(!carLoanResult){
                        return false;
                    }
                }else if(this.isAdditional=="1"&&this.isSubProduct=="0"){
                    carLoanResult=this.valiateCarAddLoan();
                    if(!carLoanResult){
                        return false;
                    }
                }
            }else if(type=="additional"){
                addLoanResult=this.valiateAdditionalLoan();
                if(!addLoanResult){
                    return false;
                }
                if(addLoanResult.loadAmtList&&addLoanResult.loadAmtList.length<=0){
                    this.$Message.warning("点击新增填写附加租赁项数据");
                    return false;
                }
            }
            console.log(discountAmt,"discountAmt")
            if(discountAmt===''){
                this.$Message.warning("请先填写贴息金额");
                return false;
            }
             // 校验贴息列表金额
            if(discountId&&discountAmt){
                if(utils.isTwoDicmal(discountAmt)){
                    for(let i=0;i<productInfo.discountInfo.subsidyList.length;i++){
                        // 判断所输入金额是否大于最大贴息额
                        if(discountId==productInfo.discountInfo.subsidyList[i].subsidySide){
                            if(productInfo.discountInfo.subsidyList[i].singleSelect&&(productInfo.discountInfo.subsidyList[i].singleSelect=='0'||productInfo.discountInfo.subsidyList[i].singleSelect=='1')){
                                if(discountId!=0){
                                    if(discountAmt>productInfo.discountInfo.subsidyList[i].subsidyMoney){
                                        this.$Message.warning("贴息额不能大于最高贴息额");
                                        return false;
                                    }
                                }else{
                                    if(!this.formData.maxDisCountAmt){
                                        this.$Message.warning('请先获取厂商最大贴息额');
                                        return false;
                                    }
                                    if(discountAmt>this.formData.maxDisCountAmt){
                                        this.$Message.warning('贴息额不能大于厂商最高贴息额');
                                        return false;
                                    }
                                }

                            }
                        }
                    }
                }else{
                    this.$Message.warning("金额格式为最多两位小数");
                    return false;
                }
            }
            let totalAmt=0;
           let discountList=[];
            if(this.formData.maxDisCountAmt){
                totalAmt=this.formData.maxDisCountAmt;
                for(let i=0;i<this.formData.loadList.length;i++){
                    let tempobj={};
                    if(this.formData.loadList[i].discountAmt===""){
                        this.$Message.warning("车款贴息金额需填写完整")
                        return false;
                    }else{
                        if(this.formData.loadList[i].discountId!="0"){
                            totalAmt=utils.add(totalAmt,this.formData.loadList[i].discountAmt);
                            tempobj.discountAmt=this.formData.loadList[i].discountAmt;

                        }else{
                            tempobj.discountAmt=this.formData.maxDisCountAmt;
                        }
                        tempobj.discountId=this.formData.loadList[i].discountId;
                        discountList.push(tempobj);
                    }
                }
            }else{
                for(let i=0;i<this.formData.loadList.length;i++){
                    let tempobj={};
                    if(this.formData.loadList[i].discountAmt===""){
                        this.$Message.warning("车款贴息金额需填写完整")
                        return false;
                    }else{
                        totalAmt=utils.add(totalAmt,this.formData.loadList[i].discountAmt)
                        tempobj.discountId=this.formData.loadList[i].discountId;
                        tempobj.discountAmt=this.formData.loadList[i].discountAmt;
                        discountList.push(tempobj);
                    }
                }
            }

            let param={};
            param.productId=productInfo.baseInfo.id
            if(type=="car"){
                console.log(carLoanResult,this.formData,"this.formData");
                param.loanAmt=carLoanResult.loanAmt;
                param.loanTerm=carLoanResult.loanLimit;

            }else if(type=="additional"){
                param.loanAmt=addLoanResult.loadAmtList[0].loanAmt;
                param.loanTerm=addLoanResult.loadAmtList[0].loanLimit;
            }
            param.settleRate=this.formData.settleRate;
            param.discountAmt=totalAmt;
            param.discountList=discountList;
            getCustRate(param).then(res=>{
                if(res.code=="0000"){
                    if(res.data||res.data==0){
                        // this.formData.custRate=res.data;
                        this.formData.custRate=res.data.custRate;
                        this.saveStatus.disabled=true;
                        this.saveStatus.ismodify=true;

                        for(let i=0;i<res.data.discountList.length;i++){
                            for(let j=0;j<this.formData.loadList.length;j++){
                                if(res.data.discountList[i].discountId==this.formData.loadList[j].discountId){
                                    this.formData.loadList[j].discountAmt=res.data.discountList[i].discountAmt;
                                }
                            }
                        }
                    }
                }
            })
        },
        addLoadDetail(){
            if(this.formData.loadList.length<this.formData.discountList.length){
                this.formData.loadList.push({
                    discountId:"",
                    discountAmt:"",
                    isCompany:false,
                })
            }else{
                this.$Message.warning("增加的贴息方式不能超过贴息方式列表数");
            }
            // this.initCheckDiscountItem()
        },
        delLoadDetail(index,type){
            this.formData.loadList.splice(index,1);
        },
        initCheckDiscountItem() {
            for (let i = 0; i < this.formData.loadList.length; i++) {
                this.formDataValiate["loadList[" + i + "].discountAmt"] = [
                    {validator:(rule,value,callback)=>{utils.isTwoDicmal(rule,value,callback)},trigger:"blur"}
                ];
            }
        },
    }
}
</script>
<style lang="less" scoped>
.addLoan{
    display: inline-block;
    vertical-align: top;
    margin-top: 6px;
}
.addLoan i{
    width: 20px;
    height: 20px;
    background: #EB9620;
    display: inline-block;
    vertical-align: middle;
    border-radius: 50%;
    line-height: 15px;
    text-align: center;
    color: white;
    font-size: 20px;
    cursor: pointer;
}
.commonB{
    font-size: 18px;
    font-weight: normal;
}
.cRed{
    color: red;
}
</style>
