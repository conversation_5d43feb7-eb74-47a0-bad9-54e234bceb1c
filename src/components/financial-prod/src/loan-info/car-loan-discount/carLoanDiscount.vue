<template>
    <div class="loadInfo">
        <div class="load_content">
            <!-- 无贴息-->
            <div class="load_detail" v-if="discountOption==0">
                <Row>
                    <Col :span="11">
                        <p><span>客户利率 : </span><b class="commonB">{{noDiscountCarLoan.custRate}}</b> <span>%</span></p>
                        <p v-show="noDiscountCarLoan.monthlyRate"><span>月利率 : </span><b class="commonB">{{noDiscountCarLoan.monthlyRate}}</b> <span>厘</span></p>

                    </Col>
                    <Col :span="13">
                        <p><span>首期月供 : </span><b class="commonB cRed">{{noDiscountCarLoan.monthPayAmt}}</b> <span>元</span></p>
                        <p v-show="noDiscountCarLoan.myriadCoefficient"><span>万元系数 : </span><b class="commonB cRed">{{noDiscountCarLoan.myriadCoefficient}}</b></p>
                    </Col>
                </Row>
            </div>
            <!-- 灵活贴息 -->
            <div v-if="discountOption==2" class="load_detail">
               <div class="mt10">
                    <div>
                         <common-loan
                         :formData="flexCarLoanForm"
                         :formDataValiate="flexCarLoanFormValiate"
                         :selectCarProductInfo="selectCarProductInfo"
                         :curLoan="carLoan"
                         :valiateCarLoan="valiateCarLoan"
                         :valiateCarAddLoan="valiateCarAddLoan"
                         :isAdditional="isAdditional"
                         :isSubProduct="isSubProduct"
                         :isShowDetails="isShowDetails"
                         :isReconsider="isReconsider"
                         :diffType="diffType"
                         :saveStatus="saveStatus"
                         :placeholderText="placeholderText"
                         :uuid="uuid"
                         ></common-loan>
                    </div>
                </div>
            </div>
            <!-- 正常贴息 -->
            <div v-if="discountOption==1" class="load_detail">
                <div>
                    <Row>
                        <Col :span="11">
                            <p><span>客户利率 : </span><b class="commonB">{{noflexCarLoan.custRate}}</b> <span>%</span></p>
                            <p v-show="noflexCarLoan.monthlyRate"><span>月利率 : </span><b class="commonB">{{noflexCarLoan.monthlyRate}}</b> <span>厘</span></p>
                        </Col>
                        <Col :span="13">
                            <p><span>首期月供 : </span><b class="commonB cRed">{{noflexCarLoan.monthPayAmt}}</b><span>元</span></p>
                            <p v-show="noflexCarLoan.myriadCoefficient"><span>万元系数 : </span><b class="commonB cRed">{{noflexCarLoan.myriadCoefficient}}</b></p>
                        </Col>
                    </Row>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import CommonLoan from "../common-loan/commonLoan.vue"
import vueEvent from "_p/basic/assets/js/vueEvent.js"
import * as utils from "@/projects/basic/assets/js/utils.js"
export default {
    name:"loadInfo",
    data(){
        return{
            placeholderText:"请输入车款贴息额",
            // 无贴息车贷无附加租赁项
            noDiscountCarLoan:{
                custRate:"",
                monthPayAmt:"",
                monthlyRate:'',
                myriadCoefficient:'',
                settleRate:"",
                addPointValue:"",
            },
            // 灵活贴息车贷无附加租赁项
            flexCarLoanForm:{
                maxDisCountAmt:"",
                settleRate:"",
                custRate:"",
                addPointValue:"",
                monthPayAmt:"",
                loadList:[
                //    {
                //       discountId:"",
                //       discountAmt:"",
                //       discountParty:"",
                //       isCompany:false,
                //    }
                ],
                discountList:[],
            },
            flexCarLoanFormValiate:{
                // "loadList[0].discountAmt":[
                //     {validator:utils.isTwoDicmalValiate,trigger:"blur"}
                // ],
            },
            // 非灵活贴息车贷有附加租赁项
            noflexCarLoan:{
                custRate:"",
                monthPayAmt:"",
                monthlyRate:'',
                myriadCoefficient:'',
                settleRate:"",
                addPointValue:"",
                maxDisCountAmt:"",
            },
        }
    },
    components:{
        CommonLoan
    },
    props:{
        discountList:{
            type:Array,
        },
        discountOption:{
            type:String,
        },
        selectCarProductInfo:{
            type:Object,
        },
        queryCarProductInfo:{
            type:Object
        },
        carMonthPay:{
            type:Number,
        },
        carMonthlyRate:{
            type:String
        },
        carMyriad:{
            type:String,
        },
        carLoan:{
            type:String,
        },
        valiateCarLoan:{
            type:Function,
        },
        valiateCarAddLoan:{
            type:Function
        },
        uuid:{
            type:String,
        },
        isAdditional:{
            type:String,
        },
        isSubProduct:{
            type:String
        },
        saveStatus:{
            type:Object,
        },
        isShowDetails:{
            type:Boolean,
        },
        isReconsider:{
            type:Boolean,
        },
        diffType:{
            type:String
        }
    },
    watch:{
        selectCarProductInfo(val){
            if(Object.keys(val).length>0){
                this.selectLoanInfo(val)
            }
        },
        queryCarProductInfo(val){
            if(Object.keys(val).length>0){
                this.queryLoanInfo(val)
            }
        },
        carMonthPay(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountCarLoan.monthPayAmt=val;
                }else if(this.discountOption==1){
                    this.noflexCarLoan.monthPayAmt=val;
                }else if(this.discountOption==2){
                    this.flexCarLoanForm.monthPayAmt=val;
                }
            }
        },
        carMonthlyRate(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountCarLoan.monthlyRate=val;
                    console.log(this.noDiscountCarLoan.monthlyRate,"monthlyRate")
                }else if(this.discountOption==1){
                    this.noflexCarLoan.monthlyRate=val;
                    console.log(this.noflexCarLoan.monthlyRate,"monthlyRate")
                }
            }
        },
        carMyriad(val){
            if(val){
                if(this.discountOption==0){
                    this.noDiscountCarLoan.myriadCoefficient=val;
                }else if(this.discountOption==1){
                    this.noflexCarLoan.myriadCoefficient=val;
                }
            }
        }

    },
    created(){
        let _this=this;
        vueEvent.$on('to-carLoanRate',function(rate,clearRate,addPointValue,maxAmt,id){
            console.log(rate,clearRate,6666)
            if(id==_this.uuid){
                if(_this.discountOption==0){
                    _this.noDiscountCarLoan.custRate=rate;
                    _this.noDiscountCarLoan.settleRate=clearRate;
                    _this.noDiscountCarLoan.addPointValue=addPointValue;
                }else if(_this.discountOption==1){
                    _this.noflexCarLoan.custRate=rate;
                    _this.noflexCarLoan.settleRate=clearRate;
                    _this.noflexCarLoan.addPointValue=addPointValue;
                    _this.noflexCarLoan.maxDisCountAmt=maxAmt;
                }else if(_this.discountOption==2){
                        // 灵活贴息的客户利率是调接口获取
                    _this.flexCarLoanForm.settleRate=clearRate;
                    _this.flexCarLoanForm.addPointValue=addPointValue;
                     _this.flexCarLoanForm.maxDisCountAmt=maxAmt;
                     if(maxAmt){
                        if(_this.flexCarLoanForm.loadList.length>0){
                            _this.flexCarLoanForm.loadList.forEach((item,index)=>{
                                if(item.discountId=='0'){
                                    _this.flexCarLoanForm.loadList[index].discountAmt=maxAmt;
                                }
                            })
                        }
                     }
                }
            }
        });
    },
    computed: {

    },
    // 解决多次绑定,多次触发问题
    beforeDestroy () {
        vueEvent.$off("to-carLoanRate");
    },
    methods:{
        // 选中产品回显
        selectLoanInfo(val){
            // 灵活贴息
            if(val.discountOption==2){
                console.log(val,"val")
                let tempSubsidySide=[];
                if(val.discountInfo.subsidyList.length>0){
                    val.discountInfo.subsidyList.forEach((item,index)=>{
                        tempSubsidySide.push(item.subsidySide);
                    })
                }
                let tempDiscoutList=[];
                this.discountList.forEach((item,index)=>{
                    if(tempSubsidySide.indexOf(item.value)!="-1"){
                        let obj={
                            value:item.value,
                            title:item.title
                        }
                        tempDiscoutList.push(obj);
                    }
                })
                // console.log(this.flexCarLoanForm.loadList,"this.flexSuppleLoanForm.loadList0")
                this.flexCarLoanForm.loadList=[];
                if(tempDiscoutList.length>0){
                    tempDiscoutList.forEach((item,index)=>{
                        let tempObj={
                                discountId:item.value,
                                discountAmt:0,
                                discountParty:item.title,
                        }
                        if(item.value=="0"){
                            tempObj.isCompany=true;
                            // this.flexCarLoanForm.loadList[0]=tempObj;
                            this.flexCarLoanForm.loadList.splice(0,0,tempObj);
                        }else{
                            tempObj.isCompany=false;
                            this.flexCarLoanForm.loadList.push(tempObj)
                        }
                    })
                }
                this.flexCarLoanForm.discountList=tempDiscoutList;

                console.log(tempDiscoutList,"tempDiscoutList")
                // this.flexCarLoanForm.maxDisCountAmt=val.discountInfo.maxdiscountAmt;
            }
        },
        queryLoanInfo(val){
               // 无贴息
            if(val.discountOption==0){
                this.noDiscountCarLoan.custRate=val.costInfo.custRate;
                this.noDiscountCarLoan.monthPayAmt=val.costInfo.monthPayAmt;
                if(val.algorithmType=='equalrental'){
                    this.noDiscountCarLoan.monthlyRate=val.costInfo.monthlyRate.toString();
                    this.noDiscountCarLoan.myriadCoefficient=val.costInfo.myriadCoefficient.toString();
                }
                this.noDiscountCarLoan.settleRate=val.costInfo.settleRate;
                this.noDiscountCarLoan.addPointValue=val.costInfo.addPointValue;
            }else if(val.discountOption==1){
                this.noflexCarLoan.custRate=val.costInfo.custRate;
                this.noflexCarLoan.monthPayAmt=val.costInfo.monthPayAmt;
                if(val.algorithmType=='equalrental'){
                    this.noflexCarLoan.monthlyRate=val.costInfo.monthlyRate.toString();
                    this.noflexCarLoan.myriadCoefficient=val.costInfo.myriadCoefficient.toString();
                }
                this.noflexCarLoan.settleRate=val.costInfo.settleRate;
                this.noflexCarLoan.addPointValue=val.costInfo.addPointValue;
            }else if(val.discountOption==2){
                this.flexCarLoanForm.maxDisCountAmt=val.costInfo.maxDiscountAmt
                this.flexCarLoanForm.custRate=val.costInfo.custRate;
                this.flexCarLoanForm.monthPayAmt=val.costInfo.monthPayAmt;
                this.flexCarLoanForm.settleRate=val.costInfo.settleRate;
                this.flexCarLoanForm.addPointValue=val.costInfo.addPointValue;

                 // 回显灵活贴息无附加租赁项的贴息列表
                this.flexCarLoanForm.loadList=this.queryDiscount(val.discountList);
            }
        },
        queryDiscount(discountList){
            let tempDiscout=[];
            discountList.forEach((item,index)=>{
                this.discountList.forEach((itemDis,indexDis)=>{
                    if(item.discountId==itemDis.value){
                        let obj={
                            discountId:item.discountId,
                            discountAmt:item.discountAmt,
                            discountParty:itemDis.title,
                        }
                        if(item.discountId=="0"){
                            obj.isCompany=true;
                        }else{
                            obj.isCompany=false;
                        }
                       tempDiscout.push(obj);
                    }
                })
            })
            return tempDiscout;
        },
        submitCarLoanDiscount(){
            if(this.discountOption==0){
                return this.noDiscountCarLoan;
            }else if(this.discountOption==1){
                return this.noflexCarLoan;
            }else if(this.discountOption==2){
                return this.flexCarLoanForm;
            }
        },
        resetCarLoanDiscount(carDiscountOption){
            if(carDiscountOption==0){
                this.noDiscountCarLoan={
                    custRate:"",
                    monthPayAmt:"",
                    settleRate:"",
                }
            }else if(carDiscountOption==1){
                this.noflexCarLoan={
                    custRate:"",
                    monthPayAmt:"",
                    settleRate:"",
                }
            }else if(carDiscountOption==2){
                this.flexCarLoanForm={
                    maxDisCountAmt:"",
                    settleRate:"",
                    custRate:"",
                    monthPayAmt:"",
                    loadList:[
                    {
                        discountId:"",
                        discountAmt:"",
                    }
                    ],
                    discountList:[],
                }
            }
        }
    }
}
</script>

<style scoped>
.load_content h2{
    font-size: 24px;
    font-weight: normal;
}
.load_detail>p{
    margin:10px 0;
}
.load_detail>p span{
    /* font-size: 16px; */
}
.load_detail>p b{
    font-size:18px;
    font-weight: normal;
}
.load_detail>p i{
    font-style: normal;
}

.load_detail>ul li{
    float: left;
    padding-left: 20px;
}
.load_detail>ul span{
    font-size: 16px;
    position: relative;
    padding:0 10px 10px;
    cursor: pointer;
}
.load_detail>ul span.active{
    color: #0D8EF5;
}
.load_detail>ul span.active::after{
    position: absolute;
    content: "";
    width: 100%;
    height: 2px;
    background-color: #0D8EF5;
    left: 0px;
    bottom: 0px;
}
.commonB{
    font-size: 18px;
    font-weight: normal;
}
.cRed{
    color: red;
}
</style>
