<template>
    <div class="carAdditioalLoan">
        <Form ref="carAdditioalLoanForm" :model="carAdditioalLoanForm" :rules="carAdditioalLoanFormValiate" :label-width="90">
            <div class="common_content" style="padding: 0px;">
                 <Row>
                    <Col>
                        <FormItem label="车辆价格(元)" prop="carPrice">
                            <Input v-model="carAdditioalLoanForm.carPrice"  placeholder="" :readonly="false"
                              :disabled="(businessOption.carNature!==undefined&&businessOption.carNature==='02')||isShowDetails||pageIdentify=='assertChange'||isReconsider"  class="wprecent100"  @on-blur="blurCarPrice"/>
                        </FormItem>
                    </Col>
                </Row>
                <Row>
                    <Col>
                        <common-finance
                        :isShowDetails="isShowDetails"
                        :isReconsider="isReconsider"
                        :diffType="diffType"
                        :financeForm="carAdditioalLoanForm"
                        :isAdditional="isAdditional"
                        :isSubProduct="isSubProduct"
                        :selectProductInfo="selectProductInfo"
                        :selectCarProductInfo="selectCarProductInfo"
                        :queryCarProductInfo="queryCarProductInfo"
                        :financeFormValiate="carAdditioalLoanFormValiate"
                        @deliverAddAmt="acceptAddAmt"
                        @clearAddAmt="resetAddAmt"
                        :mainProductId="mainProductId"
                        :isInternet="isInternet"
                        :enclosureAmt="enclosureAmt"
                        :inputPrice="inputPrice"
                        ></common-finance>
                    </Col>
                </Row>
                <Row>
                    <Col span="16">
                        <FormItem label="租赁期限(期)" prop="loanLimit" >
                            <InputNumber
                            :min="0"
                            class="wprecent100"
                             v-model="carAdditioalLoanForm.loanLimit"
                             :disabled="isShowDetails||diffType=='details'"
                             @on-blur="inputLoanLimit"
                             @keyup.native="changeNumber"
                             ></InputNumber>
                        </FormItem>
                    </Col>
                    <Col span="8" style="text-align: right;">
                        <FormItem label="" :label-width="0" style="margin-left: 10px;">
                            <p style="display:inline-block" class="term">
                                默认区间:
                                <span v-for="(item,index) in term" :key="index" :class="{active:curSpan===index}" @click="selectTerm(item,index)"><i>{{item}}</i><b v-show="index!=term.length-1">~</b></span>
                            </p>
                        </FormItem>
                    </Col>
                </Row>
                 <Row style="display: flex;">
                      <Col span="12">
                        <FormItem label="首付比例(%)" prop="downPayScale" >
                            <Input v-model="carAdditioalLoanForm.downPayScale"  placeholder=""  :disabled="isShowDetails||diffType=='details'"  class="wprecent100" @on-blur="blurDownPayScale"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="首付金额(元)" prop="downPayAmt" style="margin-left: 10px;">
                            <Input v-model="carAdditioalLoanForm.downPayAmt"  placeholder=""  :disabled="isShowDetails||diffType=='details'"  class="wprecent100" @on-blur="blurDownPayAmt"/>
                        </FormItem>
                    </Col>
                </Row>
                  <Row v-if="isCarTail" style="display: flex;">
                      <Col span="12">
                        <FormItem label="尾款比例(%)" prop="tailPayScale">
                            <Input v-model="carAdditioalLoanForm.tailPayScale"  placeholder="" @on-change="inputPayScale('tailPayScale')" :disabled="isShowDetails||diffType=='details'||isReconsider"  class="wprecent100"/>
                        </FormItem>
                    </Col>
                    <Col span="12">
                        <FormItem label="尾款金额(元)" prop="tailPayAmt" style="margin-left: 10px;">
                            <Input v-model="carAdditioalLoanForm.tailPayAmt"  placeholder="" @on-change="inputPayAmt('tailPayAmt')" :disabled="isShowDetails||diffType=='details'||isReconsider"  class="wprecent100"  @on-blur="blurPayAmt('tailPayAmt')"/>
                        </FormItem>
                    </Col>
                </Row>
                 <Row>
                    <Col>
                        <FormItem label="租赁金额(元)" prop="loanAmt">
                            <Input v-model="carAdditioalLoanForm.loanAmt"  placeholder=""
                            :disabled="isShowDetails||diffType=='details'"  class="wprecent100" @on-blur="blurLoanAmt"/>
                        </FormItem>
                    </Col>
                 </Row>
            </div>
        </Form>
         <Spin size="large" fix v-if="loading" class="apply-spin"></Spin>
    </div>
</template>
<script>
import * as utils from "@/projects/basic/assets/js/utils.js"
import vueEvent from "_p/basic/assets/js/vueEvent.js"
import CommonFinance from "../common-finance/common-finance"
import {
    getProductRate,
    getCustRate
} from "@/projects/basic/api/product/financialProducts.js"
export default {
    name:"carLoan",
    data(){
        let validateTerm=(rule,value,callback)=>{
            if(value){
               if(value<this.term[0]||value>this.term[1]){
                    callback(new Error("请输入"+(this.term[0]||"")+"~"+(this.term[1]||"")+"期数"));
                    return;
               }
               if(this.valiateFlag){
                   if(value>this.queryCarProductInfo.costInfo.loanTerm){
                     callback(new Error("租赁期限不能变长"));
                     return;
                   }
               }
            }
            callback()
        }
        let valiateTailPayScale=(rule,value,callback)=>{
            if(value){
                if(value<=this.endBalancePaymentRange&&value>=this.fromBalancePaymentInterval){
                    if(this.carAdditioalLoanForm.downPayScale){
                        if(utils.add(value,this.carAdditioalLoanForm.downPayScale)>100){
                            callback(new Error("尾付与首付比例之和需小于等于100"))
                            return;
                        }
                    }
                }else{
                    callback(new Error('请输入'+this.fromBalancePaymentInterval+'~'+this.endBalancePaymentRange+'的尾款比例'))
                    return;
                }
            }
            callback();
        }
        let valiateTailPayAmt=(rule,value,callback)=>{
            if(value){
                if(value>this.endBalancePaymentRange||value<this.fromBalancePaymentInterval){
                    callback(new Error('请输入'+this.fromBalancePaymentInterval+'~'+this.endBalancePaymentRange+'的尾款金额'))
                    return;
                }
                if(parseFloat(value)>parseFloat(this.carAdditioalLoanForm.loanAmt)){
                    callback('尾款金额应小于等于租赁金额');
                    return;
                }
            }
            callback();
        }
        let checkTailPayAmt=(rule,value,callback)=>{
            if(value){
                // console.log(value,this.carAdditioalLoanForm.loanAmt,"checkTailPayAmt")
                if(parseFloat(value)>parseFloat(this.carAdditioalLoanForm.loanAmt)){
                    callback('尾款金额应小于等于租赁金额');
                    return;
                }
            }
            callback()
        }
        let valiateDownPayScale=(rule,value,callback)=>{
            if(value){
                if(value<=100&&value>=parseFloat(this.downPayRatioMin)){
                    if(this.carAdditioalLoanForm.tailPayScale){
                        if(utils.add(value,this.carAdditioalLoanForm.tailPayScale)>100){
                            callback(new Error("首付与尾付比例之和需小于等于100"))
                            return;
                        }
                    }
                }else{
                    callback(new Error('最低首付比例为'+this.downPayRatioMin+'%','最高首付比例为100%'))
                    return;
                }
            }
            callback();
        }
        let valiateLoanAmt=(rule,value,callback)=>{
            if(value){
                if(!utils.isTwoDicmal(value)) {
                    callback(new Error("最多为两位小数"))
                    return;
                }
                if(this.valiateFlag){
                    console.log(parseFloat(value),parseFloat(this.queryCarProductInfo.costInfo.loanAmt))
                    if(parseFloat(value)>parseFloat(this.queryCarProductInfo.costInfo.loanAmt)){
                        callback(new Error("贷额不能增加"))
                        return;
                    }
                }
                if(this.isReconsider){
                    if(parseFloat(value)>parseFloat(this.queryCarProductInfo.costInfo.loanAmt)){
                        callback(new Error("租赁金额不能增加"))
                        return;
                    }
                }
                // if(this.carAdditioalLoanForm.tailPayAmt){
                //     if(parseFloat(value)<parseFloat(this.carAdditioalLoanForm.tailPayAmt)){
                //         callback(new Error("租赁金额应大于等于尾款金额"))
                //         return;
                //     }

                // }
            }
            callback();
        }
        let valitateDownPayScale=(rule,value,callback)=>{
            if(value){
                if(!utils.isTwoDicmal(value)) {
                    callback(new Error("最多为两位小数"))
                    return;
                }
                if(this.valiateFlag){
                    if(parseFloat(value)<parseFloat(this.queryCarProductInfo.costInfo.downPayScale)){
                        callback(new Error("首付比例不能降低"))
                        return;
                    }
                }
            }
            callback();
        }
        return{
            loading:false,
           tempLoanLimit:0,
           maxAmt:"",
           curSpan:"",
           term:[
            //    12,21
           ],
           tempCarRateList:[],
           endBalancePaymentRange:'',
           fromBalancePaymentInterval:'',
           valiateTailPayScale:valiateTailPayScale,
           checkTailPayAmt:checkTailPayAmt,
           valiateTailPayAmt:valiateTailPayAmt,
           valitateDownPayScale:valitateDownPayScale,
           finalPaymentBenchmark:"",

           valiateDownPayScale:valiateDownPayScale,
           downPayRatioMin:"",
           carAdditioalLoanForm:{
                carPrice:this.carPrice,
                loanLimit:0,
                downPayScale:"",
                downPayAmt:"",
                tailPayScale:"",
                tailPayAmt:"",
                loanAmt:"",
                additionalTotalAmt:"0",
                //项目融资列表
                additionalAmtList:[
                ],
           },
           carAdditioalLoanFormValiate:{
                additionalTotalAmt:[
                    {required: true, message: "附加租赁项金额不能为空", trigger: "change"},
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"change"}
                ],
                carPrice:[
                    {required: true, message: "车辆价格不能为空", trigger: "blur"},
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"blur"}
                ],
                loanLimit:[
                    {required: true, message:"租赁期限不能为空", trigger: "blur",type:"number"},
                    {validator:validateTerm,trigger: "blur",type:"number"}
                ],
                downPayScale:[
                    {required: true, message: "首付比例不能为空", trigger: "blur"},
                    {validator:valitateDownPayScale,trigger:"blur"}
                ],
                downPayAmt:[
                    {required: true, message: "首付金额不能为空", trigger: "blur"},
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"blur"}
                ],
                tailPayScale:[
                    {required: true, message: "尾款比例不能为空", trigger: "blur"},
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"blur"}
                ],
                tailPayAmt:[
                    {required: true, message: "尾款金额不能为空", trigger: "blur"},
                    {validator:(rule,value,callback)=>{utils.isTwoDicmalValiate(rule,value,callback)},trigger:"blur"}
                ],
                loanAmt:[
                    {required: true, message: "租赁金额不能为空", trigger: "blur"},
                    {validator:valiateLoanAmt,trigger:"blur"}
                ]
           }
        }
    },
    computed:{

    },
    components:{
        CommonFinance,
    },
    props:{
        diffType:{
            type:String,
        },
        selectProductInfo:{
            type:Object,
        },
        selectCarProductInfo:{
            type:Object
        },
        queryCarProductInfo:{
            type:Object,
        },
        isCarTail:{
            type:Boolean
        },
        uuid:{
            type:String,
        },
        carPrice:{
            type:String,
        },
        isShowDetails:{
            type:Boolean,
        },
        isReconsider:{
            type:Boolean,
        },
        isSubProduct:{
            type:String,
        },
        isAdditional:{
            type:String
        },
        mainProductId:{
            type:String
        },
        applyNo:{
            type:String
        },
        saveStatus:{
            type:Object
        },
        valiateFlag:{
            type:Boolean,
        },
        salePrice:{
            type:String,
        },
        isAutoCount:{
            type:Boolean
        },
        pageIdentify:{
            type:String
        },
        queryCarLoanRebate:{
            type:Function
        },
        isInternet:{
            type:String
        },
        businessOption:{
            type:Object,
            default:{},
            require:false,
        },
    },
    watch:{
        carPrice(val){
            if(val){
                this.carAdditioalLoanForm.carPrice=val;
                console.log(111111)
                if(this.isAutoCount){
                    this.inputPrice();
                }
            }
        },
        salePrice(val){
            if(val){
                this.$emit('changeCarPrice',val)

            }
        },
        selectProductInfo(val){
            if(Object.keys(val).length>0){
                let tempPeriod=[];
                tempPeriod[0]=val.detailInfo.loanPeriodMin;
                tempPeriod[1]=val.detailInfo.loanPeriodMax;
                this.term=tempPeriod;
                // 若果尾款存在
                if(val.isTail=="1"){
                    this.fromBalancePaymentInterval=val.repaymentData.repaymentMethods.fromBalancePaymentInterval;
                    this.endBalancePaymentRange=val.repaymentData.repaymentMethods.endBalancePaymentRange;
                    this.finalPaymentBenchmark=val.repaymentData.repaymentMethods.finalPaymentBenchmark;
                    //  比例
                    if(val.repaymentData.repaymentMethods.intervalSelectionFinalPayment=='0'){
                        this.carAdditioalLoanFormValiate['tailPayScale'].push({
                            validator: this.valiateTailPayScale,trigger:'blur'
                        })
                        this.carAdditioalLoanFormValiate['tailPayAmt'].push({
                            validator: this.checkTailPayAmt,trigger:'blur'
                        })

                    }else if(val.repaymentData.repaymentMethods.intervalSelectionFinalPayment=='1'){
                        this.carAdditioalLoanFormValiate['tailPayAmt'].push({
                            validator: this.valiateTailPayAmt,trigger:'blur'
                        })
                    }
                }
                this.downPayRatioMin=val.detailInfo.downPayRatioMin;
                this.carAdditioalLoanFormValiate['downPayScale'].push({
                    validator: this.valiateDownPayScale,trigger:'blur'
                })
            }
        },
        queryCarProductInfo(val){
            console.log(val,"val-carLoan")
            if(Object.keys(val).length>0){
                //input的必输类型为string
                this.carAdditioalLoanForm.carPrice=val.costInfo.contractAmt.toString();
                if(val.costInfo.addAmt){
                    this.carAdditioalLoanForm.additionalTotalAmt=val.costInfo.addAmt.toString();
                    vueEvent.$emit('to-additionalPrice',val.costInfo.addAmt,this.uuid);
                }
                this.carAdditioalLoanForm.loanLimit=val.costInfo.loanTerm;
                this.tempLoanLimit=val.costInfo.loanTerm;
                // this.matchRate(val.costInfo.loanTerm);
                this.carAdditioalLoanForm.downPayScale=val.costInfo.downPayScale.toString();
                this.carAdditioalLoanForm.downPayAmt=val.costInfo.downPayAmt.toString();
                if(val.isTail=="1"){
                    this.carAdditioalLoanForm.tailPayScale=val.costInfo.tailPayScale.toString();
                    this.carAdditioalLoanForm.tailPayAmt=val.costInfo.tailPayAmt.toString();
                }
                this.carAdditioalLoanForm.loanAmt=val.costInfo.loanAmt.toString();
                this.maxAmt=val.costInfo.maxDiscountAmt;
            }
        },
        "carAdditioalLoanForm.carPrice":{
            immediate: true,
            handler(val){
                vueEvent.$emit('to-carPrice',val,this.uuid);
            }
        },
        "carAdditioalLoanForm.downPayAmt":{
            immediate: true,
            handler(val){
                vueEvent.$emit('to-carDownPayAmt',val,this.uuid);
            }
        },
        "carAdditioalLoanForm.loanAmt":{
            immediate: true,
            handler(val){
                vueEvent.$emit('to-carLoanAmt',val,this.uuid);
            }
        }
    },
    created(){

    },
    mounted(){
        let _this=this;
    },
    beforeDestroy () {
    },
    methods:{
        acceptAddAmt(val){
            vueEvent.$emit('to-additionalPrice',val,this.uuid);
        },
        resetAddAmt(val){
            this.inputPrice()
            vueEvent.$emit('to-additionalPrice',val,this.uuid);
        },
        changeNumber() {
            document.getElementsByClassName('ivu-input-number')[0].getElementsByTagName('input')[0].addEventListener('keyup', (e) => {
                let str = '' + e.target.value;
                if (str.indexOf('.') != -1) {
                    let arr = str.split('');
                    console.log(arr,"arr")
                    arr.splice(arr.length - 1);
                    let str2 = arr.join('');
                    e.target.value = +str2;
                }
            })
        },
        selectTerm(item,index){
            // 当为新增或编辑的时候
            if(!this.isShowDetails&&this.diffType!='details'){
                this.curSpan=index;
                let loanLimit=this.carAdditioalLoanForm.loanLimit;
                this.carAdditioalLoanForm.loanLimit=parseFloat(item);
                this.tempLoanLimit=parseFloat(item);
                this.$refs.carAdditioalLoanForm.validateField('loanLimit',(errorMsg)=>{
                    if(!errorMsg){
                        if(loanLimit!=item){
                            this.matchRate(item).then((data)=>{
                                if(data){
                                    this.enclosureAmt();
                                }
                            });
                        }
                    }
                });
            }
        },
        inputLoanLimit(){
            let loanLimit=parseFloat(this.carAdditioalLoanForm.loanLimit)
            this.$refs.carAdditioalLoanForm.validateField('loanLimit',(errorMsg)=>{
                if(!errorMsg){
                      // 当期限改变调接口
                    if(this.tempLoanLimit!=this.carAdditioalLoanForm.loanLimit){
                        this.matchRate(loanLimit).then((data)=>{
                            if(data){
                                this.enclosureAmt();
                            }
                        });
                    }
                }
            });
            this.tempLoanLimit=this.carAdditioalLoanForm.loanLimit;
        },
        resetLoanLimit(){
            this.tempLoanLimit=0;
        },
        // 车辆价格失去焦点
        blurCarPrice(){
            this.$refs.carAdditioalLoanForm.validateField('carPrice',(errorMsg)=>{
                if(!errorMsg){
                    this.inputPrice();
                }
            });
        },
        // 首付比例失去焦点
        blurDownPayScale(){
            this.$refs.carAdditioalLoanForm.validateField('downPayScale',(errorMsg)=>{
                if(!errorMsg){
                    this.inputPayScale('downPayScale')
                }
            });
        },
        // 首付金额失去焦点
        blurDownPayAmt(){
            this.$refs.carAdditioalLoanForm.validateField('downPayAmt',(errorMsg)=>{
                if(!errorMsg){
                     this.inputPayAmt('downPayAmt')
                }
            });
        },
        // 租赁金额失去焦点
        blurLoanAmt(){
            this.$refs.carAdditioalLoanForm.validateField('loanAmt',(errorMsg)=>{
                if(!errorMsg){
                     this.inputLoanAmt(this.carAdditioalLoanForm.loanAmt);
                }
            });
        },
        // 当租赁金额，跟租赁期限改变都调此接口
        enclosureAmt(){
            let loanRebate=this.queryCarLoanRebate();
            let selectInfo=this.selectCarProductInfo;
            if(selectInfo.discountOption=="2"){
                if(this.carAdditioalLoanForm.loanLimit==''||(this.carAdditioalLoanForm.loanLimit<this.term[0]||this.carAdditioalLoanForm.loanLimit>this.term[1])){
                    //  this.$Message.warning("请输入"+(this.term[0]||"")+"~"+(this.term[1]||"")+"期数")
                    return false;
                }
                if(this.carAdditioalLoanForm.loanAmt==''||!utils.isTwoDicmal(this.carAdditioalLoanForm.loanAmt)){
                    //  this.$Message.warning("租赁金额不能为空且为两位小数")
                    return false;
                }
                for(let i=0;i<loanRebate.loadList.length;i++){
                    if(!loanRebate.loadList[i].discountId||loanRebate.loadList[i].discountAmt===""){
                        // this.$Message.warning("贴息列表需填写完整")
                        return false;
                    }
                }
                for(let i=0;i<loanRebate.loadList.length;i++){
                    for(let j=0;j<selectInfo.discountInfo.subsidyList.length;j++){
                        // 匹配id校验
                        if(loanRebate.loadList[i].discountId==selectInfo.discountInfo.subsidyList[j].subsidySide){
                            if(selectInfo.discountInfo.subsidyList[j].singleSelect&&(selectInfo.discountInfo.subsidyList[j].singleSelect=='0'||selectInfo.discountInfo.subsidyList[j].singleSelect=='1')){
                                if(loanRebate.loadList[i].discountId!=0){
                                    if(loanRebate.loadList[i].discountAmt>selectInfo.discountInfo.subsidyList[j].subsidyMoney){
                                        this.$Message.warning("贴息额不能大于最高贴息额")
                                        return false;
                                    }
                                }else{
                                    if(loanRebate.loadList[i].discountAmt>loanRebate.maxDisCountAmt){
                                        this.$Message.warning('贴息额不能大于厂商最高贴息额');
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }
                let totalAmt=0;
                let discountList=[];
                // if(type=='loanLimit'&&this.maxAmt){
                if(this.maxAmt){
                    totalAmt=this.maxAmt;
                    for(let i=0;i<loanRebate.loadList.length;i++){
                        let tempobj={};
                        if(loanRebate.loadList[i].discountId!='0'){
                            totalAmt=utils.add(totalAmt,loanRebate.loadList[i].discountAmt);
                            tempobj.discountAmt=loanRebate.loadList[i].discountAmt;
                        }else{
                            tempobj.discountAmt=this.maxAmt;
                        }
                        tempobj.discountId=loanRebate.loadList[i].discountId
                        discountList.push(tempobj);
                    }
                }else{
                    for(let i=0;i<loanRebate.loadList.length;i++){
                        let tempobj={};
                        totalAmt=utils.add(totalAmt,loanRebate.loadList[i].discountAmt)
                        tempobj.discountId=loanRebate.loadList[i].discountId
                        tempobj.discountAmt=loanRebate.loadList[i].discountAmt;
                        discountList.push(tempobj);
                    }
                }
                console.log(discountList,"discountList")
                let param={};
                param.productId=selectInfo.baseInfo.id;
                param.loanAmt=this.carAdditioalLoanForm.loanAmt;
                param.loanTerm=this.carAdditioalLoanForm.loanLimit;
                param.settleRate=loanRebate.settleRate;
                param.discountAmt=totalAmt;
                param.discountList=discountList;
                getCustRate(param).then(res=>{
                    if(res.code=="0000"){
                       if(res.data.custRate||res.data.custRate==0){
                            loanRebate.custRate=res.data.custRate;
                            if(res.data.discountList.length>0){
                                vueEvent.$emit('to-discountList',res.data.discountList,this.uuid);
                            }
                        }
                    }
                })
            }
        },
        // 匹配rate值
        matchRate(loanLimit){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            let obj={
                applyNo:this.applyNo,
                id:this.mainProductId,
                loanTerm :loanLimit,
                costType:"01",
            }
            this.loading=true;
            return new Promise((resolve, reject) => {
                getProductRate(obj).then(res=>{
                    this.loading=false;
                    if(res.code=="0000"){
                         this.$Message.success("获取客户利率成功");
                        if(res.data){
                            if(res.data.main){
                                let maxdiscountAmt="";
                                if(res.data.mainDiscount.discountOption!=0){
                                    maxdiscountAmt=res.data.mainDiscount.discountInfo.maxdiscountAmt;
                                    this.maxAmt=res.data.mainDiscount.discountInfo.maxdiscountAmt;
                                }
                                vueEvent.$emit('to-carLoanRate',res.data.main.custRate,res.data.main.clearRate,res.data.main.addPointValue,maxdiscountAmt,this.uuid);
                            }else{
                                vueEvent.$emit('to-carLoanRate','','','','',this.uuid);
                            }
                            resolve(res.data)
                        }
                    }else{
                         this.loading=false;
                         reject(res.message)
                    }
                })
            })
        },
        //输入比例
        inputPayScale(type){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            if(utils.isTwoDicmal(this.carAdditioalLoanForm[type])){
                if(type=="downPayScale"){
                    let carPriceError="",totalAmtError="";
                    this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                        carPriceError=carPriceError;
                    })
                    this.$refs.carAdditioalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                        totalAmtError=totalAmtError;
                    })
                    if(!carPriceError||!totalAmtError){
                          let  multiplyAmt=utils.multiply(utils.add(this.carAdditioalLoanForm.carPrice,this.carAdditioalLoanForm.additionalTotalAmt),utils.divided(this.carAdditioalLoanForm[type],100).toFixed(4));
                          let  payAmt=Math.ceil(utils.multiply(multiplyAmt,100))/100;
                        //   let  payAmt=Math.round(multiplyAmt*100)/100;
                          this.carAdditioalLoanForm.downPayAmt=payAmt.toString();
                          this.computedLoanAmt(this.carAdditioalLoanForm.downPayAmt);
                    }
                }else if(type=="tailPayScale"){
                    let multiplyAmt="";
                    if(this.finalPaymentBenchmark=="0"){
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            if(!carPriceError){
                                multiplyAmt=utils.multiply(this.carAdditioalLoanForm.carPrice,utils.divided(this.carAdditioalLoanForm[type],100));

                            }
                        })
                    }else if(this.finalPaymentBenchmark=="1"){
                        this.$refs.carAdditioalLoanForm.validateField('loanAmt',(loanAmtError)=>{
                            if(!loanAmtError){
                                multiplyAmt=utils.multiply(this.carAdditioalLoanForm.loanAmt,utils.divided(this.carAdditioalLoanForm[type],100));
                            }
                        })
                    }else if(this.finalPaymentBenchmark=="2"){
                        let carPriceError="",totalAmtError="";
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            carPriceError=carPriceError;
                        })
                        this.$refs.carAdditioalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                            totalAmtError=totalAmtError;
                        })
                        if(!carPriceError||!totalAmtError){
                             multiplyAmt=utils.multiply(utils.add(this.carAdditioalLoanForm.carPrice,this.carAdditioalLoanForm.additionalTotalAmt),utils.divided(this.carAdditioalLoanForm[type],100));
                        }
                    }  else if(this.finalPaymentBenchmark=="3"){
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            if(!carPriceError){
                                this.$refs.carAdditioalLoanForm.validateField('downPayScale',(downPayScaleError)=>{
                                    if(!downPayScaleError){
                                        let price=utils.multiply(this.carAdditioalLoanForm.carPrice,utils.divided(utils.sub(100,this.carAdditioalLoanForm.downPayScale),100))
                                         multiplyAmt=utils.multiply(price,utils.divided(this.carAdditioalLoanForm[type],100));
                                    }
                                });
                            }
                        });
                    }
                    let payAmt=Math.round(utils.multiply(multiplyAmt,100))/100;
                    this.carAdditioalLoanForm.tailPayAmt=payAmt.toString();
                }
            }
            if(type=="downPayScale"){
                this.$refs.carAdditioalLoanForm.validateField('downPayAmt');
                if(this.isCarTail){
                    if(this.carAdditioalLoanForm.tailPayScale){
                        if(utils.add(this.carAdditioalLoanForm.tailPayScale,this.carAdditioalLoanForm.downPayScale)<=100){
                            this.$refs.carAdditioalLoanForm.validateField('tailPayScale');
                        }
                    }
                }
            }else if(type=="tailPayScale"){
                this.$refs.carAdditioalLoanForm.validateField('tailPayAmt');
                if(this.carAdditioalLoanForm.downPayScale){
                    if(utils.add(this.carAdditioalLoanForm.downPayScale,this.carAdditioalLoanForm.tailPayScale)<=100){
                        this.$refs.carAdditioalLoanForm.validateField('downPayScale');
                    }
                }
            }
        },
        //输入金额
        inputPayAmt(type){
            this.saveStatus.disabled=true;
            this.saveStatus.ismodify=true;
            if(utils.isTwoDicmal(this.carAdditioalLoanForm[type])){
                if(type=="downPayAmt"){
                    let carPriceError="",totalAmtError="";
                    this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                        carPriceError=carPriceError;
                    })
                    this.$refs.carAdditioalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                        totalAmtError=totalAmtError;
                    })
                    if(!carPriceError||!totalAmtError){
                       let  multiplyScale=utils.multiply(utils.divided(this.carAdditioalLoanForm[type],utils.add(this.carAdditioalLoanForm.carPrice,this.carAdditioalLoanForm.additionalTotalAmt)),100);
                    //    let payScale=Math.round(multiplyScale*100)/100;
                        let payScale=Math.floor(utils.multiply(multiplyScale,100))/100
                       this.carAdditioalLoanForm.downPayScale=payScale.toString();
                    }
                    this.computedLoanAmt(this.carAdditioalLoanForm[type]);
                }else if(type=="tailPayAmt"){
                    let multiplyScale="";
                    if(this.finalPaymentBenchmark=="0"){
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            if(!carPriceError){
                                multiplyScale=utils.multiply(utils.divided(this.carAdditioalLoanForm[type],this.carAdditioalLoanForm.carPrice),100);
                            }
                        })
                    }else if(this.finalPaymentBenchmark=="1"){
                        this.$refs.carAdditioalLoanForm.validateField('loanAmt',(loanAmtError)=>{
                            if(!loanAmtError){
                                multiplyScale=utils.multiply(utils.divided(this.carAdditioalLoanForm[type],this.carAdditioalLoanForm.loanAmt),100);
                            }
                        })
                    }else if(this.finalPaymentBenchmark=="2"){
                        let carPriceError="",totalAmtError="";
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            carPriceError=carPriceError;
                        })
                        this.$refs.carAdditioalLoanForm.validateField('additionalTotalAmt',(totalAmtError)=>{
                            totalAmtError=totalAmtError;
                        })
                        if(!carPriceError||!totalAmtError){
                            multiplyScale=utils.multiply(utils.divided(this.carAdditioalLoanForm[type],utils.add(this.carAdditioalLoanForm.carPrice,this.carAdditioalLoanForm.additionalTotalAmt)),100);
                        }
                    }else if(this.finalPaymentBenchmark=="3"){
                        this.$refs.carAdditioalLoanForm.validateField('carPrice',(carPriceError)=>{
                            if(!carPriceError){
                                this.$refs.carAdditioalLoanForm.validateField('downPayScale',(downPayScaleError)=>{
                                    if(!downPayScaleError){
                                        let price=utils.multiply(this.carAdditioalLoanForm.carPrice,utils.divided(utils.sub(100,this.carAdditioalLoanForm.downPayScale),100))
                                         multiplyScale=utils.multiply(utils.divided(this.carAdditioalLoanForm[type],price),100);
                                    }
                                });
                            }
                        });
                    }
                    let payScale=Math.round(utils.multiply(multiplyScale,100))/100;
                    this.carAdditioalLoanForm.tailPayScale=payScale.toString();
                }
            }
            if(type=="downPayAmt"){
                this.$refs.carAdditioalLoanForm.validateField('downPayScale')
            }else if(type=="tailPayAmt"){
                this.$refs.carAdditioalLoanForm.validateField('tailPayScale')
            }
        },
        // 输入车辆价格或附加金额
        inputPrice(){
            if(utils.isTwoDicmal(this.carAdditioalLoanForm.carPrice)&&utils.isTwoDicmal(this.carAdditioalLoanForm.additionalTotalAmt)){
                if(this.carAdditioalLoanForm.downPayScale){
                    this.inputPayScale('downPayScale')
                }
            }
        },
         // 当尾款金额与租赁金额不相同，但尾款比例与首付比例为100,触发此事件将尾款金额赋值给租赁金额
         blurPayAmt(tailPayAmt){
            if(this.finalPaymentBenchmark=='0'||this.finalPaymentBenchmark=='2'){
                this.$refs.carAdditioalLoanForm.validateField('tailPayAmt',(tailPayAmttError)=>{
                    if(!tailPayAmttError){
                        if(parseFloat(this.carAdditioalLoanForm.tailPayAmt)!=parseFloat(this.carAdditioalLoanForm.loanAmt)){
                            if(utils.add(this.carAdditioalLoanForm.downPayScale,this.carAdditioalLoanForm.tailPayScale)==100){
                                this.inputLoanAmt(this.carAdditioalLoanForm.tailPayAmt);
                            }
                        }
                    }
                })
            }
        },
        // 计算租赁金额
        computedLoanAmt(downPayAmt){
            console.log(downPayAmt,"downPayAmt")
            let remainAmt=0;
            let carPrice=this.carAdditioalLoanForm.carPrice;
            let addPrice=this.carAdditioalLoanForm.additionalTotalAmt;
            let price=utils.add(carPrice,addPrice)
            remainAmt=utils.sub(price,downPayAmt);
            // if(remainAmt.toString()=='0'){
            //     this.carAdditioalLoanForm.loanAmt="";
            // }else{
            this.carAdditioalLoanForm.loanAmt=remainAmt.toString()||'';
            this.enclosureAmt();
            // }
            // 当租赁金额改变时 finalPaymentBenchmark=1 尾款金额改变
            // 当租赁金额改变时首付比例改变，首付比例改变，finalPaymentBenchmark=3，尾款金额改变
            if(this.finalPaymentBenchmark==1||this.finalPaymentBenchmark==3){
                if(this.carAdditioalLoanForm.tailPayScale){
                    this.inputPayScale('tailPayScale')
                }
            }
        },
        // 输入租赁金额
        inputLoanAmt(loanAmt){
            let carPrice=this.carAdditioalLoanForm.carPrice;
            let addPrice=this.carAdditioalLoanForm.additionalTotalAmt;
            let price=utils.add(carPrice,addPrice)
            this.carAdditioalLoanForm.downPayAmt=utils.sub(price,loanAmt).toString();
            this.inputPayAmt('downPayAmt');
            // 当租赁金额改变时 finalPaymentBenchmark=1 尾款金额改变
            // 当租赁金额改变时首付比例改变，首付比例改变，finalPaymentBenchmark=3，尾款金额改变
            if(this.finalPaymentBenchmark==1||this.finalPaymentBenchmark==3){
                if(this.carAdditioalLoanForm.tailPayScale){
                    this.inputPayScale('tailPayScale')
                }
            }
        },
        submitCarLoan(){
            let carVailate=false;
            this.$refs.carAdditioalLoanForm.validate((valid) => {
                if(valid){
                    carVailate=valid;
                }else{
                    carVailate=false;
                }
            })
            if(carVailate){
                return this.carAdditioalLoanForm;
            }else{
                return false;
            }
        },
        submitCarAddLoan(){
            let carAddValiate=false;
            this.$refs.carAdditioalLoanForm.validate((valid) => {
                if(valid){
                    carAddValiate=valid;
                }else{
                    carAddValiate=false;
                }
            })
            if(carAddValiate){
                 return this.carAdditioalLoanForm
            }else{
                return false;
            }
        },
        // 清空数据
        resetCarAddLoan(){
            let self=this;
            this.$refs["carAdditioalLoanForm"].fields.forEach(function (e) {
                if (e.prop !="carPrice") {
                    e.resetField();
                    vueEvent.$emit('to-additionalPrice',0,self.uuid);

                }
            })
            // this.carAdditioalLoanForm.carPrice=this.carPrice;
        }
    }
}
</script>
<style scoped>
.carAdditioalLoan /deep/ .ivu-input-number-handler-wrap{
    display: none;
}
.term span{
    height: 20px;
    display: inline-block;
    vertical-align: middle;
    line-height: 20px;
    margin-left: 5px;
    text-align: center;
    cursor: pointer;
}
.term span>i{
    font-style: normal;
    height: 20px;
    width: 20px;
    display: inline-block;
}
.term span.active>i,.term span>i:hover{
    background: #EB9620;
    color: white;
}
.w400{
    width: 400px;
}
</style>
