<template>
    <div class="config-list">
        <RadioGroup v-model="type">
            <div class="item">
                <Radio :disabled="disableChoice" class="choice" label="TYPE_NOT_SET">不设置</Radio>
                <span class="tip-info">日和周只能设置其中之一</span>
            </div>
            <div class="item">
                <Radio :disabled="disableChoice" class="choice" label="TYPE_RANGE">区间</Radio>
                从<Select v-model="valueRange.start" :disabled="type!=TYPE_RANGE || disableChoice" class="w80">
                <Option v-for="(v, k) of WEEK_MAP" :key="`week-pre-Lf13-${v}`" :value="v">{{ k }}</Option>
            </Select>
                至<Select v-model="valueRange.end" :disabled="type!=TYPE_RANGE || disableChoice" class="w80">
                <Option v-for="(v, k) of WEEK_MAP" :key="`week-next-1fas-${v}`" :value="v">{{ k }}</Option>
            </Select>
            </div>
            <div class="item">
                <Radio :disabled="disableChoice" class="choice" label="TYPE_LOOP">循环</Radio>
                从<Select v-model="valueLoop.start" :disabled="type!=TYPE_LOOP || disableChoice" class="w80">
                <Option v-for="(v, k) of WEEK_MAP" :key="`week-pre-Lf13-${v}`" :value="v">{{ k }}</Option>
            </Select>开始，间隔
                <InputNumber v-model="valueLoop.interval" :disabled="type!=TYPE_LOOP || disableChoice" :max="maxValue" :min="minValue"
                             :precision="0" class="w60"/>
                天
            </div>
            <div class="item">
                <Radio :disabled="disableChoice" class="choice" label="TYPE_SPECIFY">指定</Radio>
                <div class="list">
                    <CheckboxGroup v-model="valueList" @on-change="sortValueList">
                        <Checkbox v-for="(v, k) of WEEK_MAP" :key="`key-01jfs-${v}`"
                                  :disabled="type!=TYPE_SPECIFY || disableChoice" :label="v" class="list-check-item">
                            <span>{{ k }}</span></Checkbox>
                    </CheckboxGroup>
                </div>
            </div>
        </RadioGroup>
    </div>
</template>

<script>
import mixin from './mixin'
import {replaceWeekName, WEEK_MAP_EN} from './const.js'

const WEEK_MAP = {
    '日': 1,
    '一': 2,
    '二': 3,
    '三': 4,
    '四': 5,
    '五': 6,
    '六': 7,
}

export default {
    name: 'week',
    mixins: [mixin],
    props: {
        day: {
            type: String,
            default: '*'
        }
    },
    data() {
        return {
            WEEK_MAP,
            WEEK_MAP_EN
        }
    },
    computed: {
        disableChoice() {
            return (this.day && this.day !== '?') || this.disabled
        }
    },
    watch: {
        value_c(newVal, oldVal) {
            // 如果设置日，那么星期就直接不设置
            this.updateValue()
        },
        day(newVal) {
            // console.info('new day: ' + newVal)
            this.updateValue()
        }
    },
    methods: {
        updateValue() {
            this.$emit('change', this.disableChoice ? '?' : this.value_c)
        },
        preProcessProp(c) {
            return replaceWeekName(c)
        }
    },
    created() {
        this.DEFAULT_VALUE = '*'
        // 1表示周日
        this.minValue = 0
        this.maxValue = 6
        this.valueRange.start = 2
        this.valueRange.end = 6
        this.valueLoop.start = 2
        this.valueLoop.interval = 1
        this.parseProp(this.prop)
    }
}
</script>

<style scoped>

.config-list {
    text-align: left;
    margin: 0 10px 10px 10px;
}

.item {
    margin-top: 5px;
}

.tip-info {
    color: #999
}

.choice {
    border: 1px solid transparent;
    padding: 5px 8px;
}

.choice:hover {
    border: 1px solid #2d8cf0;
}

.w80 {
    width: 80px;
}

.ivu-input-number, .ivu-select {
    margin-left: 5px;
    margin-right: 5px;
}

.list {
    margin: 0 20px;
}

.list-check-item {
    padding: 1px 3px;
}
</style>
