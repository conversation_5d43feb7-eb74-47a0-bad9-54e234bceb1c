/*
// FIXME 锚点、图形控制坐标系
(0, 0) ---------- (0.5, 0) ---------- (1, 0)
|                                          |
|                                          |
|                                          |
(0, 0.5)                            (1, 0.5)
|                                          |
|                                          |
|                                          |
(0, 1) ---------- (0.5, 1) ---------- (1, 1)
*/

// 锚点坐标
const anchorPoints = [
    [0, 0],
    [0, 1],
    [1, 0],
    [1, 1],
    [0.5, 0],
    [0.5, 1],
    [0, 0.35],
    [1, 0.35]
]

export default [
    {
        name: 'jpdl-flow',
        label: 'jpdlFlow',
        lang: '流程节点',
        icon: '',
        enable: true,
        children: [
            {
                type: 'jpdl_start',
                label: '开始节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'start_node',
                prefix: 'StartNode_',
                configKey: 'start'
            },
            {
                type: 'jpdl_end',
                label: '结束节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'end_node',
                prefix: 'EndNode_',
                configKey: 'end'
            },
            {
                type: 'jpdl_artific',
                label: '人工节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'artific_node',
                prefix: 'TaskNode_',
                configKey: 'artific'
            },
            {
                type: 'jpdl_auto',
                label: '自动节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'auto_node',
                prefix: 'AutoNode_',
                configKey: 'auto'
            },
            {
                type: 'jpdl_rule',
                label: '规则节点',
                enable: false,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'rule_node',
                prefix: 'RuleNode_',
                configKey: 'rule'
            },
            {
                type: 'sub_process',
                label: '子流程节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,
                icon: 'sub_pro_node',
                prefix: 'SubFlowNode_',
                configKey: 'subprocess'
            },
            {
                type: 'jpdl_wait',
                label: '等待节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'wait_node',
                prefix: 'WaitNode_',
                configKey: 'wait'
            },
            {
                type: 'jpdl_timer',
                label: '定时节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'timer_node',
                prefix: 'TimerNode_',
                configKey: 'timer'
            },
            {
                type: 'jpdl_virtual',
                label: '虚拟节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'virtual_node',
                prefix: 'VirtualNode_',
                configKey: 'virtual'
            },
            {
                type: 'jpdl_parlbran',
                label: '并行分支节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'parlbran_node',
                prefix: 'ForkNode_',
                configKey: 'fork'
            },
            {
                type: 'jpdl_parljoin',
                label: '并行汇聚节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,

                icon: 'parljoin_node',
                prefix: 'JoinNode_',
                configKey: 'join'
            },
            {
                type: 'jpdl_decision',
                label: '决策分支节点',
                enable: true,
                width: 32,
                height: 46,
                minWidth: 32,
                minHeight: 46,
                anchorPoints: anchorPoints,
                icon: 'decision_node',
                prefix: 'DecisionNode_',
                configKey: 'decision',
            }
        ]
    }
]
