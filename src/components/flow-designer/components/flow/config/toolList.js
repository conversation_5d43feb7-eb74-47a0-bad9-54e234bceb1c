export default {
    // 工具列表
    toolList: [
        {
            name: 'undo',
            label: 'Undo',
            lang: '回退',
            type: 'normal',
            icon: 'undo',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+z',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: false
            }
        },
        {
            name: 'clearLog',
            label: 'ClearLog',
            lang: '清除记录',
            type: 'normal',
            icon: 'clear_log',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+shift+l',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: false
            }
        },
        {
            name: 'redo',
            label: 'redo',
            lang: '重做',
            type: 'normal',
            icon: 'redo',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+shift+z',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: true
            }
        },
        {
            name: 'copy',
            label: 'Copy',
            lang: '复制',
            type: 'normal',
            icon: 'copy',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+c',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: ['node'],
                divider: false
            }
        },
        {
            name: 'paste',
            label: 'Paste',
            lang: '粘贴',
            type: 'normal',
            icon: 'paste',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+v',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: ['canvas', 'node'],
                divider: false
            }
        },
        {
            name: 'delete',
            label: 'Delete',
            lang: '删除',
            type: 'normal',
            icon: 'trash',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'del',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['node', 'edge'],
                divider: true
            }
        },
        {
            name: 'resetToLine',
            label: 'resetToLine',
            lang: '设为直线',
            type: 'normal',
            icon: 'undo',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: '',
            toolbar: {
                enable: false,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['edge'],
                divider: true
            }
        },
        {
            name: 'clear',
            label: 'Clear',
            lang: '清除画布',
            type: 'normal',
            icon: 'delete',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+shift+e',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: true
            }
        },
        {
            name: 'export',
            label: 'download',
            lang: '导出图片',
            type: 'normal',
            icon: 'export',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: '',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: false
            },
            // 默认选中项index
            selected: 0,
            lockLabel: true,
        },
        {
            name: 'setting',
            label: 'setting',
            lang: '流程全局设置',
            type: 'normal',
            icon: 'mzicon-setting',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+0',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: false
            }
        },
        {
            name: 'check',
            label: 'check',
            lang: '检查',
            type: 'normal',
            icon: 'tickcheckmarkac',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+1',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['canvas'],
                divider: false
            }
        },
        {
            name: 'save',
            label: 'save',
            lang: '保存',
            type: 'normal',
            icon: 'save',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+2',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: [],
                divider: false
            }
        },
        {
            name: 'deploy',
            label: 'deploy',
            lang: '部署',
            type: 'normal',
            icon: 'fabu',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design'],
            shortcuts: 'mod+3',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: [],
                divider: false
            }
        },
        {
            name: 'undeploy',
            label: 'undeploy',
            lang: '反部署',
            type: 'normal',
            icon: 'quxiaofabu',
            enable: true,
            enableMode: ['design'],
            disabled: false,
            disabledMode: ['design',],
            shortcuts: 'mod+4',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: [],
                divider: false
            }
        },
        {
            name: 'play',
            label: 'play',
            lang: '播放',
            type: 'normal',
            icon: 'play',
            enable: true,
            enableMode: ['history'],
            disabled: false,
            disabledMode: ['history',],
            shortcuts: 'mod+4',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['history'],
                divider: false
            }
        },
        {
            name: 'pause',
            label: 'pause',
            lang: '暂停',
            type: 'normal',
            icon: 'pause',
            enable: true,
            enableMode: ['history'],
            disabled: false,
            disabledMode: ['history',],
            shortcuts: 'mod+4',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['history'],
                divider: false
            }
        },
        {
            name: 'stop',
            label: 'stop',
            lang: '停止',
            type: 'normal',
            icon: 'stop',
            enable: true,
            enableMode: ['history'],
            disabled: false,
            disabledMode: ['history',],
            shortcuts: 'mod+4',
            toolbar: {
                enable: true,
                position: 'left',
                divider: true
            },
            contextmenu: {
                enable: true,
                target: ['history'],
                divider: false
            }
        },
        {
            name: 'fullscreen',
            label: 'fullscreen',
            lang: '全屏',
            type: 'normal',
            icon: 'fullscreen',
            enable: true,
            enableMode: ['design', 'history'],
            disabled: false,
            disabledMode: ['design', 'history'],
            shortcuts: '',
            toolbar: {
                enable: true,
                position: 'left',
                divider: false
            },
            contextmenu: {
                enable: true,
                target: [],
                divider: false
            }
        }
    ]
}
