<style lang="less" rel="stylesheet/less" scoped>
.panel-left {
    float: left;
    margin-top: 2px;

    .card-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        align-content: flex-start;
    }
}
</style>

<template>
    <CardBox :width="120" class="panel-left" placement="left" position="right">
        <CardItem
            v-for="(item, index) in materials"
            :key="index"
            :bold="true"
            :enableFold="true"
            :title="item.lang || item.label"
        >
            <NodeElement
                v-for="(child, childIndex) in item.children.filter(target => target.enable)"
                :key="childIndex"
                :editor="editor"
                :info="child" :title="child.label"
            >
            </NodeElement>
        </CardItem>
    </CardBox>
</template>

<script>
import config from '../config/index'
import CardBox from '../components/CardBox'
import CardItem from '../components/CardItem'
import NodeElement from '../components/NodeElement'

export default {
    name: 'PanelLeft',
    props: {
        editor: {
            type: Object,
            required: true
        }
    },
    components: {
        CardBox,
        CardItem,
        NodeElement
    },
    data() {
        return {}
    },
    computed: {
        materials() {
            return config && Array.isArray(config.nodeList) ? config.nodeList.filter(item => item.enable) : []
        }
    },
    methods: {}
}
</script>
