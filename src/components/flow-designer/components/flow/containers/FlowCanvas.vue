<style lang="less" rel="stylesheet/less" scoped>
.canvas-box {
    float: left;
    margin-top: 2px;
    margin-left: 2px;
    height: calc(~"100% - 40px");
    overflow: hidden;
    width: calc(~"100% - 122px");
    position: relative;
    .canvas {
        display: inline-block;
        min-width: 960px;
        min-height: 720px;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0);
        overflow: hidden;
        z-index: 30;
        position: relative;
    }
}
</style>

<template>
    <div :id="`canvas-box-${eleId}`" class="canvas-box">
        <div :id="`flow-canvas-${eleId}`" class="canvas">
        </div>
    </div>
</template>

<script>
export default {
    name: 'FlowCanvas',
    props:{
        eleId:{
            type:String,
            default:''
        }
    },
    data() {
        return {}
    },
    created() {
    }
}
</script>
