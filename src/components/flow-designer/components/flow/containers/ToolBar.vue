<style lang="less" rel="stylesheet/less" scoped>
.tool-bar {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-content: flex-start;
    width: 100%;
    padding: 0 10px 0 26px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
    height: 40px;
    transition: all 0.5s ease-in-out;

    .tool-box {
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        align-content: flex-start;
        flex: 1 1 auto;
        z-index: 3;

        &.left {
            justify-content: flex-start;
        }

        &.center {
            justify-content: center;
        }

        &.right {
            justify-content: flex-end;
        }

        .tool-item {
            display: inline-block;

            .link {
                display: inline-block;
                line-height: 1;
                vertical-align: middle;

                .icon {
                }

                .img {
                    width: 40px;
                    height: auto;
                }
            }
        }

        .divider {
            height: calc(~'100% - 2px');
        }
    }
}
</style>

<template>
    <div :style="toolBarStyle" class="tool-bar">
        <template v-for="(type, typeIndex) in Object.keys(toolMap)">
            <ToolBox :key="typeIndex" :class="type" mode="horizontal">
                <template v-for="(item, index) in toolMap[type]">
                    <ToolItem
                        v-if="item.type === 'text'"
                        :key="'tool_' + type + '_item_' + index"
                        :active="item.active"
                        :disabled="item.disabled"
                        :title="item.lang"
                        @click.native="handleToolClick(item)"
                    >
                        <template v-slot:label>
                            <XIcon v-if="item.icon" :type="item.icon"></XIcon>
                            <span v-else>{{ item.lang }}</span>
                        </template>
                    </ToolItem>
                    <ToolItem
                        v-if="item.type === 'normal'"
                        :key="'tool_' + type + '_item_' + index"
                        :active="item.active"
                        :disabled="item.disabled"
                        :title="item.lang"
                        @click.native="handleToolClick(item)"
                    >
                        <template v-slot:label>
                            <XIcon v-if="item.icon" :type="item.icon"></XIcon>
                            <span v-else>{{ item.lang }}</span>
                        </template>
                    </ToolItem>
                    <XDivider
                        v-if="item.toolbar.divider"
                        :key="'tool_' + type + '_divider_' + index"
                        class="divider"
                        mode="vertical"
                    />
                </template>
            </ToolBox>
        </template>
        <ToolBox class="right" mode="horizontal">
            <div class="tool-item" title="流程信息">
                <span style="line-height: 30px">
                   流程名称:&nbsp;{{ datas.name }}&nbsp;&nbsp;|&nbsp;&nbsp;流程id:&nbsp;{{ datas.id }}&nbsp;&nbsp;|&nbsp;&nbsp;版本:&nbsp;{{ datas.version }}
                </span>
                <span v-if="mode=='design'" style="line-height: 30px">
                 |&nbsp;&nbsp;状态:&nbsp;{{ datas.isDeployedName }}
              </span>
                <span v-if="mode=='history'" style="line-height: 30px">
                &nbsp;&nbsp;| 流程实例ID:&nbsp;&nbsp;{{ processId }}
              </span>
            </div>
        </ToolBox>
        <Spin v-if="spin" fix>
            <div>
                <Icon size=14 style="animation: ani-demo-spin 1s linear infinite;" type="ios-loading"></Icon>
                <div>加载中...</div>
            </div>
        </Spin>
    </div>
</template>

<script>
import ToolBox from '../components/ToolBox/ToolBox'
import ToolItem from '../components/ToolBox/ToolItem'
import config from '../config/index'
import XIcon from '../../../global/Icon/icon'
import XDivider from '../../../global/Divider/divider'
export default {
    name: 'ToolBar',
    props: {
        datas: {
            type: Object,
            required: true
        },
        spin: {
            type: Boolean,
            required: true
        },
        mode: {
            type: String,
            required: true
        },
        processId: {
            type: String,
            required: false
        }
    },
    components: {
        XIcon,
        XDivider,
        ToolBox,
        ToolItem
    },
    data() {
        return {
            formData: {
                ...config.style
            }
        }
    },
    computed: {
        toolBarStyle() {
            let style = {position: 'relative'}
            return style
        },
        toolMap() {
            let _t = this
            let toolMap = {}
            config.tools.toolList.forEach((item) => {
                if (item.enable && item.toolbar && item.toolbar.enable) {
                    let position = item.toolbar.position
                    if (!toolMap.hasOwnProperty(position)) {
                        toolMap[position] = []
                    }
                    if (item.enableMode.includes(this.mode)) {
                        toolMap[position].push(item)
                    }
                }
            })
            return toolMap
        }
    },
    methods: {
        toggleHandler(val) {
            let _t = this
            _t.isExpand = val !== undefined ? val : !_t.isExpand
        },
        handleDropdownClick(item, type, index, val) {
            let _t = this
            // console.log('handleDropdownClick', item.name)
            if (item.disabled) {
                return
            }
            let child = item.children[val]
            _t.formData[item.name] = child.name
            let payload = {
                context: 'ToolBar',
                event: event,
                name: item.name
            }
            switch (item.name) {
                case 'lineWidth':
                case 'lineType':
                case 'lineDash':
                case 'preview':
                case 'export':
                    payload = {
                        ...payload,
                        data: child.name
                    }
                    break
                case 'zoom':
                case 'startArrow':
                case 'endArrow':
                    payload = {
                        ...payload,
                        data: child.data
                    }
                    break
            }
            // this.parentInfo.handleToolTrigger(payload);
            this.$emit('toolTrigger',payload);
            // 处理选中，更新toolList
            let toolList
            toolList = _t.toolList.map((target) => {
                if (target.name === item.name) {
                    target.selected = val
                }
                return target
            })
        },
        handleToolClick(item, val) {
            let _t = this
            // console.log('handleToolClick', item.name, val)
            if (item.disabled) {
                return
            }
            let payload = {
                context: 'ToolBar',
                name: item.name
            }
            switch (item.name) {
                case 'fill':
                case 'lineColor':
                    _t.formData[item.name] = val
                    payload = {
                        ...payload,
                        data: val
                    }
                    break
                case 'toFront':
                case 'toBack':
                    payload = {
                        ...payload,
                        data: _t.currentItem
                    }
                    break
            }
            // this.parentInfo.handleToolTrigger(payload);
            this.$emit('toolTrigger',payload);
        }
    },
    created() {
    }
}
</script>
