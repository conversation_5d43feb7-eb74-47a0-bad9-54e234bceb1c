<style lang="less" rel="stylesheet/less" scoped>
.card-item {
    display: inline-block;
    width: 100%;
    position: relative;

    .header {
        display: inline-block;
        width: 100%;
        padding: 0;
        position: relative;
        text-align: center;
        &:hover {
            background: rgba(0, 0, 0, .1);
        }

        .title {
            width: 100%;
            height: 39px;
            line-height: 39px;
            color: #000;

            &.bold {
                font-weight: 800;
            }
        }

        .handler {
            position: absolute;
            right: 10px;
            top: 50%;
            margin-top: -9px;
            z-index: 10;
            cursor: pointer;
        }
    }

    .body {
        display: inline-block;
        width: 100%;
        border-bottom: 1px solid rgba(0, 0, 0, .1);
        padding: 1px 1px 2px 1px;
    }
}
</style>

<template>
    <div class="card-item">
        <div class="header">
            <div v-if="title" :class="{ 'title': true, 'bold': bold }">{{ title }}</div>
        </div>
        <div class="body">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CardItem',
    props: {
        title: String,
        bold: Boolean
    },
    data() {
        return {}
    }
}
</script>
