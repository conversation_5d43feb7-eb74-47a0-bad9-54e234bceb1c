import FlowValidate from './FlowValidate'
import StartValidate from './StartValidate'
import EndValidate from './EndValidate'
import AutoValidate from './AutoValidate'
import WaitValidate from './WaitValidate'
import VirtualValidate from './VirtualValidate'
import ForkValidate from './ForkValidate'
import Join<PERSON>alidate from './JoinValidate'
import TimerValidate from './TimerValidate'
import TaskValidate from './TaskValidate'
import EdgeValidate from './EdgeValidate'
import EdgeOnlyParamValidate from './EdgeOnlyParamValidate'
import SubprocessValidate from './SubprocessValidate'

const obj = [
    FlowValidate,
    StartValidate,
    EndValidate,
    AutoValidate,
    WaitValidate,
    VirtualValidate,
    ForkValidate,
    JoinValidate,
    TimerValidate,
    TaskValidate,
    EdgeValidate,
    SubprocessValidate,
    EdgeOnlyParamValidate
]

const validators = {}

validators.init = function () {
    obj.forEach(value => {
        validators[value.name] = value;
    });
}
validators.init();
export default {
    validate(type, el, newData, oldData) {
        return validators[type].validate(el, newData, oldData);
    }
}
