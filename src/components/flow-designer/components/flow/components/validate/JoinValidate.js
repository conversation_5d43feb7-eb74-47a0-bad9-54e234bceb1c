export default {
    name: 'join',
    validate(el, newData, oldData) {
        console.log(newData)
        if (newData.base.name === '' && newData.base.name !== oldData.base.name) {
            el.$Message.error('节点名称不能为空');
            return false
        }
        if (newData.base.matchid === '' && newData.base.matchid !== oldData.base.matchid) {
            el.$Message.error('匹配节点ID为空');
        }

        return true;
    }
}
