export default {
    name: 'edge',
    validate(el, newData, oldData) {
        if (newData.from.indexOf('Fork') < 0) {
            if (newData.content !== oldData.content) {
                if (!newData.content || newData.content == '') {
                    el.$Message.error('路由条件为空');
                }
            }
            if (newData.descflag !== oldData.descflag || newData.description !== oldData.description) {
                if (newData.descflag == 'true' && newData.description == '') {
                    el.$Message.error('业务名称为空');
                }
            }
        }
        return true;
    }
}
