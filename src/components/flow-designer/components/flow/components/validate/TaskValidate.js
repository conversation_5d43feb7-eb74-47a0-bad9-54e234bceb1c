export default {
    name: 'artific',
    validate(el, newData, oldData) {
        console.log(newData)
        if (newData.base.name === '' && newData.base.name !== oldData.base.name) {
            el.$Message.error('节点名称不能为空');
            return false
        }

        if (newData.assigners.pthistory === oldData.assigners.pthistory && newData.assigners.result === oldData.assigners.result) {
            console.log('不校验')
        } else {
            if(newData.assigners.adaptertype==='3'&&newData.assigners.callBackURI===''){
                el.$Message.error('回调URL不能为空');
                return false
            }
            if (newData.assigners.adaptertype!=='3'&&newData.assigners.pthistory === '0' && (!newData.assigners.result || newData.assigners.result === '')) {
                el.$Message.error('流程参与者不能为空');
                return false
            }
        }
        if (newData.assigners.ptassign === oldData.assigners.ptassign && newData.assigners.ptkind === oldData.assigners.ptkind) {
            console.log('不校验')
        } else {
            if (newData.assigners.ptassign === '2' && (newData.assigners.ptkind === '4' || newData.assigners.ptkind === '6')) {
                let orgStr = '', levelStr = '', roleStr = '';
                let result = newData.assigners.result;
                if (result.indexOf('(机构)=') > -1) {
                    orgStr = result.substring(result.indexOf('(机构)=') + 5);
                    if (orgStr.indexOf('(') > -1) {
                        orgStr = orgStr.substring(0, orgStr.indexOf("("))
                    }
                }
                if (result.indexOf('(机构级别)=') > -1) {
                    levelStr = result.substring(result.indexOf('(机构级别)=') + 7);
                    if (levelStr.indexOf('(') > -1) {
                        levelStr = levelStr.substring(0, levelStr.indexOf("("))
                    }
                }
                if (result.indexOf('(岗位)=') > -1) {
                    roleStr = result.substring(result.indexOf('(岗位)=') + 5);
                }
                if (orgStr == '') {
                    el.$Message.error('参与者机构不能为空');
                    return false
                }
                if (roleStr == '') {
                    el.$Message.error('参与者岗位不能为空');
                    return false
                }
                if (newData.assigners.ptkind == '6' && levelStr == '') {
                    el.$Message.error('参与者级别不能为空');
                    return false
                }
            }
        }

        return true;
    }
}
