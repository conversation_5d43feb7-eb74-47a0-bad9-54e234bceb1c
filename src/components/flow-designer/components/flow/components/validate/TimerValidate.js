export default {
    name: 'timer',
    validate(el, newData, oldData) {
        console.log(newData)
        if (newData.base.name === '' && newData.base.name !== oldData.base.name) {
            el.$Message.error('节点名称不能为空');
            return false
        }
        if(newData.base.adaptertype==='3') {
            if ((!newData.base.callBackURI || newData.base.callBackURI === '')) {
                el.$Message.error('回调URI不能为空');
                return false
            }
        }else {
            if (newData.base.adaptername === '' && newData.base.adaptername !== oldData.base.adaptername) {
                el.$Message.error('适配器名不能为空');
                return false
            }

            if (newData.base.adaptertype === '' && newData.base.adaptertype !== oldData.base.adaptertype) {
                el.$Message.error('适配器类型不能为空');
                return false
            }
        }

        if (newData.base.delayday === oldData.base.delayday && newData.base.delayhour === oldData.base.delayhour && newData.base.delayminute === oldData.base.delayminute) {

        } else {
            if ((newData.base.delayday + newData.base.delayhour + newData.base.delayminute) <= 0) {
                el.$Message.error('延迟时间必须大于0');
                return false
            }
        }

        if (newData.base.cycletype == '2' || newData.base.cycletype == '3') {
            if ((newData.base.intervalday + newData.base.intervalhour + newData.base.intervalminute) <= 0) {
                el.$Message.error('间隔时间必须大于0');
                return false
            }
        }
        if (newData.base.cycletype == '3') {
            if ((newData.base.continueday + newData.base.continuehour + newData.base.continueminute) <= 0) {
                el.$Message.error('持续时间必须大于0');
                return false
            }
        }
        return true;
    }
}
