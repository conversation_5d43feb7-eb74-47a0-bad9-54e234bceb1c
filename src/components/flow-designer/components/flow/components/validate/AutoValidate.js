export default {
    name: 'auto',
    validate(el, newData, oldData) {
        if (newData.base.name === '' && oldData.base.name !== newData.base.name) {
            el.$Message.error('节点名称不能为空');
            return false
        }
        if(newData.base.adaptertype==='3'){
            if((!newData.base.callBackURI||newData.base.callBackURI==='')) {
                el.$Message.error('回调URI不能为空');
                return false
            }
        }else {
            if (newData.base.adaptername === '' && oldData.base.adaptername !== newData.base.adaptername) {
                el.$Message.error('适配器名不能为空');
                return false
            }

            if (newData.base.adaptertype === '' && oldData.base.adaptertype !== newData.base.adaptertype) {
                el.$Message.error('适配器类型不能为空');
                return false
            }
        }

        return true;
    }
}
