<template>
    <Modal v-model="config.open" :closable="false" :footerHide="footHide" :maskClosable="maskCloseable"
           :width="750" :zIndex="zIndex" class-name="vertical-center-modal" @on-visible-change="initData">
        <div slot="header" class="ivu-modal-header-inner">
            <span>{{ config.title }}</span>
            <a class="ivu-modal-close" @click="resetValue"><i class="ivu-icon ivu-icon-ios-close"></i></a>
        </div>
        <Row>
            <Col v-if="this.datas.ptkind==='1'">
                <Form inline  ref="userQuery">
                    <FormItem :required="true" :error="errorMsg"  :label-width="50" label="系统">
                        <Select v-model="systemCode" style="width: 100px" @on-change="checkSystemCode">
                            <Option v-for="row in flowConfig.bizSystem" :key="row.id" :value="row.systemCode">
                                {{row.systemName}}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem :required="true" :error="errorMsgGroupId"  :label-width="50" label="群组" v-if="datas.choiceType==='3'||datas.choiceType==='1'">
                        <Select v-model="groupId" style="width: 100px"  @on-change="checkGroupId">
                            <Option v-for="row in groups" :key="row.id" :value="row.id">
                                {{row.groupName}}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem :label-width="40" label="ID">
                        <Input v-model="userId" placeholder="员工id" style="width: 100px" type="text"/>
                    </FormItem>
                    <FormItem :label-width="40" label="姓名">
                        <Input v-model="userName" placeholder="员工名称" style="width: 100px" type="text"/>
                        &nbsp;
                        <Button type="primary" @click="queryUser">查询</Button>
                    </FormItem>
                </Form>
                <Table :columns="userColumn" :data="users" :max-height="250" border size="small"
                       @on-selection-change="setSelectUserData" @on-select-cancel="cancelUserSelect">
                </Table>
                <div style="height: 5px"></div>
                <Page :current="userPage.index" :page-size="10" :total="userPage.total" show-elevator show-total
                      size="small" @on-change="changeUserIndex"/>
            </Col>
            <Col v-if="this.datas.ptkind==='5'">
                <Form inline >
                    <FormItem :required="true" :error="errorMsgGroup"  :label-width="50" label="系统"  @on-change="checkSystemCode">
                        <Select v-model="systemCode" style="width: 100px">
                            <Option v-for="row in flowConfig.bizSystem" :key="row.id" :value="row.systemCode">
                                {{row.systemName}}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem :label-width="40" label="ID">
                        <Input v-model="groupIdCondition" placeholder="群组ID" style="width: 100px" type="text"/>
                    </FormItem>
                    <FormItem :label-width="40" label="名称">
                        <Input v-model="groupNameCondition" placeholder="群组名称" style="width: 100px" type="text"/>
                        &nbsp;
                        <Button type="primary" @click="queryGroup">查询</Button>
                    </FormItem>
                </Form>
                <Table :columns="groupColumn" :data="groups" :max-height="250" border size="small">
                    <template slot="groupChoice" slot-scope="{ row, index }">
                        <Checkbox v-model="row.chose" @on-change="setSelectGroupDatas(row)"/>
                    </template>
                </Table>
                <div style="height: 5px"></div>
                <Page :current="groupPage.index" :page-size="10" :total="groupPage.total" show-elevator show-total
                      size="small" @on-change="changeGroupIndex"/>
            </Col>
        </Row>
        <div slot="footer">
            <Button size="small" @click="resetValue">取消</Button>
            <Button size="small" type="primary" @click="checkAndClose">确定</Button>
        </div>
    </Modal>
</template>

<script>
export default {
    name: 'orgChoice',
    components: {},
    props: {
        config: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        flowConfig:{
            type:Object
        },requestMethods:{
            type:Object,
            default:()=>{
                return {}
            }
        },
    },
    data() {
        return {
            zIndex: 2002,
            footHide: false,
            maskCloseable: false,
            errorMsg:'',
            errorMsgGroup:'',
            errorMsgGroupId:'',
            groupId: '',
            bizGroupId: '',
            userId: '',
            userName:'',
            groupNameCondition: '',
            groupIdCondition: '',
            result: '',
            showResult: '',
            systemCode:'',
            userColumn: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '业务ID',
                    key: 'userId'
                },
                {
                    title: '姓名',
                    key: 'userName'
                }
            ],
            users: [],
            userPage: {
                index: 1,
                total: 0,
            },
            groupColumn: [
                {
                    width: 80,
                    align: 'center',
                    slot: 'groupChoice'
                },
                {
                    title: '群组编号',
                    key: 'groupId',
                    width: 100
                },
                {
                    title: '群组名称',
                    key: 'groupName',
                    tooltip: true
                }
            ],
            groups: [],
            groupPage: {
                index: 1,
                total: 0,
            }
        }
    },
    methods: {
        initData(show){
            if(!show){
                return;
            }
            this.result = this.datas.result;
            this.showResult = this.datas.showResult;
        },
        checkSystemCode(){
           if(this.systemCode!==''){
               this.$nextTick(()=>{
                   this.errorMsg ='';
                   this.errorMsgGroup ='';
                   this.queryGroup();
               })
           }
        },
        checkGroupId(value){
            if(this.groupId!==''){
                for (let index = 0; index < this.groups.length; index++) {
                    if(this.groups[index].id===value){
                        this.bizGroupId = this.groups[index].groupId;
                        break;
                    }
                }
                this.$nextTick(()=>{
                    this.errorMsgGroupId ='';
                })
            }else{
                this.bizGroupId = '';
            }
        },
        queryUser() {
            let self = this;
            if(!this.systemCode||this.systemCode===''){
                this.errorMsg = '不能为空'
                return;
            }
            if((this.datas.choiceType==='3'||this.datas.choiceType==='1')&&(!this.groupId||this.groupId==='')){
                this.errorMsgGroupId = '不能为空';
                return;
            }
            this.errorMsg ='';
            this.errorMsgGroupId ='';
            let temp = (this.result === '') ? [] : (this.result.split(';'));
            this.requestMethods
                .queryUser(this.systemCode,this.bizGroupId,this.userId,this.userName,this.userPage.index,10)
                .then(response=>{
                    if (response.code === '0000') {
                        self.userPage.total = response.data.total;
                        self.users.splice(0,self.users.length)
                        response.data.users.forEach(user=>{
                            let index ,find = false;
                            for (index = 0; index < temp.length; index++) {
                                if(temp[index].indexOf(user.userId)>-1||temp[index].indexOf(user.id)>-1){
                                    find = true;
                                    if(temp[index]===user.id){
                                        temp[index] = 'bizId='+user.userId+'@@@'+this.systemCode+'@@@'+user.id;
                                        if((this.datas.choiceType==='3'||this.datas.choiceType==='1')&&this.groupId!==''){
                                            temp[index] += (','+this.bizGroupId+'@@@'+this.groupId);
                                        }
                                    }
                                    break;
                                }
                            }
                            if(find){
                                user._checked = true;
                            }
                        });
                        this.result = temp.join(";");
                        console.log(this.result,"====result===")
                        self.users.push(...response.data.users);
                    } else {
                        this.$Message.error('数据加载失败');
                    }
                }).catch(e=>{
                    this.$Message.error('数据加载失败');
                })
        },
        changeUserIndex(pageIndex) {
            this.userPage.index = pageIndex;
            this.queryUser();
        },
        setSelectUserData(selection) {
            console.log("choice==>users",selection);
            let temp = (this.result === ''||this.result===undefined) ? [] : (this.result.split(';'));
            let tempShow = (this.showResult === ''||this.showResult===undefined) ? [] : (this.showResult.split(';'));
            for (let index = 0; index < selection.length; index++) {
                let findIndex,find = false;
                for (findIndex = 0; findIndex < temp.length; findIndex++) {
                    if(temp[findIndex]===selection[index].id){
                        temp[findIndex] = 'bizId='+selection[index].userId+'@@@'+this.systemCode+'@@@'+selection[index].id;
                        if((this.datas.choiceType==='3'||this.datas.choiceType==='1')&&this.groupId!==''){
                            temp[findIndex] += (','+this.bizGroupId+'@@@'+this.groupId);
                        }
                        find = true;
                        continue;
                    }
                    if(temp[findIndex].indexOf(selection[index].userId)>-1){
                        find = true;
                        break;
                    }
                }
                if (!find) {
                    temp.push('bizId='+selection[index].userId+'@@@'+this.systemCode+'@@@'+selection[index].id);
                    if((this.datas.choiceType==='3'||this.datas.choiceType==='1')&&this.groupId!==''){
                        temp[findIndex] += (','+this.bizGroupId+'@@@'+this.groupId);
                    }
                }
                if (tempShow.indexOf(selection[index].userName) < 0) {
                    tempShow.push(selection[index].userName);
                }
            }
            let resultArray =[];
            temp.forEach(assign=>{
                if(resultArray.indexOf(assign)<0){
                    resultArray.push(assign);
                }
            })
            this.result = resultArray.join(';');
            this.showResult = tempShow.join(';');
        },
        cancelUserSelect(all,selection){
            console.log("cancel",selection)
            let temp = (this.result === ''||this.result===undefined) ? [] : (this.result.split(';'));
            let tempShow = (this.showResult === ''||this.showResult===undefined) ? [] : (this.showResult.split(';'));
            let configUsers= temp.filter((config)=>{
                // console.log('remove user===>',config,!(config.indexOf(selection.userId) > -1 || config.indexOf(selection.id)>-1))
                return !(config.indexOf(selection.userId) > -1 || config.indexOf(selection.id)>-1)
            })
            console.log(configUsers);
            temp = [];
            console.log('temp===>',temp)
            temp.push(...configUsers);
            if (tempShow.indexOf(selection.userName) > -1) {
                tempShow.splice(tempShow.indexOf(selection.userName), 1);
            }
            this.result = temp.join(';');
            this.showResult = tempShow.join(';');
        },
        queryGroup() {
            let self = this;
            if(!this.systemCode||this.systemCode===''){
                this.errorMsgGroup = '不能为空'
                return;
            }
            this.errorMsgGroup ='';
            this.requestMethods
                .queryGroup(this.systemCode,this.groupIdCondition,this.groupNameCondition,this.groupPage.index,10)
                .then(response=>{
                    if (response.code === '0000') {
                        self.groupPage.total = response.data.total;
                        self.groups.splice(0,self.groups.length);
                        response.data.groups.forEach(group => {
                            group.chose = false;
                            if(this.result.indexOf(group.id)>-1||this.result.indexOf(group.groupId)>-1){
                                group.chose = true;
                                this.result = 'bizId='+group.groupId+'@@@'+group.bizSystemCode+'@@@'+group.id;
                            }
                            self.groups.push(group)
                        })
                    } else {
                        this.$Message.error('数据加载失败');
                    }
                }).catch(e=>{
                this.$Message.error('数据加载失败');
            })
        },
        changeGroupIndex(pageIndex) {
            this.groupPage.index = pageIndex;
            this.queryGroup();
        },
        setSelectGroupDatas(selection) {
            this.groups.forEach(group => {
                let flag = selection.chose;
                group.chose = false;
                if (group.groupId === selection.groupId) {
                    group.chose = flag;
                    if (flag) {
                        this.result = 'bizId='+group.groupId+'@@@'+group.bizSystemCode+'@@@'+group.id;
                        console.log("choice==>group",group);
                        this.showResult = group.groupName;
                    }
                }
            })
        },
        resetValue() {
            this.orgs = [];
            this.roles = [];
            this.users = [];
            this.groups = [];
            this.userPage.total = 0;
            this.userPage.index = 1;
            this.groupPage.total = 0;
            this.groupPage.index = 1;
            this.userCondition = '';
            this.groupNameCondition = '';
            this.groupIdCondition = '';
            this.errorMsg = '';
            this.errorMsgGroup = '';
            this.errorMsgGroupId = '';
            this.systemCode = '';
            this.groupId = '';
            this.bizGroupId = '';
            this.$nextTick(()=>{
                this.config.open = false;
            })
        },
        checkAndClose() {
            if (this.result === '') {
                this.$Message.error('请选参与者');
                return;
            }
            // 多重分配检测
            if (this.datas.ptkind == '1' && this.datas.assignmode == '3') {
                if (this.result.indexOf(';') < 0) {
                    this.$Message.error('多重分配时请至少选择两名人员');
                    return
                }
            }
            this.datas.result = this.result;
            this.datas.showResult = this.showResult;
            this.result = '';
            this.errorMsg = '';
            this.errorMsgGroup = '';
            this.errorMsgGroupId = '';
            this.users = [];
            this.groups = [];
            this.systemCode = '';
            this.userPage.total = 0;
            this.userPage.index = 1;
            this.groupPage.total = 0;
            this.groupPage.index = 1;
            this.groupNameCondition = '';
            this.groupIdCondition = '';
            this.groupId = '';
            this.bizGroupId = '';
            this.config.open = false;
        }
    }
}
</script>
