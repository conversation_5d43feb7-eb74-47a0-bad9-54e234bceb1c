<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="80" label="节点id:">
                            <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="节点名称:">
                            <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="节点描述:">
                            <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'2'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
    </Tabs>
</template>

<script>
import EventRegister from "./EventRegister";
export default {
    name: 'startNodeSetting',
    components:{
        EventRegister
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            eventTypeList: [{
                value: '2',
                label: '节点执行结束时'
            }]
        }
    },
    methods: {
    },
    created(){
    }
}
</script>
