import defaultVariableComponentInfo from '../constant/var-rule-component'
let operators = [];
operators.push(...defaultVariableComponentInfo.operators.arithmeticOperator);
operators.push(...defaultVariableComponentInfo.operators.compareOperator);
operators.push(...defaultVariableComponentInfo.operators.specialCompare);
const operatorsDic = operators.map(oper=> {return {title:oper.name,value:oper.key}})
export  default[
    {
        key: 'varType',
        title: '组件类型',
        type:'singleSelect',
        dicKey: '',
        dicData: [{title:'数据字典',value:'dic'},{title:'自定义字典',value:'custDic'},{title:'日期',value:'date'},{title:'文本',value:'text'},{title:'数字',value:'number'},{title:'级联选择',value:'cascader'},{title:'自定义组件',value:'component'}],
        format:'',
        require:false,
        rules: [],
        readOnly: false,
        placeholder:'组件类型',
        resizable:false,
        align: 'center',
        columnShow:true
    },

    {
        key: 'varOperatorScope',
        title: '操作符类型',
        type:'singleSelect',
        dicKey: '',
        dicData: [{title:'所有',value:'all'},{title:'自定义',value:'cust'}],
        format:'',
        require:false,
        rules: [],
        readOnly: false,
        placeholder:'可用操作符',
        scale:0,
        minWidth: 150,
        maxWidth: 400,
        resizable:false,
        align: 'center',
        columnShow:true
    },
    {
        key: 'name',
        title: '变量名',
        type:'text',
        dicKey: '',
        dicData: [],
        format:'',
        require:false,
        rules: [{ pattern: /^\w+$/, message: '只能输入字母、数字、下划线', trigger: 'blur' },{ pattern: /^[A-Za-z]\w{0,}$/, message: '不能已非字母开头', trigger: 'blur' }],
        readOnly: false,
        placeholder:'变量名',
        minWidth: 80,
        maxWidth: 100,
        resizable:false,
        align: 'center',
        columnShow:true
    },
    {
        key: 'displayName',
        title: '显示名称',
        type:'text',
        dicKey: '',
        dicData: [],
        format:'',
        require:false,
        rules: [],
        minWidth: 100,
        maxWidth: 100,
        readOnly: false,
        placeholder:'显示名称',
        resizable:false,
        align: 'center',
        columnShow:true
    },
    {
        key: 'bizValueExpress',
        title: '业务取值表达式',
        type:'text',
        dicKey: '',
        dicData: [],
        format:'',
        require:false,
        rules: [{ pattern: /^[a-zA-Z][a-zA-Z0-9_\.]*$/, message: '不能已非字母开头', trigger: 'blur' }],
        readOnly: false,
        placeholder:'业务取值表达式',
        resizable:false,
        align: 'center',
        columnShow:true
    },
    {
        key: 'varComponentConfig',
        title: '组件配置',
        type:'text',
        dicKey: '',
        dicData: [],
        format:'',
        require:false,
        rules: [],
        readOnly: false,
        placeholder:'组件配置',
        resizable:false,
        align: 'center',
        columnShow:false
    },
    {
        key: 'operatorKeyList',
        title: '可用操作符',
        type:'multipleSelect',
        dicKey: '',
        dicData: operatorsDic,
        format:'',
        require:false,
        rules: [],
        readOnly: false,
        placeholder:'可用操作符',
        scale:0,
        resizable:false,
        align: 'center',
        columnShow:true
    },
    {
        key: 'action',
        title: '操作',
        width: 200,
        align: 'center',
        columnShow:true
    }
]
