<template>
    <rui-page :defines="defines">
        <Row>
            <rui-query :label-width="120"
                       :query="queryVariableData"
                       :query-rows="queryRows"
                       ref="variableQuery"
            />
        </Row>
        <rui-table :defineId="'variableConfig'"
                   :fixedLeft="['name','displayName']"
                   :fixedRight="['action']"
                   :showIndex="false"
                   :select="false"
                   :slots="[{key:'action',slot:'action'}]"
                   @loadDatas="queryVariableData"
                   ref="variableTable"
                   ref-query="variableQuery">
            <template slot="toolBar">
                <Button @click="createVariable" icon="ios-add" type="primary">新增</Button>
            </template>
            <template slot="action" slot-scope="{ row }">
                <div>
                    <Button @click="edit(row)" size="small" type="primary" >编辑</Button>
                    <Button @click="delVariable(row)" size="small" type="error" >删除</Button>
                    <Button @click="variableComponentConfig(row)" size="small" type="warning" >组件配置
                    </Button>
                </div>
            </template>
        </rui-table>
        <Modal
            :closable="false"
            :mask-closable="false"
            :title="varConfigTitle"
            :width="700"
            class-name="vertical-center-modal"
            v-model="variableConfig">
            <rui-form :afterChange="editFromChange"
                      :form-options="variableConfigFormOptions"
                      :label-width="120"
                      :read-only="false"
                      ref="editForm">
                <template slot="operatorKey" slot-scope="{ key,item,data,dataDic }">
                    <CheckboxGroup style="width:480px" v-model="data[key]">
                        <Checkbox :label="item.key" :key="item.key" v-for="item in operatorList">
                            <span>{{item.name}}</span>
                        </Checkbox>
                    </CheckboxGroup>
                </template>
            </rui-form>
            <div slot="footer">
                <Button @click="variableConfig = false" size="small">取消</Button>
                <Button @click="checkVariableConfig" size="small" type="primary">保存</Button>
            </div>
        </Modal>
        <Modal
            :closable="false"
            :mask-closable="false"
            :title="componentConfigTitle"
            class-name="vertical-center-modal"
            v-model="componentConfig">
            <Form :label-width="100" :model="variableComponentInfo.componentInfo" :rules="validateRules"
                  ref="componentConfigForm">
                <FormItem label="字典编号" prop="dicKey" v-if="variableComponentInfo.variableInfo.varType=='dic'">
                    <Input placeholder="输入字典编号" type="text" v-model="variableComponentInfo.componentInfo.dicKey">
                    </Input>
                </FormItem>

                <FormItem label="请求方法" prop="method" v-if="variableComponentInfo.variableInfo.varType=='custDic'">
                    <Select v-model="variableComponentInfo.componentInfo.method">
                        <Option value="get">GET</Option>
                        <Option value="post">POST</Option>
                    </Select>
                </FormItem>

                <FormItem label="请求路径" prop="dicUrl" v-if="variableComponentInfo.variableInfo.varType=='custDic'">
                    <Input placeholder="输入请求路径" type="text" v-model="variableComponentInfo.componentInfo.dicUrl">
                    </Input>
                </FormItem>

                <FormItem label="请求参数" prop="params" v-if="variableComponentInfo.variableInfo.varType=='custDic'">
                    <Input placeholder="输入请求参数" type="textarea" v-model="variableComponentInfo.componentInfo.params">
                    </Input>
                </FormItem>

                <FormItem label="日期格式" prop="format" v-if="variableComponentInfo.variableInfo.varType=='date'">
                    <Select v-model="variableComponentInfo.componentInfo.format">
                        <Option value="yyyy-MM-dd">yyyy-MM-dd</Option>
                        <Option value="yyyyMMdd">yyyyMMdd</Option>
                        <Option value="yyyy/MM/dd">yyyy/MM/dd</Option>
                    </Select>
                </FormItem>

                <FormItem label="日期范围起" prop="minDate" v-if="variableComponentInfo.variableInfo.varType=='date'">
                    <Input placeholder="" type="text" v-model="variableComponentInfo.componentInfo.minDate">
                    </Input>
                </FormItem>

                <FormItem label="日期范围止" prop="maxDate" v-if="variableComponentInfo.variableInfo.varType=='date'">
                    <Input placeholder="" type="text" v-model="variableComponentInfo.componentInfo.maxDate">
                    </Input>
                </FormItem>

                <FormItem label="最小长度" prop="minLength" v-if="variableComponentInfo.variableInfo.varType=='text'">
                    <InputNumber :max="100" :min="1" v-model="variableComponentInfo.componentInfo.minLength"></InputNumber>
                </FormItem>

                <FormItem label="最大长度" prop="maxLength" v-if="variableComponentInfo.variableInfo.varType=='text'">
                    <InputNumber :max="100" :min="1" v-model="variableComponentInfo.componentInfo.maxLength"></InputNumber>
                </FormItem>

                <FormItem label="校验表达式" prop="reg" v-if="variableComponentInfo.variableInfo.varType=='text'">
                    <Input placeholder="正则校验表达式" type="textarea" v-model="variableComponentInfo.componentInfo.reg">
                    </Input>
                </FormItem>

                <FormItem label="失败提示" prop="regError" v-if="variableComponentInfo.variableInfo.varType=='text'">
                    <Input placeholder="正则校验失败提示" type="text" v-model="variableComponentInfo.componentInfo.regError">
                    </Input>
                </FormItem>

                <FormItem label="数字类型" prop="type" v-if="variableComponentInfo.variableInfo.varType=='number'">
                    <Select v-model="variableComponentInfo.componentInfo.type">
                        <Option value="number">普通数字</Option>
                        <Option value="currency">金额</Option>
                        <Option value="percentage">百分比</Option>
                    </Select>
                </FormItem>

                <FormItem label="精度" prop="scale" v-if="variableComponentInfo.variableInfo.varType=='number'">
                    <InputNumber :max="10" :min="0" v-model="variableComponentInfo.componentInfo.scale"></InputNumber>
                </FormItem>

                <FormItem label="最小值" prop="min" v-if="variableComponentInfo.variableInfo.varType=='number'">
                    <InputNumber :max="99999999999" :min="-9999"
                                 v-model="variableComponentInfo.componentInfo.min"></InputNumber>
                </FormItem>

                <FormItem label="最大值" prop="max" v-if="variableComponentInfo.variableInfo.varType=='number'">
                    <InputNumber :max="99999999999" :min="-9999"
                                 v-model="variableComponentInfo.componentInfo.max"></InputNumber>
                </FormItem>

                <FormItem label="级联层级" prop="level" v-if="variableComponentInfo.variableInfo.varType=='cascader'">
                    <InputNumber :max="5" :min="1" :precision="0" :step="1" @on-change="processCasInfo"
                                 v-model="variableComponentInfo.componentInfo.level"></InputNumber>
                </FormItem>
                <Table :columns="cascaderTableColumns" :data="variableComponentInfo.componentInfo.levelConfig" border
                       v-if="variableComponentInfo.variableInfo.varType=='cascader'">
                    <template slot="level_action" slot-scope="{ row ,index}">
                        <div>
                            <Button @click="editCascaderLevel(row,index)" size="small">编辑
                            </Button>
                        </div>
                    </template>
                    <template slot="level" slot-scope="{ row,index }">
                        <span>第{{index+1}}级</span>
                    </template>
                </Table>
                <br>
                <FormItem label="是否为全路径" prop="ifFull" v-if="variableComponentInfo.variableInfo.varType=='component'">
                    <Select v-model="variableComponentInfo.componentInfo.isFull">
                        <Option value="1">是</Option>
                        <Option value="0">不是</Option>
                    </Select>
                </FormItem>
                <FormItem label="组件参数" prop="params" v-if="variableComponentInfo.variableInfo.varType=='component'">
                    <Input placeholder="输入组件参数" type="textarea" v-model="variableComponentInfo.componentInfo.params">
                    </Input>
                </FormItem>
                <FormItem label="组件路径" prop="componentPath" v-if="variableComponentInfo.variableInfo.varType=='component'">
                    <Input placeholder="输入组件路径" type="text" v-model="variableComponentInfo.componentInfo.componentPath">
                    </Input>
                </FormItem>
                <FormItem label="提醒信息" prop="placeholder" v-if="componentConfig">
                    <Input placeholder="输入提醒信息" type="text" v-model="variableComponentInfo.componentInfo.placeholder">
                    </Input>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="componentConfig = false" size="small">取消</Button>
                <Button @click="checkVariableComponentConfig" size="small" type="primary">确定</Button>
            </div>
        </Modal>
        <Modal :closable="false"
               :mask-closable="false"
               :title="cascaderLevelTitle"
               class-name="vertical-center-modal"
               v-model="cascaderLevel">
            <Form :label-width="100" :model="cascaderLevelEdit" :rules="cascaderLevelValidateRule"
                  ref="cascaderLevelConfigForm">
                <FormItem label="请求方法" prop="method">
                    <Select placeholder="请求方法" v-model="cascaderLevelEdit.method">
                        <Option value="get">GET</Option>
                        <Option value="post">POST</Option>
                    </Select>
                </FormItem>
                <FormItem label="请求路径" prop="url">
                    <Input placeholder="请求路径" type="text" v-model="cascaderLevelEdit.url">
                    </Input>
                </FormItem>
                <FormItem label="父级参数名" prop="superKey" v-if="cascaderLevelEdit._index>0">
                    <Input placeholder="父级参数名" type="text" v-model="cascaderLevelEdit.superKey">
                    </Input>
                </FormItem>

                <FormItem label="附加参数" prop="params" >
                    <Input placeholder="附加参数" type="textarea" v-model="cascaderLevelEdit.params">
                    </Input>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cascaderEditLevelCancel" size="small">取消</Button>
                <Button @click="cascaderEditLevelOk" size="small" type="primary">确定</Button>
            </div>
        </Modal>
    </rui-page>
</template>

<script>
    import variableDefineInfo from "./define/flow-variable-define";
    import random from '../../../../../global/utils/random';
    import { deleteAtom} from "_p/basic/api/rule/ruleAtom"
    import {deepClone} from "@/libs/utils/ObjectClone";
    import defaultVariableComponentInfo from './constant/var-rule-component'

    export default {
        name: "variable-config",
        props: {
            variables: {
                type: Array,
                required: true
            }
        },
        data() {
            return {
                defines:[],
                variableConfig: false,
                componentConfig: false,
                cascaderLevel: false,
                modelType: '',
                cascaderEditLevel:-1,
                varConfigTitle: '变量编辑',
                componentConfigTitle: '变量组件配置',
                cascaderLevelTitle: '级联层级维护',
                validateRules: {},
                requestPath:'',
                cascaderLevelEdit: {
                    url:'',
                    method:'',
                    params:'',
                    superKey:''
                },
                variableComponentInfo: {
                    variableInfo: {},
                    componentInfo: null
                },
                queryRows: [
                    {
                        defineId: 'variableConfig',
                        fields: ['name','displayName']
                    }
                ],
                variableConfigFormOptions: [
                    {
                        isGroup: false,
                        grids: [
                            {defineId: 'variableConfig', span: 12, fields: ['name', 'bizValueExpress','varOperatorScope']},
                            {defineId: 'variableConfig', span: 12, fields: ['displayName', 'varType']},
                        ]
                    },
                    {
                        isGroup: false,
                        hideKey: ['operatorKeyList'],
                        grids: [
                            {
                                defineId: 'variableConfig',
                                span: 24,
                                fields: ['operatorKeyList'],
                                slots: [{key: 'operatorKeyList', slotName: 'operatorKey'}]
                            },
                        ]
                    }
                ]
            };
        },
        methods: {
            queryVariableData(queryData) {
                console.log(queryData)
                console.log(this.variables);
                if(queryData.condition.name===''&&queryData.condition.displayName===''){
                    this.$refs.variableTable.updateTableData(this.variables, this.variables.length);
                }
            },
            createVariable() {
                this.varConfigTitle = '新建变量';
                this.$refs.editForm.setAllFieldRequired(true);
                this.modelType = 'new';
                let self = this;
                this.$nextTick(() => {
                    self.$refs.editForm.resetFrom();
                    this.variableConfig = true;
                })
            },
            delVariable(row) {
                let self = this;
                this.$Modal.confirm({
                    title: "确认",
                    content: `确认删除该变量[${row.displayName}]？`,
                    onOk: () => {
                        let deleteIndex = -1;
                        for (let index = 0; index < self.variables.length; index++) {
                            if(self.variables[index]._varId===row._varId){
                                deleteIndex = index;
                                break
                            }
                        }
                        if(deleteIndex > -1){
                            self.variables.splice(deleteIndex,1);
                        }
                        self.$refs['variableTable'].reloadData();
                    }
                });
            },
            variableComponentConfig(row) {
                this.componentConfigTitle = '配置' + row.displayName + "(" + row.name + ") 组件类型";
                this.variableComponentInfo.variableInfo = deepClone(row);
                this.validateRules = defaultVariableComponentInfo.getValidateRules(row.varType);
                console.log(row.varType,"===========")
                this.variableComponentInfo.componentInfo = defaultVariableComponentInfo.genComponentConfig(row.varType, JSON.parse(row.varComponentConfig));
                this.componentConfig = true;
            },
            edit(row) {
                this.varConfigTitle = '配置' + row.displayName + "(" + row.name + ")";
                this.modelType = 'modify';
                this.$refs.editForm.updateFormData(deepClone(row));
                this.$refs.editForm.setAllFieldRequired(true);
                this.variableConfig = true;
            },
            checkVariableConfig() {
                let self = this;
                this.$refs.editForm.getForm().validate((valid) => {
                    if (valid) {
                        let reqData = self.$refs.editForm.getFormData();
                        if (self.modelType === 'new') {
                            reqData.varComponentConfig = '{}';
                            reqData._varId = Date.now()+'_'+random(10);
                            this.variables.push(reqData);
                            self.$refs['variableTable'].reloadData();
                            self.variableConfig = false;
                        } else if (self.modelType === 'modify') {
                            for (let index = 0; index < this.variables.length; index++) {
                                if(this.variables[index]._varId===reqData._varId){
                                    Object.assign(this.variables[index],reqData);
                                    break
                                }
                            }
                            self.variableConfig = false;
                            console.log(reqData)
                        }
                    } else {
                        self.variableConfig = true;
                        self.$Message.error('数据不完整!');
                    }
                })
            },
            checkVariableComponentConfig() {
                let self = this;
                this.$refs.componentConfigForm.validate((valid) => {
                    if (valid) {
                        self.variableComponentInfo.variableInfo.varComponentConfig = JSON.stringify(self.variableComponentInfo.componentInfo);
                        console.log(self.variableComponentInfo.variableInfo )
                        for (let index = 0; index < this.variables.length; index++) {
                            if(this.variables[index]._varId===self.variableComponentInfo.variableInfo._varId){
                                Object.assign(this.variables[index],self.variableComponentInfo.variableInfo);
                                break
                            }
                        }
                        self.componentConfig = false;
                    } else {
                        self.componentConfig = true;
                        self.$Message.error('数据不完整!');
                    }
                })
            },
            editFromChange(key, afterValue) {
                if (key === 'varOperatorScope' && afterValue === 'cust') {
                    this.$refs.editForm.setFiledHide('operatorKeyList', false);
                } else if (key === 'varOperatorScope') {
                    this.$refs.editForm.setFiledHide('operatorKeyList', true);
                    this.$refs.editForm.setFiledValue('operatorKeyList', []);
                }
            },
            editCascaderLevel(row, index) {
                this.cascaderLevelTitle = '配置第' + (index + 1) + "级参数";
                this.cascaderLevelEdit = deepClone(row);
                this.cascaderEditLevel = index;
                this.cascaderLevel = true;
            },
            cascaderEditLevelCancel(){
                this.cascaderLevel = false;
                this.cascaderEditLevel = -1;
                this.cascaderLevelEdit.url = '';
                this.cascaderLevelEdit.method = '';
                this.cascaderLevelEdit.params = '';
                this.cascaderLevelEdit.superKey = '';
                this.$refs['cascaderLevelConfigForm'].resetFields();
            },
            cascaderEditLevelOk(){
                let self = this;
                this.$refs['cascaderLevelConfigForm'].validate((valid) => {
                    if (valid) {
                        self.variableComponentInfo.componentInfo.levelConfig[self.cascaderEditLevel].url = self.cascaderLevelEdit.url;
                        self.variableComponentInfo.componentInfo.levelConfig[self.cascaderEditLevel].method = self.cascaderLevelEdit.method;
                        self.variableComponentInfo.componentInfo.levelConfig[self.cascaderEditLevel].params = self.cascaderLevelEdit.params;
                        self.cascaderLevelEdit.url = '';
                        self.cascaderLevelEdit.method = '';
                        self.cascaderLevelEdit.params = '';
                        if(self.cascaderEditLevel>0){
                            self.variableComponentInfo.componentInfo.levelConfig[self.cascaderEditLevel].superKey=self.cascaderLevelEdit.superKey;
                            self.cascaderLevelEdit.superKey = '';
                        }
                        self.cascaderLevel = false;
                        this.$refs['cascaderLevelConfigForm'].resetFields();
                    } else {
                        self.cascaderLevel = true;
                        self.$Message.error('数据不完整!');
                    }
                })
            },
            processCasInfo(number) {
                if(number>this.variableComponentInfo.componentInfo.levelConfig.length){
                    while (number>this.variableComponentInfo.componentInfo.levelConfig.length){
                        this.variableComponentInfo.componentInfo.levelConfig.push({
                            url:'',
                            params:'',
                            method:'',
                            superKey:''
                        })
                    }
                }else{
                    while(this.variableComponentInfo.componentInfo.levelConfig.length>number){
                        this.variableComponentInfo.componentInfo.levelConfig.splice(-1,1);
                    }
                }
            }
        },
        computed: {
            cascaderTableColumns() {
                return defaultVariableComponentInfo.cascaderTable
            },
            cascaderLevelValidateRule(){
                return defaultVariableComponentInfo.cascaderLevelRule
            },
            operatorList() {
                let operators = [];
                operators.push(...defaultVariableComponentInfo.operators.arithmeticOperator);
                operators.push(...defaultVariableComponentInfo.operators.compareOperator);
                operators.push(...defaultVariableComponentInfo.operators.specialCompare);
                return operators;
            }
        },
        created(){
            this.defines.push({
                id: 'variableConfig',
                fields: variableDefineInfo
            })
        },
        mounted() {
            this.$refs['variableTable'].reloadData();
        }
    };
</script>
