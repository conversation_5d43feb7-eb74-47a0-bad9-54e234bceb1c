import {deepClone} from "@/libs/utils/ObjectClone";

const dateCheck = function (date) {
    let result = date.match(/^(\d{1,4})(-|\/)(\d{1,2})\2(\d{1,2})$/);
    if (result == null)
        return false;
    let d = new Date(result[1], result[3] - 1, result[4]);
    return (d.getFullYear() == result[1] && (d.getMonth() + 1) == result[3] && d.getDate() == result[4]);
}

const defaultConfigs = {
    dic: {
        placeholder: '',
        dicKey: ''
    },
    custDic: {
        placeholder: '',
        dicUrl: '',
        params: '',
        method: ''
    },
    date: {
        placeholder: '',
        minDate: '',
        maxDate: '',
        format: 'yyyy-MM-dd'
    },
    text: {
        placeholder: '',
        minLength: 1,
        maxLength: 50,
        reg: '',
        regError: ''
    },
    number: {
        placeholder: '',
        scale: 2,
        type: '',
        min: 0,
        max: 0
    },
    cascader: {
        placeholder: '',
        level: 1,
        levelConfig: [{
            url: '',
            params: '',
            method: ''
        }]
    },
    component: {
        isFull: '0',
        params: '',
        componentPath: ''
    }
}

const validateRules = {
    dic: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        dicKey: [
            {required: true, message: '请输入字典编号', trigger: 'blur'},
        ]
    },
    custDic: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        dicUrl: [
            {required: true, message: '字典请求路径不能为空', trigger: 'blur'},
        ],
        params: [
            {
                validator: (rule, value, callback) => {
                    if (value !== '') {
                        if (typeof value == 'string') {
                            let obj = JSON.parse(value);
                            if (typeof obj == 'object' && obj) {
                                callback();
                            } else {
                                callback(new Error('附加参数格式不正确(JSON格式数据)'));
                            }
                        } else {
                            callback(new Error('附加参数格式不正确(JSON格式数据)'));
                        }
                    }
                    callback();
                },
                trigger: 'blur'
            },
        ],
        method: [
            {required: true, message: '请求类型不能为空', trigger: 'change'},
        ]
    },
    date: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        minDate: [
            {
                validator: (rule, value, callback) => {
                    try {
                        if (value != '') {
                            if (!dateCheck(value)) {
                                callback(new Error('日期输入不正确'));
                            }
                        }
                        callback();
                    } catch (e) {
                        callback(new Error('日期输入不正确'));
                    }
                },
                trigger: 'blur'
            }
        ],
        maxDate: [
            {
                validator: (rule, value, callback) => {
                    try {
                        if (value != '') {
                            if (!dateCheck(value)) {
                                callback(new Error('日期输入不正确'));
                            }
                        }
                        callback();
                    } catch (e) {
                        callback(new Error('日期输入不正确'));
                    }
                },
                trigger: 'blur'
            },
        ],
        format: [
            {required: true, message: '日期格式不能为空', trigger: 'change'},
        ]
    },
    text: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        minLength: [
            {required: true, type: 'number', message: '请输入最小长度', trigger: 'blur'}
        ],
        maxLength: [
            {required: true, type: 'number', message: '请输入最大长度', trigger: 'blur'}
        ]
    },
    number: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        scale: [
            {required: true, type: 'number', message: '精度不能为空', trigger: 'blur'}
        ],
        type: [
            {required: true, message: '数字类型不能为空', trigger: 'blur'}
        ],
        min: [
            {required: true, type: 'number', message: '最小值不能为空', trigger: 'blur'}
        ],
        max: [
            {required: true, type: 'number', message: '最大值不能为空', trigger: 'blur'}
        ]
    },
    cascader: {
        placeholder: [
            {required: true, message: '请输入提醒信息', trigger: 'blur'}
        ],
        level: [
            {required: true, type: 'number', message: '级联层级不能为空', trigger: 'blur'}
        ]
    },
    component: {
        componentPath: [
            {required: true, message: '组件路径不能为空', trigger: 'blur'}
        ],
        params: [
            {
                validator: (rule, value, callback) => {
                    if (value !== '') {
                        if (typeof value == 'string') {
                            let obj = JSON.parse(value);
                            if (typeof obj == 'object' && obj) {
                                callback();
                            } else {
                                callback(new Error('附加参数格式不正确(JSON格式数据)'));
                            }
                        } else {
                            callback(new Error('附加参数格式不正确(JSON格式数据)'));
                        }
                    }
                    callback();
                },
                trigger: 'blur'
            },
        ],
    }
}

const cascaderLevelRule = {
    url: [
        {required: true, message: '请求路径不能为空', trigger: 'blur'},
    ],
    params: [
        {
            validator: (rule, value, callback) => {
                try {
                    if (value != '') {
                        if (typeof value == 'string') {
                            let obj = JSON.parse(value);
                            if (typeof obj == 'object' && obj) {
                                callback();
                            } else {
                                callback(new Error('附加参数格式不正确(JSON格式数据)'));
                            }
                        } else {
                            callback(new Error('附加参数格式不正确(JSON格式数据)'));
                        }
                    }
                    callback();
                } catch (e) {
                    callback(new Error('附加参数格式不正确(JSON格式数据)'));
                }
            },
            trigger: 'blur'
        },
    ],
    method: [
        {required: true, message: '请求类型不能为空', trigger: 'change'},
    ]
}
const cascaderTable = [
    {
        title: "级别",
        key: "action",
        align: "center",
        width: 100,
        slot: 'level',
        fixed: 'left'
    },
    {
        title: '请求路径',
        key: 'url',
        align: "center",
        width: 120,
        tooltip: true
    },
    {
        title: '请求方法',
        key: 'method',
        align: "center",
        width: 120,
        tooltip: true
    },
    {
        title: '附加参数',
        key: 'params',
        align: "center",
        width: 120,
        tooltip: true
    },
    {
        title: '父级参数名',
        key: 'superKey',
        align: "center",
        width: 120,
        tooltip: true
    },
    {
        title: "操作",
        key: "action",
        align: "center",
        width: 100,
        fixed: 'right',
        slot: 'level_action',
    }
]

const operators = {
    //比较操作符
    specialCompare: [
        {
            key: 'in',
            name: "in",
            value: 'in'
        },
        {
            key: 'contain',
            name: '包含',
            value: 'contain'
        }
    ],
    compareOperator: [
        {
            key: 'lt',
            name: "小于",
            value: '<'
        },
        {
            key: 'gt',
            name: "大于",
            value: '>'
        },
        {
            key: 'let',
            name: "小于等于",
            value: '<='
        },
        {
            key: 'get',
            name: "大于等于",
            value: '>='
        },
        {
            key: 'eq',
            name: "等于",
            value: '=='
        },
        {
            key: 'neq',
            name: "不等于",
            value: '!='
        },
        {
            key: 'like',
            name: "like",
            value: 'like'
        },
        {
            key: 'sw',
            name: "左匹配",
            value: 'startWith'
        },
        {
            key: 'ew',
            name: "右匹配",
            value: 'endWith'
        }
    ],
    // 逻辑操作符
    logicOperator: [
        {
            key: 'and',
            name: "并且",
            value: '&&'
        },
        {
            key: 'or',
            name: "或",
            value: '||'
        },
        {
            key: 'not',
            name: "非",
            value: '!'
        }
    ],
    // 算数操作符
    arithmeticOperator: [
        {
            key: 'minus',
            name: "减",
            value: '-'
        },
        {
            key: 'plus',
            name: "加",
            value: '+'
        },
        {
            key: 'times',
            name: "乘",
            value: '*'
        },
        {
            key: 'divided',
            name: "除",
            value: '/'
        }
    ]
}

function genComponentConfig(type, config) {
    const copy = deepClone(defaultConfigs[type]);
    Object.keys(copy).forEach(key => {
        if (!config[key]) {
            config[key] = copy[key];
        }
    })
    return config;
}

function getValidateRules(type) {
    return validateRules[type];
}

export default {
    genComponentConfig,
    getValidateRules,
    cascaderTable,
    cascaderLevelRule,
    operators
}
