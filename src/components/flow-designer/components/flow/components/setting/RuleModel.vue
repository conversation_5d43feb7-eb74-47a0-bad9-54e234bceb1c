<template>
    <Modal v-model="config.show" :closable="false" :footerHide="footHide" :maskClosable="maskCloseable"
           :width="750" :zIndex="zIndex" class-name="vertical-center-modal" @on-visible-change="initData">
        <div slot="header" class="ivu-modal-header-inner">
            <span>{{ datas.ruleName }}</span>
            <a class="ivu-modal-close" @click="resetValue"><i class="ivu-icon ivu-icon-ios-close"></i></a>
        </div>
        <Form ref="choiceForm" :model="formData" :rules="ruleValidate">
            <Row>
                <Col span="9">
                    <FormItem :label-width="100" prop="ruleName"  label="规则名称:">
                        <Input style="width:150px" v-model="formData.ruleName" placeholder="规则名称"></Input>
                    </FormItem>
                </Col>
                <Col span="6">
                    <FormItem :label-width="80"   prop="priority"  label="优先级:">
                        <InputNumber v-model="formData.priority" :min="0" :max="100"/>
                    </FormItem>
                </Col>
                <Col span="9">
                    <FormItem :label-width="100"   prop="resultType"  label="分配类型:">
                        <Select style="width: 150px" v-model="formData.resultType" @on-change="clearAssignResult" placeholder="类型">
                            <Option value="0">人员</Option>
                            <Option value="1">工作组</Option>
                        </Select>
                    </FormItem>
                </Col>
            </Row>
            <Row>
                <FormItem :label-width="92" label="可选参与者"  prop="showResult" >
                    <Input v-model="formData.showResult" placeholder=""
                           readonly style="width: 550px" type="text" @on-focus="showOrgChoiceHandler"/>
                    &nbsp;&nbsp;
                    <Button  type="primary" @click="clearAssignResult">清空</Button>
                </FormItem>
                <OrgChoiceModule :request-methods="requestMethods" :flow-config="flowConfig" :config="choiceConfig" :datas="formData" :showOrgChoice="true"/>
            </Row>
        </Form>
        <UserRule ref="userRole" v-if="showRule" rule-name="规则维护" :rule-data="ruleData" :variables="variables" :request-methods="requestMethods"></UserRule>
        <div slot="footer">
            <Button size="small" @click="resetValue">取消</Button>
            <Button size="small" type="primary" @click="checkAndClose">确定</Button>
        </div>
    </Modal>
</template>

<script>
import UserRule from './flow-rule/flow-rule';
import OrgChoiceModule from './OrgChoiceModule'
export default {
    name: 'assignRuleModal',
    components: {
        OrgChoiceModule,
        UserRule
    },
    props: {
        config: {
            type: Object,
            required: true
        },
        ruleEditType: {
            type: String,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        flowConfig:{
            type:Object
        },
        variables:{
            type:Array,
            default:()=>{
                return []
            }
        },
        requestMethods:{
            type:Object,
            default:()=>{
                return {}
            }
        },
        flowAssignInfo:{
            type:Object,
            required:true
        }
    },
    data() {
        return {
            zIndex: 1005,
            footHide: false,
            maskCloseable: false,
            errorMsg:'',
            ruleData:[],
            choiceConfig: {open: false, title: '',},
            ruleValidate: {
                ruleName: [
                    {required: true, message: '不能为空', trigger: 'blur'}
                ],
                resultType: [
                    {required: true, message: '不能为空', trigger: 'change'}
                ],
                priority: [
                    {required: true,type:'number',message: '不能为空', trigger: 'blur'}
                ],
            },
            formData:{
                ruleName:'',
                resultType:'0',
                result:'',
                showResult:'',
                ptkind:'',
                priority:0,
                choiceType: '',
                fromRule: true
            },
            showRule:false
        }
    },
    methods: {
        initData(show){
            if(show){
                this.formData.ruleName=this.datas.ruleName;
                this.formData.resultType=this.datas.resultType;
                this.formData.result=this.datas.result;
                this.formData.showResult=this.datas.showResult;
                this.formData.priority=this.datas.priority;
                this.formData.choiceType = this.flowAssignInfo.choiceType;

                this.ruleData.splice(0);
                if(this.datas.ruleData&&this.datas.ruleData!=='') {
                    this.ruleData.push(...JSON.parse(this.datas.ruleData));
                }
                this.$nextTick(()=>{
                    this.showRule = true;
                })
            }
        },
        showOrgChoiceHandler() {
            if (this.formData.resultType === undefined || this.formData.resultType === '') {
                this.$Message.error('请先选择参与者类型')
                return;
            }
            this.choiceConfig.open = true;
            if (this.formData.resultType === '0') {
                this.formData.ptkind = '1';
                this.choiceConfig.title = '选择人员';
            } else {
                this.formData.ptkind = '5';
                this.choiceConfig.title = '选择工作组';
            }
        },
        clearAssignResult() {
           this.formData.result = '';
           this.formData.showResult = '';
        },
        checkAndClose() {
            if(this.formData.showResult===''){
                this.$Message.error('选择参与者')
                return;
            }
            this.datas.ruleName=this.formData.ruleName;
            this.datas.resultType=this.formData.resultType;
            this.datas.result=this.formData.result;
            this.datas.showResult=this.formData.showResult;
            this.datas.priority=this.formData.priority;
            this.$refs.userRole.saveRule((result)=>{
                if(result.express) {
                    this.datas.ruleData = result.ruleData;
                    this.datas.express = result.express;
                    this.$emit('modifyData',this.ruleEditType,this.datas)
                    this.$nextTick(()=>{
                        this.config.show = false;
                        this.$nextTick(()=>{
                            this.showRule = false;
                        })
                    })
                }
            });
        },
        resetValue(){
            this.$refs.choiceForm.resetFields();
            this.$nextTick(()=>{
                this.config.show = false;
                this.$nextTick(()=>{
                    this.showRule = false;
                })
            })
        }
    }
}
</script>
