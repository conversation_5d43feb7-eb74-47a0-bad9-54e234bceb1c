<style lang="less" rel="stylesheet/less">
.footer {
    position: absolute;
    bottom: 13px;
    left: 120px;
}

.ivu-message {
    z-index: 9999 !important;
}

.ivu-card-extra {
    top: 20px !important;
}
</style>

<template>
    <Tabs v-model="currectTab" type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                基础属性
                            </span>
                            <FormItem :label-width="50" >
                                <slot name="label">
                                    <span>节点id:&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                </slot>
                                <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 180px"
                                       type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="45" >
                                <slot name="label">
                                    <span>节点名称:&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                </slot>
                                <Input v-model="datas.base.name" placeholder="节点名称" style="width: 180px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="20">
                                <slot name="label">
                                    <tooltip content="自定义索引,回调时同步回传,可用于业务端对任务归类处理" max-width="150" transfer>
                                        <Icon type="ios-alert" size="15" color="#ff8c49" />
                                        <span>节点索引:&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    </tooltip>
                                </slot>
                                <Input v-model="datas.base.userDefinedIndex"  placeholder="节点索引"  style="width: 180px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="20">
                                <slot name="label">
                                    <tooltip content="数值越大,优先级越高,在查询领取任务时作为默认排序条件" max-width="150" transfer>
                                        <Icon type="ios-alert" size="15" color="#ff8c49"/>
                                        <span>任务优先级:&nbsp;&nbsp;&nbsp;&nbsp;</span>
                                    </tooltip>
                                </slot>
                                <InputNumber v-model="datas.base.priority" placeholder="任务优先级" style="width: 180px"/>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                跳转权限
                            </span>
                            <FormItem :label-width="200" label="允许跳转到其他节点:">
                                <i-switch :falseValue="'0'" :trueValue="'1'"
                                          :value="datas.base.skipauth.charAt(0)" size="large" @on-change="updateShipAuthSource">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>

                            <FormItem :label-width="200" label="允许其他节点跳转到本节点:">
                                <i-switch :falseValue="'0'" :trueValue="'1'"
                                          :value="datas.base.skipauth.charAt(1)" size="large" @on-change="updateShipAuthTarget">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                其他设置
                            </span>
                            <FormItem :label-width="80" label="自动提交:">
                                <Select v-model="datas.base.autoCommitOnNextNodeSameUser" style="width: 200px">
                                    <Option value="0">关闭</Option>
                                    <Option value="2">上一节点审批人相同</Option>
                                    <Option value="1">下一节点审批人相同</Option>
                                </Select>
                            </FormItem>
                            <FormItem :label-width="80" label="页面URL:">
                                <Input v-model="datas.base.url" placeholder="页面URL" style="width: 500px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80"   label="回调URI:">
                                <Input v-model="datas.base.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 500px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="回调参数:">
                                <Input v-model="datas.base.commonParam" placeholder="回调参数" style="width: 500px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="节点描述:">
                                <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px"
                                       type="textarea">
                                </Input>
                            </FormItem>
                        </div>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="参与者" name="assign">
            <div style="background:#ececec;">
                <Card :border="false">
                    <Form inline>
<!--                        <div class="properties_contain">-->
<!--                            <span class="title">-->
<!--                                基于流程的分配-->
<!--                            </span>-->
<!--                            <FormItem :label-width="20" label="">-->
<!--                                <RadioGroup v-model="datas.assigners.pthistory" @on-change="checkPtkindValue">-->
<!--                                    <Row>-->
<!--                                        <Radio label="0" v-show="flowConfig.ptHistoryType.includes('0')">-->
<!--                                            <span>与流程启动者无关</span>-->
<!--                                        </Radio>-->
<!--                                        <Radio label="1" v-show="flowConfig.ptHistoryType.includes('1')">-->
<!--                                            <span>分配到流程启动者</span>-->
<!--                                        </Radio>-->
<!--                                        <Radio label="4" v-show="flowConfig.ptHistoryType.includes('2')">-->
<!--                                            <span>分配到流程启动者机构</span>-->
<!--                                        </Radio>-->
<!--                                        <Radio label="3" v-show="flowConfig.ptHistoryType.includes('3')">-->
<!--                                            <span>分配到流程启动者岗位</span>-->
<!--                                        </Radio>-->
<!--                                    </Row>-->
<!--                                </RadioGroup>-->
<!--                            </FormItem>-->
<!--                        </div>-->
                        <div class="properties_contain">
                            <span class="title">
                                分配模式
                            </span>
                            <FormItem :label-width="20" label="" v-show="processInfo.base.firsttaskcommit==='1'">
                                <RadioGroup v-model="datas.assigners.pthistory" @on-change="checkPtkindValue">
                                    <Row>
                                        <Radio label="0" v-show="flowConfig.ptHistoryType.includes('0')">
                                            <span>与流程启动者无关</span>
                                        </Radio>
                                        <Radio label="1" v-show="flowConfig.ptHistoryType.includes('1')">
                                            <span>分配到流程启动者</span>
                                        </Radio>
                                        <Radio label="4" v-show="flowConfig.ptHistoryType.includes('2')">
                                            <span>分配到流程启动者机构</span>
                                        </Radio>
                                        <Radio label="3" v-show="flowConfig.ptHistoryType.includes('3')">
                                            <span>分配到流程启动者岗位</span>
                                        </Radio>
                                    </Row>
                                </RadioGroup>
                            </FormItem>
                            <FormItem :label-width="20" label="" v-show="datas.assigners.pthistory==='0'">
                                <FormItem>
                                    <RadioGroup v-model="datas.assigners.assignmode" @on-change="fixChoiceType">
                                        <Radio :disabled="disableAssignmode()" label="1">
                                            <span>单一分配(不能加签)</span>
                                        </Radio>
                                        <Radio :disabled="disableAssignmode()" label="4">
                                            <span>单一分配(可加签)</span>
                                        </Radio>
                                        <Radio :disabled="disableAssignmode()" label="2">
                                            <span>抢占分配</span>
                                        </Radio>
                                        <Radio :disabled="disableAssignmode()" label="3">
                                            <span>多重分配</span>
                                        </Radio>
                                        <Radio :disabled="disableAssignmode()" label="5">
                                            <span>会签模式</span>
                                        </Radio>
                                    </RadioGroup>
                                </FormItem>
                                <FormItem>
                                    <div v-if="datas.assigners.assignmode==='3'||datas.assigners.assignmode==='5'">
                                        <span style="line-height: 26px">&nbsp;完成条件 &nbsp;</span>
                                        <Select v-model="datas.assigners.exittype" size="small" style="width:120px">
                                            <Option :value="'1'">按个数</Option>
                                            <Option :value="'2'">按百分比</Option>
                                        </Select>
                                        &nbsp;
                                        <InputNumber v-model="datas.assigners.exitcount" style="width: 80px"/>
                                        <span v-if="datas.assigners.exittype===2">%</span>
                                    </div>
                                </FormItem>
                            </FormItem>
                        </div>
                        <div class="properties_contain" v-show="(datas.assigners.assignmode==='1'||datas.assigners.assignmode==='4')&&datas.assigners.pthistory==='0'" >
                            <span class="title">
                                参与者优先级
                            </span>
                            <FormItem :label-width="20" label="">
                                <Alert >
                                    <p>1.参与者优先级只在单一分配下有效</p>
                                    <p>2.按节点待办最少分配-需校验是否在岗(同时可处理任务量受工作组任务量限制)</p>
                                    <p>3.按总任务量最少分配-不校验是否在岗(受总任务量限制)</p>
                                    <p>4.按当天节点最少分配-校验是否在岗任务量受用户组任务量限制(当日已处理量达到节点配置数量后不再分配)</p>
                                    <p>5.分配任务量不能超过用户总任务量</p>
                                </Alert>
                                <RadioGroup v-model="datas.assigners.choiceType" @on-change="choiceTypeCheck">
                                    <Radio  label="1" :disabled="disableAssignmode()">
                                        <span>按节点待办最少分配</span>
                                    </Radio>
                                    <Radio :disabled="(datas.assigners.assignmode!=='1'&&datas.assigners.assignmode!=='4')||disableAssignmode()" label="3">
                                        <span>按当天节点最少分配</span>
                                    </Radio>
                                    <Radio  label="2" :disabled="disableAssignmode()">
                                        <span>按总任务量最少分配</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                        <div class="properties_contain" v-show="datas.assigners.pthistory==='0'">
                            <span class="title">
                                指定参与者
                            </span>
                            <FormItem :label-width="180" label="新任务保持第一次分配结果">
                                <i-switch v-model="datas.assigners.keepFirstAssign" size="large">
                                    <span slot="open">是</span>
                                    <span slot="close">否</span>
                                </i-switch>
                            </FormItem>
                            <br>
                            <FormItem :label-width="140" label="候选人冲突解决方式">
                                <RadioGroup v-model="datas.assigners.conflictProcessor" style="text-align: center">
                                    <Radio v-for="(item) in flowConfig.conflictProcess" :label="item.value">
                                        <span>{{ item.label }}</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <br>
                            <FormItem :label-width="130" label="排除任务提交列表">
                                <Select
                                    v-model="excludes"
                                    multiple
                                    style="width: 400px"
                                    transfer
                                    @on-change="excludeHandler"
                                >
                                    <Option v-for="(option, index) in excludeTaskList" :value="option.value" :key="index">{{option.label}}</Option>
                                </Select>
                            </FormItem>
                            <br>
                            <FormItem :label-width="20" label="">
                                <RadioGroup v-model="datas.assigners.ptassign" style="text-align: center"
                                            @on-change="clearAssignResult">
                                    <Radio :disabled="disableAssiger()" label="1" v-show="flowConfig.assignType.includes('1')">
                                        <span>系统组织信息</span>
                                    </Radio>
                                    <Radio :disabled="disableAssiger()" label="2"  v-show="flowConfig.assignType.includes('2')">
                                        <span>流程变量</span>
                                    </Radio>
                                    <Radio :disabled="disableAssiger() || disableAdapter()" label="3"  v-show="flowConfig.assignType.includes('3')">
                                        <span>分派策略适配器</span>
                                    </Radio>
                                    <Radio  label="4"  :disabled="datas.assigners.assignmode==='3' || datas.assigners.assignmode==='5'" v-show="flowConfig.assignType.includes('4')">
                                        <span>根据规则分配</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <div v-if="this.datas.assigners.ptassign ==='1'">
                                <FormItem :label-width="92" label="参与者类型">
                                    <Select v-model="datas.assigners.ptkind" :disabled="disableAssiger()" size="small" transfer
                                            style="width:230px" @on-change="clearAssignResult">
                                        <Option :value="'1'" v-show="flowConfig.ptKindType.includes('1')">分配到人员</Option>
                                        <Option :value="'3'" v-show="flowConfig.ptKindType.includes('3')">分配到机构</Option>
                                        <Option :value="'5'" v-show="flowConfig.ptKindType.includes('5')">分配到群组</Option>
                                        <Option :value="'4'" v-show="flowConfig.ptKindType.includes('4')">分配到机构岗位</Option>
                                        <Option :value="'6'" v-show="flowConfig.ptKindType.includes('6')">分配到岗位(机构+级别+岗位)</Option>
                                        <Option :value="'99'" v-show="flowConfig.ptKindType.includes('99')">业务端指定处理人</Option>
                                    </Select>
                                </FormItem>
                                <FormItem :label-width="92" label="可选参与者">
                                    <Input v-model="datas.assigners.showResult" :key="`${timeStamp}-result`" :disabled="disableAssignResult()" placeholder=""
                                           readonly style="width: 400px" type="text" @on-focus="showOrgChoiceHandler"/>
                                    &nbsp;
                                    <Button :disabled="disableAssignResult()" type="primary" @click="clearAssignResult">清空
                                    </Button>
                                </FormItem>
                                <OrgChoiceModule :request-methods="requestMethods" :flow-config="flowConfig" :config="choiceConfig" :datas="datas.assigners" :showOrgChoice="showOrgChoice"/>
                            </div>
                            <div v-if="this.datas.assigners.ptassign ==='2'">
                                <FormItem :label-width="100" label="参与者类型">
                                    <Select v-model="datas.assigners.ptkind" :disabled="disableAssiger()" size="small"
                                            style="width:230px" @on-change="clearAssignResult">
                                        <Option :value="'1'">分配到人员</Option>
                                        <Option :value="'3'">分配到机构</Option>
                                        <Option :value="'5'">分配到群组</Option>
                                        <Option :value="'4'">分配到机构岗位</Option>
                                        <Option :value="'6'">分配到岗位(机构+级别+岗位)</Option>
                                        <Option :value="'99'" v-show="flowConfig.ptKindType.includes('99')">业务端指定处理人</Option>
                                    </Select>
                                </FormItem>
                                <br>
                                <FormItem :label-width="80" label="参数选择">
                                    <span v-if="datas.assigners.ptkind==='1'">人员&nbsp;&nbsp;</span>
                                    <span v-if="datas.assigners.ptkind==='3'">机构&nbsp;&nbsp;</span>
                                    <span v-if="datas.assigners.ptkind==='5'">群组&nbsp;&nbsp;</span>
                                    <Select v-if="datas.assigners.ptkind==='1' || datas.assigners.ptkind==='3' || datas.assigners.ptkind==='5'"
                                            v-model="assignResult"
                                            style="width:200px" @on-change="calAssignResult">
                                        <Option v-for="item in variables" :key="item.name" :value="item.name">
                                            {{ item.name }}
                                        </Option>
                                    </Select>
                                    <span v-if="datas.assigners.ptkind==='4' || datas.assigners.ptkind==='6'">&nbsp;&nbsp;机构&nbsp;&nbsp;</span>
                                    <Select v-if="datas.assigners.ptkind==='4' || datas.assigners.ptkind==='6' "
                                            v-model="assignOrg"
                                            style="width:120px" @on-change="calAssignResult">
                                        <Option v-for="item in variables" :key="item.name" :value="item.name">
                                            {{ item.name }}
                                        </Option>
                                    </Select>
                                    <span v-if="datas.assigners.ptkind==='6'">&nbsp;&nbsp;级别&nbsp;&nbsp;</span>
                                    <Select v-if="datas.assigners.ptkind==='6' " v-model="assignLevel"
                                            style="width:120px" @on-change="calAssignResult">
                                        <Option v-for="item in variables" :key="item.name" :value="item.name">
                                            {{ item.name }}
                                        </Option>
                                    </Select>
                                    <span v-if="datas.assigners.ptkind==='4' || datas.assigners.ptkind==='6'">&nbsp;&nbsp;岗位&nbsp;&nbsp;</span>
                                    <Select v-if="datas.assigners.ptkind==='4' || datas.assigners.ptkind==='6' "
                                            v-model="assignRole"
                                            style="width:120px" @on-change="calAssignResult">
                                        <Option v-for="item in variables" :key="item.name" :value="item.name">
                                            {{ item.name }}
                                        </Option>
                                    </Select>
                                </FormItem>
                            </div>
                            <div v-show="this.datas.assigners.ptassign ==='3'" :key="`${timeStamp}-ptassign`">
                                <FormItem style="width: 600px" :label-width="120"  label="调用方式:">
                                    <RadioGroup v-model="datas.assigners.callType" @on-change="callTypeHandler" >
                                        <Radio label="1">
                                            <span>本地</span>
                                        </Radio>
                                        <Radio label="2">
                                            <span>远端</span>
                                        </Radio>
                                    </RadioGroup>
                                </FormItem>
                                <FormItem :label-width="120" label="适配器类型:">
                                    <RadioGroup v-model="datas.assigners.adaptertype" @on-change="callAdapterHandler" >
                                        <Radio label="1">
                                            <span>Spring Bean</span>
                                        </Radio>
                                        <Radio label="2">
                                            <span>普通JavaBean</span>
                                        </Radio>
                                        <Radio label="3" v-show="datas.assigners.callType==='2'">
                                            <span>URI</span>
                                        </Radio>
                                    </RadioGroup>
                                </FormItem>
                                <FormItem :label-width="100" v-if="!showUri" label="策略适配器">
                                    <Input v-model="datas.assigners.result" :key="`${timeStamp}-resultInput`" placeholder="" style="width: 500px"
                                           type="text"/>
                                </FormItem>
                                <FormItem :label-width="100"  v-if="showUri" label="回调URI:">
                                    <Input v-model="datas.assigners.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 480px" type="text">
                                    </Input>
                                </FormItem>
                                <FormItem :label-width="100" label="回调参数:">
                                    <Input v-model="datas.assigners.callBackParam" :key="`${timeStamp}-callBackParam`" placeholder="回调参数" style="width: 480px" type="text">
                                    </Input>
                                </FormItem>
                            </div>
                            <div v-if="this.datas.assigners.ptassign ==='4'">
                                <Row style="padding-bottom: 5px;text-align: right"><Button size="small" type="primary" v-on:click="assignRule()">添加</Button></Row>
                                <Table :columns="assignRuleColumn" :data="ruleList" border size="small" width="630" maxHeight="200">
                                    <template slot="resultType" slot-scope="{ row, index }">
                                        <span v-show="row.resultType==='0'">用户</span>
                                        <span v-show="row.resultType==='1'">用户组</span>
                                    </template>
                                    <template slot="action" slot-scope="{ row, index }">
                                        <Button size="small" type="primary" @click="assignRule(row)">编辑</Button>
                                        <Button size="small" type="error" @click="removeRule(index)">删除</Button>
                                    </template>
                                </Table>
                            </div>
                            <RuleModel :flow-assign-info="datas.assigners" :rule-edit-type="ruleType" :datas="ruleForm" :variables="variables" @modifyData="modifyRuleData" :config="ruleModal" :flow-config="flowConfig" :request-methods="requestMethods"/>
                        </div>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="退回配置信息" name="backConfig">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                退回权限
                            </span>
                            <FormItem :label-width="200" label="能否退回到其他节点:">
                                <i-switch :falseValue="'0'" :trueValue="'1'"
                                          :value="datas.reject.rejectauth.charAt(0)" size="large" @on-change="setRejectAuthSource">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>

                            <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                                <i-switch :falseValue="'0'" :trueValue="'1'"
                                          :value="datas.reject.rejectauth.charAt(1)" size="large" @on-change="setRejectAuthTarget">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                默认退回选项
                            </span>
                            <FormItem :label-width="40" label="">
                                <RadioGroup v-model="datas.reject.rejectdefault">
                                    <Radio label="1">
                                        <span>退回到首节点</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>退回到上一节点</span>
                                    </Radio>
                                    <Radio label="3">
                                        <span>默认退回节点</span>
                                    </Radio>
                                </RadioGroup>
                                <Input v-if="datas.reject.rejectdefault=='3'" v-model="datas.reject.rejectnodeid"
                                       placeholder="" style="width:120px" type="text"/>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                退回目标任务提交流转方式
                            </span>
                            <FormItem :label-width="40" label="">
                                <RadioGroup v-model="datas.reject.rejectdcontinue">
                                    <Radio label="1">
                                        <span>直接返回本节点</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>按照历史路径重新流转</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                退回目标分配方式
                            </span>
                            <FormItem :label-width="40" label="">
                                <RadioGroup v-model="datas.reject.rejectassigntype">
                                    <Radio label="0">
                                        <span>重新分配</span>
                                    </Radio>
                                    <Radio label="1">
                                        <span>最后一次执行人</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                        </div>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
        <TabPane label="时效" name="timelimit">
            <div class="properties_contain">
                        <span class="title">
                            任务时效性配置
                        </span>
                <Card :bordered="false">
                    <Button slot="extra" size="small" type="primary" v-on:click="addTimeLimit($event)">
                        添加
                    </Button>
                    <Form inline>
                        <FormItem :label-width="100" label="按工作日历:">
                            <i-switch v-model="timeLimit.refcalendar" :disabled="true" :falseValue="'0'" :trueValue="'1'" size="large">
                                <span slot="open">开</span>
                                <span slot="close">关</span>
                            </i-switch>
                        </FormItem>
                        <FormItem :label-width="50" label="时限:">
                            <InputNumber v-model="timeLimit.consumeday" :min="0" :step="1" placeholder=""
                                         style="width: 50px"/>
                            天
                            <InputNumber v-model="timeLimit.consumehour" :max="23" :min="0" :step="1"
                                         placeholder="" style="width: 50px"/>
                            小时
                            <InputNumber v-model="timeLimit.consumeminute" :max="59" :min="0" :step="1"
                                         placeholder="" style="width: 50px"/>
                            分钟
                            <InputNumber v-model="timeLimit.consumesecond" :max="59" :min="0" :step="1"
                                         placeholder="" style="width: 50px"/>
                            秒
                        </FormItem>
                        <FormItem style="width: 600px" :label-width="120"  label="调用方式:">
                            <RadioGroup v-model="timeLimit.callType" @on-change="callTimeTypeHandler" >
                                <Radio label="1">
                                    <span>本地</span>
                                </Radio>
                                <Radio label="2">
                                    <span>远端</span>
                                </Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem :label-width="100" label="适配器类型:">
                            <RadioGroup v-model="timeLimit.adaptertype" @on-change="callTimeAdapterHandler">
                                <Radio label="1">
                                    <span>Spring Bean</span>
                                </Radio>
                                <Radio label="2">
                                    <span>普通JavaBean</span>
                                </Radio>
                                <Radio label="3" v-show="timeLimit.callType==='2'">
                                    <span>URI</span>
                                </Radio>
                            </RadioGroup>
                        </FormItem>
                        <FormItem :label-width="100" label="适配器名:" v-if="!showTimeUri">
                            <Input v-model="timeLimit.adaptername" placeholder="适配器名" style="width: 490px" type="text">
                            </Input>
                            <span style="vertical-align:middle;font-size: 24px;line-height:24px;color: red">*</span>
                        </FormItem>
                        <FormItem :label-width="100"  v-if="showTimeUri" label="回调URI:">
                            <Input v-model="timeLimit.callBackURI" :key="`${timeStamp}-timecallBackURI`" placeholder="回调URI" style="width: 480px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="100" label="回调参数:">
                            <Input v-model="timeLimit.callBackParam" :key="`${timeStamp}-timecallBackParam`" placeholder="回调参数" style="width: 480px" type="text">
                            </Input>
                        </FormItem>
                    </Form>
                    <Table :columns="timeLimitColumn" :data="datas.timelimits" border size="small" width="630">
                        <template slot="refcalendar" slot-scope="{ row, index }">
                            <span v-if="row.refcalendar=='1'">
                                是
                            </span>
                            <span v-if="row.refcalendar=='0'">
                                否
                            </span>
                        </template>
                        <template slot="adapterType" slot-scope="{ row, index }">
                            <span v-show="row.adaptertype=='2'">
                                普通JavaBean
                            </span>
                            <span v-show="row.adaptertype=='1'">
                                Spring Bean
                            </span>
                            <span v-show="row.adaptertype=='3'">
                                URI
                            </span>
                        </template>
                        <template slot="action" slot-scope="{ row, index }">
                            <Button size="small" type="error" @click="removeTimeLimit(index)">删除</Button>
                        </template>
                    </Table>
                </Card>
            </div>
        </TabPane>
        <TabPane label="业务扩展属性" name="bussineext">
            <div class="properties_contain">
                        <span class="title">
                            参数映射关系
                        </span>
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="110" label="业务扩展字段1:">
                            <Select v-model="datas.userexpansions[0].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段2:">
                            <Select v-model="datas.userexpansions[1].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段3:">
                            <Select v-model="datas.userexpansions[2].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段4:">
                            <Select v-model="datas.userexpansions[3].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段5:">
                            <Select v-model="datas.userexpansions[4].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段6:">
                            <Select v-model="datas.userexpansions[5].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段7:">
                            <Select v-model="datas.userexpansions[6].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段8:">
                            <Select v-model="datas.userexpansions[7].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
    </Tabs>
</template>

<script>
import './setting.less';
import OrgChoiceModule from './OrgChoiceModule'
import EventRegister from "./EventRegister";
import RuleModel from "./RuleModel";
import {deepClone} from "@/libs/utils/ObjectClone";
export default {
    name: 'ArtificNodeSetting',
    components: {
        OrgChoiceModule,
        EventRegister,
        RuleModel
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        },
        requestMethods:{
            type:Object,
            default:()=>{
                return {}
            }
        },
        taskNodeList: {
            type:Array,
            default: ()=>{
                return [];
            }
        },
        processInfo:{
            type: Object,
            required: true
        }
    },
    computed:{
        excludeTaskList(){
            return this.taskNodeList.filter(node=>{
                return node.value!==this.datas.base.id;
            })

        }
    },
    data() {
        return {
            showOrgChoice: false,
            currectTab: 'baseInfo',
            assignResult: '',
            assignOrg: '',
            assignRole: '',
            assignLevel: '',
            showUri:true,
            showTimeUri:true,
            timeStamp:'',
            choiceConfig: {open: false, title: '',},
            eventTypeList: [{
                value: '1',
                label: '节点开始时'
            }, {
                value: '2',
                label: '节点结束时'
            }],
            timeLimit: {
                consumeday: 0,
                consumehour: 0,
                consumeminute: 0,
                consumesecond: 0,
                adaptertype: '1',
                adaptername: '',
                refcalendar: '0',
                callType:'2',
                callBackURI:"",
                callBackParam:'',
            },
            timeLimitColumn: [
                {
                    title: '工作历',
                    key: 'refcalendar',
                    slot: 'refcalendar',
                    width: 80,
                    fixed: 'left'
                }, {
                    title: '天',
                    key: 'consumeday'
                    ,
                    width: 80,
                }, {
                    title: '小时',
                    key: 'consumehour',
                    width: 80,
                }, {
                    title: '分',
                    key: 'consumeminute',
                    width: 80,
                }, {
                    title: '秒',
                    key: 'consumesecond',
                    width: 80,
                },
                {
                    title: '适配器类型',
                    key: 'adaptertype',
                    width: 200,
                    slot: 'adapterType'
                },
                {
                    title: '适配器名称',
                    width: 200,
                    key: 'adaptername',
                },
                {
                    title: '回调地址',
                    width: 200,
                    key: 'callBackURI',
                },{
                    title: '参数',
                    width: 200,
                    key: 'callBackParam',
                }
                ,
                {
                    title: '操作',
                    key: 'action',
                    width: 80,
                    slot: 'action',
                    fixed: 'right'
                }
            ],
            assignRuleColumn:[
                {
                    title: '规则描述',
                    key: 'ruleName',
                    width: 180,
                    tooltip:true
                },
                {
                    title: '优先级',
                    key: 'priority',
                    width: 100,
                },{
                    title: '结果类型',
                    slot: 'resultType',
                    key: 'resultType',
                },
                {
                    title: '结果',
                    key: 'showResult',
                    width: 100,
                    tooltip:true
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 140,
                    slot: 'action',
                    fixed: 'right'
                }
            ],
            excludes:[],
            ruleList:[],
            ruleForm:{
               ruleName:'',
               resultType:'',
               result:'',
               showResult:'',
               ruleData:'',
               priority:0
            },
            ruleType:'edit',
            ruleModal:{
                show:false
            },
            lastChoiceType:''
        }
    },
    methods: {
        callTypeHandler(){
            this.$nextTick(()=>{
                if(this.datas.assigners.callType==='1'&&this.datas.assigners.adaptertype==='3'){
                    this.datas.assigners.adaptertype='1';
                }
                this.showUri = this.datas.base.callType==='2'
            })
        },
        callTimeTypeHandler(){
            this.$nextTick(()=>{
                if(this.timeLimit.callType==='1'&&this.timeLimit.adaptertype==='3'){
                    this.timeLimit.adaptertype='1';
                }
                this.showTimeUri = this.timeLimit.callType==='2'
            })
        },
        updateShipAuthSource(status) {
            this.datas.base.skipauth = status + this.datas.base.skipauth.charAt(1);
        },
        updateShipAuthTarget(status) {
            this.datas.base.skipauth = this.datas.base.skipauth.charAt(0) + status;
        },
        setRejectAuthSource(status) {
            this.datas.reject.rejectauth = status + this.datas.reject.rejectauth.charAt(1);
        },
        setRejectAuthTarget(status) {
            this.datas.reject.rejectauth = this.datas.reject.rejectauth.charAt(0) + status;
        },
        disableAssignmode() {
            return this.datas.assigners.pthistory === '1';
        },
        disableAdapter(){
          return this.datas.assigners.assignmode!=='1'&& this.datas.assigners.assignmode!=='4';
        },
        disableAssiger() {
            return this.datas.assigners.pthistory === '1' ||
                this.datas.assigners.pthistory === '3' ||
                this.datas.assigners.pthistory === '4';
        },
        disableAssignResult() {
            return this.datas.assigners.ptkind==='99'||this.disableAssiger();
        },
        modifyRuleData(type,row){
            console.log(row)
            if(type==='add'){
                this.ruleList.push(row);
            }else {
                this.ruleList[row._index].ruleName = row.ruleName;
                this.ruleList[row._index].resultType = row.resultType;
                this.ruleList[row._index].result = row.result;
                this.ruleList[row._index].showResult = row.showResult;
                this.ruleList[row._index].ruleData = row.ruleData;
                this.ruleList[row._index].priority = row.priority;
                this.ruleList[row._index].express = row.express;
            }
            console.log(this.ruleList)
            this.datas.assigners.result = JSON.stringify(this.ruleList);
        },
        assignRule(row = undefined){
            if(row){
                this.ruleForm = row;
                this.ruleType = 'edit';
                console.log(row,'edit======')
            }else {
                this.ruleForm = {
                    ruleName:'',
                    resultType:'0',
                    result:'',
                    showResult:'',
                    ruleData:'',
                    priority:0
                };
                this.ruleType = 'add';
            }
            this.$nextTick(()=>{
                this.ruleModal.show = true;
            })
        },
        removeRule(index){
            let self = this;
            this.$Modal.confirm({
                title: "确认",
                content: '确认删除规则数据?',
                onOk: () => {
                    self.ruleList.splice(index,1);
                    this.datas.assigners.result = JSON.stringify(this.ruleList);
                }
            });
        },
        clearAssignResult() {
            // this.datas.assigners.ptkind = '1';
            this.$nextTick(()=>{
                this.datas.assigners.result = '';
                this.datas.assigners.showResult = '';
                this.assignOrg = '';
                this.assignLevel = '';
                this.assignRole = '';
                if(this.datas.assigners.ptkind==='99') {
                    this.datas.assigners.showResult = '业务端指定处理人';
                    this.datas.assigners.result = 'bizAssign';
                }else {
                    if (this.datas.assigners.ptassign !== '3') {
                        this.datas.assigners.adaptertype = '1';
                        this.datas.assigners.callBackURI = '';
                        this.datas.assigners.callBackParam = '';
                    } else {
                        if (this.datas.assigners.adaptertype === undefined || this.datas.assigners.adaptertype === '')
                            this.datas.assigners.adaptertype = '1';
                    }
                }
            })
        },
        calAssignResult() {
            if (this.datas.assigners.ptassign === '2') {
                if (
                    this.datas.assigners.ptkind === '1' ||
                    this.datas.assigners.ptkind === '3' ||
                    this.datas.assigners.ptkind === '5'
                ) {
                    this.datas.assigners.result = this.assignResult;
                } else if (this.datas.assigners.ptkind === '4') {
                    this.datas.assigners.result =
                        this.datas.result = (this.assignOrg === '' ? '' : ('(机构)=' + this.assignOrg)) + (this.assignRole === '' ? '' : ('(岗位)=' + this.assignRole));
                } else if (this.datas.assigners.ptkind === '6') {
                    this.datas.assigners.result =
                        this.datas.result = (this.assignOrg === '' ? '' : ('(机构)=' + this.assignOrg)) + (this.assignLevel === '' ? '' : ('(机构级别)=' + this.assignLevel)) + (this.assignRole === '' ? '' : ('(岗位)=' + this.assignRole));
                }
            }
        },
        checkPtkindValue() {
            if (this.datas.assigners.pthistory === '1') {
                this.datas.assigners.assignmode = '1';
                this.datas.assigners.exittype = 1;
                this.datas.assigners.exitcount = 1;
                this.datas.assigners.ptassign = '1';
                this.datas.assigners.ptkind = '1';
                this.datas.assigners.result = '';
                this.datas.assigners.showResult = '';
                this.datas.assigners.keepFirstAssign = true;
            } else if (this.datas.assigners.pthistory === '3') {
                this.datas.assigners.ptassign = '1';
                this.datas.assigners.ptkind = '';
                this.datas.assigners.exittype = 1;
                this.datas.assigners.result = '';
                this.datas.assigners.exitcount = 1;
            } else if (this.datas.assigners.pthistory === '4') {
                this.datas.assigners.ptassign = '1';
                this.datas.assigners.ptkind = '1';
                this.datas.assigners.exittype = 1;
                this.datas.assigners.exitcount = 1;
                this.datas.assigners.result = '';
            }
        },
        showOrgChoiceHandler() {
            if (this.datas.assigners.ptkind == undefined || this.datas.assigners.ptkind === '') {
                this.$Message.error('请先选择参与者类型')
                return;
            }
            this.choiceConfig.open = true;
            // console.log(typeof this.datas.assigners.ptkind)
            if (this.datas.assigners.ptkind === '1') {
                this.choiceConfig.title = '分配到人员';
            } else if (this.datas.assigners.ptkind === '6') {
                this.choiceConfig.title = '分配到岗位(机构+级别+岗位)';
            } else if (this.datas.assigners.ptkind === '3') {
                this.choiceConfig.title = '分配到机构';
            } else if (this.datas.assigners.ptkind === '4') {
                this.choiceConfig.title = '分配到机构岗位';
            } else if (this.datas.assigners.ptkind === '5') {
                this.choiceConfig.title = '分配到群组';
            } else {
                this.choiceConfig.title = '未选择种类';
            }
        },

        addTimeLimit() {
            if (this.datas.timelimits.length > 0) {
                this.$Message.error('只能有一个适配器');
                return;
            }
            let total = 0;
            total += this.timeLimit.consumeday;
            total += this.timeLimit.consumehour;
            total += this.timeLimit.consumeminute;
            total += this.timeLimit.consumesecond;
            if (total <= 0) {
                this.$Message.error('时间之和必须大于0');
                return;
            }
            if(this.timeLimit.adaptertype!=='3') {
                if (this.timeLimit.adaptername === '') {
                    this.$Message.error('适配器名称不能为空');
                    return;
                }
            }else {
                if (this.timeLimit.callBackURI === '') {
                    this.$Message.error('回调地址不能为空');
                    return;
                }
            }
            this.datas.timelimits.push({
                consumeday: this.timeLimit.consumeday,
                consumehour: this.timeLimit.consumehour,
                consumeminute: this.timeLimit.consumeminute,
                consumesecond: this.timeLimit.consumesecond,
                adaptertype: this.timeLimit.adaptertype,
                adaptername: this.timeLimit.adaptername,
                refcalendar: this.timeLimit.refcalendar,
                callType:this.timeLimit.callType,
                callBackURI:this.timeLimit.callBackURI,
                callBackParam:this.timeLimit.callBackParam,
            });
            this.timeLimit.consumeday = 0;
            this.timeLimit.consumehour = 0;
            this.timeLimit.consumeminute = 0;
            this.timeLimit.consumesecond = 0;
            this.timeLimit.consumesecond = 0;
            this.timeLimit.callType = '2';
            this.timeLimit.adaptertype = '1';
            this.timeLimit.adaptername = '';
            this.timeLimit.refcalendar = '0';
            this.timeLimit.callBackURI = '';
            this.timeLimit.callBackParam = '';
        },

        removeTimeLimit(index) {
            this.datas.timelimits.splice(index, 1);
        },
        fixChoiceType(){
            this.$nextTick(()=>{
                if(this.datas.assigners.assignmode!=='1'&&this.datas.assigners.choiceType==='3'){
                    this.datas.assigners.choiceType='1';
                    this.lastChoiceType = '1';
                }
                if(this.datas.assigners.assignmode!=='1' && this.datas.assigners.assignmode!=='4'){
                    this.datas.assigners.ptassign = '1';
                    this.datas.assigners.choiceType='2';
                    this.lastChoiceType = '2';
                }else{
                    this.datas.assigners.choiceType='1';
                    this.lastChoiceType = '1';
                }
                if(this.datas.assigners.assignmode==='4' || this.datas.assigners.assignmode==='5'){
                    this.datas.assigners.exittype='2';
                    this.datas.assigners.exitcount = 100;
                }
            })
        },
        choiceTypeCheck(){
            if((this.lastChoiceType==='1'||this.lastChoiceType==='2')&&this.datas.assigners.choiceType==='3'){
                this.$Notice.warning({
                    title:'参与者优先级切换警告',
                    desc:'切换到该模式下有可能会导致流程分配出错建议重新配置分配可选参与者'
                });
            }
            this.lastChoiceType = this.datas.assigners.choiceType;
        },
        callAdapterHandler(){
            if(this.datas.assigners.adaptertype==='3'){
                this.$nextTick(()=>{
                    this.datas.assigners.result='';
                    this.showUri = true;
                })
            }else{
                this.$nextTick(()=>{
                    this.datas.assigners.callBackURI='';
                    this.datas.assigners.callBackParam='';
                    this.showUri = false;
                })
            }
        },
        callTimeAdapterHandler(){
            if(this.timeLimit.adaptertype==='3'){
                this.$nextTick(()=>{
                    this.timeLimit.adaptername='';
                    this.showTimeUri = true;
                })
            }else{
                this.$nextTick(()=>{
                    this.timeLimit.callBackURI='';
                    this.timeLimit.callBackParam='';
                    this.showTimeUri = false;
                })
            }
        },
        excludeHandler(values){
            this.datas.assigners.exclude = values.join(';');
        }
    },
    created() {
        let result = this.datas.assigners.result;
        this.lastChoiceType = this.datas.assigners.choiceType;
        if(!this.datas.base.userDefinedIndex){
            this.datas.base.userDefinedIndex='';
        }
        if(!this.datas.base.commonParam){
            this.datas.base.commonParam='';
        }
        if(!this.datas.base.callBackURI){
            this.datas.base.callBackURI='';
        }
        if(!this.datas.base.autoCommitOnNextNodeSameUser||this.datas.base.autoCommitOnNextNodeSameUser===''){
            this.datas.base.autoCommitOnNextNodeSameUser='0';
        }
        if (this.datas.assigners.ptassign === '2') {
            if (
                this.datas.assigners.ptkind === '1' ||
                this.datas.assigners.ptkind === '3' ||
                this.datas.assigners.ptkind === '5'
            ) {
                this.assignResult = result;
            } else {
                let orgStr = '', levelStr = '', roleStr = '';
                if (result.indexOf('(机构)=') > -1) {
                    orgStr = result.substring(result.indexOf('(机构)=') + 5);
                    if (orgStr.indexOf('(') > -1) {
                        orgStr = orgStr.substring(0, orgStr.indexOf("("))
                    }
                }
                if (result.indexOf('(机构级别)=') > -1) {
                    levelStr = result.substring(result.indexOf('(机构级别)=') + 7);
                    if (levelStr.indexOf('(') > -1) {
                        levelStr = levelStr.substring(0, levelStr.indexOf("("))
                    }
                }
                if (result.indexOf('(岗位)=') > -1) {
                    roleStr = result.substring(result.indexOf('(岗位)=') + 5);
                }
                this.assignOrg = orgStr;
                this.assignLevel = levelStr;
                this.assignRole = roleStr;
            }
        }else if(this.datas.assigners.ptassign==='4'){
            this.ruleList = JSON.parse(this.datas.assigners.result);
        }
        if(!this.datas.assigners.choiceType||this.datas.assigners.choiceType===''){
            this.datas.assigners.choiceType = '1';
        }
        this.timeStamp = Date.now()+'';
        if(this.datas.assigners.adaptertype===undefined){
            this.datas.assigners.adaptertype='';
        }
        if(this.datas.assigners.callBackURI===undefined){
            this.datas.assigners.callBackURI='';
        }
        if(this.datas.assigners.callBackParam===undefined){
            this.datas.assigners.callBackParam='';
        }
        if(this.datas.assigners.adaptertype==='3'){
            this.showUri = true;
        }else {
            this.showUri = false;
        }
        if(this.datas.assigners.keepFirstAssign===undefined){
            this.datas.assigners.keepFirstAssign = true;
        }
        if(this.datas.assigners.pthistory===undefined||this.datas.assigners.pthistory===''){
            this.datas.assigners.pthistory='0';
        }
        if(this.datas.assigners.callType===undefined ||this.datas.assigners.callType==='') {
            this.datas.assigners.callType = '2';
        }
        if(this.timeLimit.callType===undefined ||this.timeLimit.callType===''){
            this.timeLimit.callType= '2';
        }
        if(this.timeLimit.adaptertype==='3'){
            this.showTimeUri = true;
        }else {
            this.showTimeUri = false;
        }
        if(this.datas.assigners.exclude && this.datas.assigners.exclude!==''){
            this.excludes.splice(0,this.excludes.length);
            this.excludes.push(...this.datas.assigners.exclude.split(';'));
        }
        if(this.datas.assigners.conflictProcessor==='' || this.datas.assigners.conflictProcessor===undefined||this.datas.assigners.conflictProcessor===null){
            this.datas.assigners.conflictProcessor = 'default';
        }
        if(this.processInfo.base.firsttaskcommit==='0'){
            this.datas.assigners.pthistory='0';
        }
    }
}
</script>
