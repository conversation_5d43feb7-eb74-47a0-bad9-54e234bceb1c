<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                节点属性
                            </span>
                            <FormItem :label-width="80" label="节点id:">
                                <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px"
                                       type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="节点索引:">
                                <Input v-model="datas.base.userDefinedIndex"  placeholder="节点索引"  style="width: 165px" type="text">
                                </Input>
                                <tooltip content="自定义索引,回调时同步回传,可用于业务端对任务归类处理" max-width="150" transfer>
                                    <Icon type="ios-alert" size="20" color="#ff8c49" />
                                </tooltip>
                            </FormItem>
                            <FormItem :label-width="80" label="节点名称:">
                                <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                                </Input>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                执行逻辑
                            </span>
                            <FormItem style="width: 600px" :label-width="120"  label="调用方式:">
                                <RadioGroup v-model="datas.base.callType" @on-change="callTypeHandler" >
                                    <Radio label="1">
                                        <span>本地</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>远端</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem :label-width="120" label="适配器类型:">
                                <RadioGroup v-model="datas.base.adaptertype" @on-change="callAdapterHandler" >
                                    <Radio label="1">
                                        <span>Spring Bean</span>
                                    </Radio>
                                    <Radio label="2">
                                        <span>普通JavaBean</span>
                                    </Radio>
                                    <Radio label="3" v-show="showUri">
                                        <span>URI</span>
                                    </Radio>
                                </RadioGroup>
                            </FormItem>
                            <FormItem :label-width="120"  v-if="datas.base.adaptertype!=='3'" label="适配器名称:">
                                <Input v-model="datas.base.adaptername" :key="`${timeStamp}-adaptername`" placeholder="适配器名" style="width: 480px"
                                       type="text">
                                </Input>
                                <span style="vertical-align:middle;font-size: 14px;line-height:24px;color: red">*</span>
                            </FormItem>
                            <FormItem :label-width="120"    v-if="datas.base.adaptertype==='3'" label="回调URI:">
                                <Input v-model="datas.base.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 480px" type="text">
                                </Input>
                                <span style="vertical-align:middle;font-size: 14px;line-height:24px;color: red">*</span>
                            </FormItem>
                            <FormItem :label-width="120" label="回调参数:">
                                <Input v-model="datas.base.callBackParam" :key="`${timeStamp}-callBackParam`" placeholder="回调参数" style="width: 480px" type="text">
                                </Input>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                跳转退回条件
                            </span>
                            <FormItem :label-width="200" label="其他节点能否跳转到本节点:">
                                <i-switch v-model="datas.base.skipauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                            <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                                <i-switch v-model="datas.base.rejectauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                        <FormItem :label-width="80" label="节点描述:">
                            <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
    </Tabs>
</template>

<script>
import EventRegister from "./EventRegister";
export default {
    name: 'autoNodeSetting',
    components:{
        EventRegister
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            eventTypeList: [{
                value: '1',
                label: '节点执行开始时'
            }, {
                value: '2',
                label: '节点执行结束时'
            }],
            showUri:true,
            timeStamp:''
        }
    },
    methods: {
        callTypeHandler(){
            this.$nextTick(()=>{
                if(this.datas.base.callType==='1'&&this.datas.base.adaptertype==='3'){
                    this.datas.base.adaptertype='1';
                }
                this.showUri = this.datas.base.callType==='2'
            })
        },
        callAdapterHandler(){
            if(this.datas.base.adaptertype==='3'){
                this.$nextTick(()=>{
                    this.datas.base.adaptername='';
                })
            }else{
                this.$nextTick(()=>{
                    this.datas.base.callBackURI='';
                    this.datas.base.callBackParam='';
                })
            }
        }
    },
    created(){
        if(!this.datas.base.callType||this.datas.base.callType==='') {
            this.datas.base.callType = this.flowConfig.defaultCallType;
        }
        this.showUri = this.datas.base.callType==='2'
        this.timeStamp = Date.now()+'';
    }
}
</script>
