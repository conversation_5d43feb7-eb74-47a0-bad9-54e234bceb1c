<style lang="less" scoped rel="stylesheet/less">
.edge-rule{
    .ivu-cell {
        padding: 2px !important;
        font-size: 12px;
        line-height: 14px;
    }

    .ivu-cell-item {
        font-size: 12px;
        line-height: 14px;
        padding-left: 10px;
    }

    .ivu-scroll-loader {
        display: none !important;
    }
}
</style>

<template>
    <div class="edge-rule">
        <Row>
            <Col span="24" v-if="onlyParam">
                <Form inline>
                    <FormItem :label-width="80"   label="回调URI:">
                        <Input v-model="datas.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 600px" type="text">
                        </Input>
                    </FormItem>
                    <FormItem :label-width="80" label="回调参数:">
                        <Input v-model="datas.callBackParam" placeholder="回调参数" style="width: 600px" type="text"/>
                    </FormItem>
                </Form>
            </Col>
            <div v-else>
                <Col span="24">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                路由条件表达式信息
                            </span>
                            <FormItem :label-width="100" label="显示业务描述" style="width: 130px">
                                <Checkbox v-model="datas.descflag" :false-value="'false'" :true-value="'true'"/>
                            </FormItem>
                            <FormItem :label-width="60" label="业务描述">
                                <Input v-model="datas.description" placeholder="业务描述" style="width: 250px" type="text"/>
                            </FormItem>
                            <FormItem :label-width="50" label="优先级">
                                <InputNumber v-model="datas.priority" :max="9999" :min="0" :step="1"
                                             placeholder="" style="width: 100px"/>
                            </FormItem>
                            <br>
                            <FormItem :label-width="80"   label="回调URI:">
                                <Input v-model="datas.callBackURI" :key="`${timeStamp}-callBackURI`" placeholder="回调URI" style="width: 200px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="回调参数:">
                                <Input v-model="datas.callBackParam" placeholder="回调参数" style="width: 300px" type="text"/>
                            </FormItem>
                        </div>
                    </Form>
                </Col>
                <Col span="24">
                    <EdgeRule ref="edgeRule" :request-methods="requestMethods" :variables="ruleVariables" :read-only="false" rule-name="路由表达式" :rule-data="ruleData"/>
                </Col>
            </div>
        </Row>
    </div>
</template>

<script>
import EdgeRule from './flow-rule/flow-rule';
export default {
    name: 'edgeSetting',
    components:{
        EdgeRule:EdgeRule
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        requestMethods:{
            type:Object
        },
        onlyParam:{
            type:Boolean,
            required:true
        }
    },
    data() {
        return {
            ruleData:[],
            isSourceVote:false,
            voteVariable:{
                bizValueExpress:'',
                displayName:'',
                name:'',
                operatorKeyList:['eq','neq'],
                varComponentConfig:JSON.stringify({"placeholder":"投票结果","dicKey":"_flowVoteResult_"}),
                varOperatorScope:'cust',
                varType:'dic',
                _varId:'text',
            }
        }
    },
    computed:{
        ruleVariables() {
            if(this.isSourceVote){
                return [...this.variables,this.voteVariable]
            }else {
                return this.variables;
            }
        }
    },
    created(){
        if(!this.onlyParam) {
            if (this.datas.ruleData) {
                this.ruleData.push(...JSON.parse(this.datas.ruleData));
            }
            if (this.datas.priority === undefined || this.datas.priority === '') {
                this.datas.priority = 0;
            }
        }
        if(this.datas.callBackParam===undefined){
            this.datas.callBackParam = '';
        }
        if(this.item.getSource()
            &&this.item.getSource().getModel()
            &&this.item.getSource().getModel().data
            &&this.item.getSource().getModel().data.properties
            &&this.item.getSource().getModel().data.properties.assigners
            &&this.item.getSource().getModel().data.properties.assigners.assignmode==='5') {
            this.isSourceVote = true;
            this.voteVariable.bizValueExpress = this.item.getSource().getModel().data.properties.base.id+'VotePass';
            this.voteVariable.name = this.item.getSource().getModel().data.properties.base.id+'VotePass';
            this.voteVariable.displayName = '投票结果';
            this.voteVariable._varId = this.item.getSource().getModel().data.properties.base.id+'VotePass';
            console.log(this.variables)
        }
        if(this.datas.callBackURI===undefined){
            this.datas.callBackURI = '';
        }
        this.timeStamp = Date.now()+'';
    },
    methods: {
        _saveRule(){
            return new Promise((resolve) => {
                this.$refs.edgeRule.saveRule((result)=>{
                    if(result.express) {
                        this.datas.content = result.express;
                        this.datas.ruleData = result.ruleData;
                        resolve(true);
                    }else {
                        resolve(false);
                    }
                });
            })

        }
    }
}
</script>
