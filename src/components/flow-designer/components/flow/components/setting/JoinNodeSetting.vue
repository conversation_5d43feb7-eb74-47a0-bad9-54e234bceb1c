<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="80" label="节点id:">
                            <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="节点名称:">
                            <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="匹配节点:">
                            <Input v-model="datas.base.matchid" placeholder="匹配节点" style="width: 490px" type="text">
                            </Input><span
                            style="vertical-align:middle;font-size: 24px;line-height:24px;color: red">*</span>
                        </FormItem>
                        <FormItem :label-width="200" label="其他节点能否跳转到本节点:">
                            <i-switch v-model="datas.base.skipauth" :falseValue="'00'" :trueValue="'01'" size="large">
                                <span slot="open">能</span>
                                <span slot="close">不能</span>
                            </i-switch>
                        </FormItem>
                        <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                            <i-switch v-model="datas.base.rejectauth" :falseValue="'00'" :trueValue="'01'" size="large">
                                <span slot="open">能</span>
                                <span slot="close">不能</span>
                            </i-switch>
                        </FormItem>
                        <FormItem :label-width="80" label="节点描述:">
                            <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
    </Tabs>
</template>

<script>
export default {
    name: 'JoinNodeSetting',
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {}
    },
    methods: {}
}
</script>
