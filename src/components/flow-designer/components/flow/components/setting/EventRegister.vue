<template>
    <div>
        <div class="properties_contain">
            <span class="title">
                事件
            </span>
            <Card :bordered="false">
                <Button style="margin-top: 8px" slot="extra" size="small" type="primary" v-on:click="addEvent($event)">
                    注册
                </Button>
                <Form inline ref="evtForm" :model="evt_" :rules="validateRule">
                    <FormItem :label-width="120" label="事件类型:">
                        <Select v-model="evt_.eventType" style="width:200px">
                            <Option v-for="item in eventTypeList" :key="item.value" :value="item.value">{{
                                    item.label
                                }}
                            </Option>
                        </Select>
                    </FormItem>
                    <FormItem style="width: 600px" :label-width="120"  label="调用方式:">
                        <RadioGroup v-model="evt_.callType" @on-change="resetEvtForm('callType')" >
                            <Radio label="1">
                                <span>本地</span>
                            </Radio>
                            <Radio label="2">
                                <span>远端</span>
                            </Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem :label-width="120"  label="适配器类型:">
                        <RadioGroup v-model="evt_.adapterType" @on-change="resetEvtForm('adType')" >
                            <Radio label="1">
                                <span>Spring Bean</span>
                            </Radio>
                            <Radio label="2">
                                <span>普通JavaBean</span>
                            </Radio>
                            <Radio label="3" v-show="evt_.callType==='2'">
                                <span>URI</span>
                            </Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem :label-width="120" :required="evt_.adapterType!=='3'"  :error="errorMsg" prop="adapterName" v-if="evt_.adapterType!=='3'" label="适配器名:">
                        <Input v-model="evt_.adapterName" placeholder="适配器名" style="width: 490px" type="text">
                        </Input>
                    </FormItem>
                    <FormItem :label-width="120" prop="callBackURI" :required="evt_.adapterType==='3'" :error="errorMsg" v-if="evt_.adapterType==='3'" label="回调URI:">
                        <Input v-model="evt_.callBackURI" placeholder="回调URI" style="width: 490px" type="text">
                        </Input>
                    </FormItem>
                    <FormItem :label-width="120" prop="callBackParam"   label="回调参数:">
                        <Input v-model="evt_.callBackParam" placeholder="回调参数" style="width: 490px" type="text">
                        </Input>
                    </FormItem>
                </Form>
            </Card>
        </div>
        <Table :columns="evtColumn" :data="eventList" border max-height="400" size="small">
            <template slot="type" slot-scope="{ row, index }">
                {{calEventName(row.type)}}
            </template>
            <template slot="adapterType" slot-scope="{ row, index }">
                    <span v-if="row.adaptertype==='3'">
                        URI
                    </span>
                <span v-if="row.adaptertype==='2'">
                        普通JavaBean
                    </span>
                <span v-if="row.adaptertype==='1'">
                        Spring Bean
                    </span>
            </template>
            <template slot="callType" slot-scope="{ row, index }">
                    <span v-if="row.callType==='1'">
                        本地
                    </span>
                <span v-if="row.callType==='2'">
                        远程
                </span>
            </template>
            <template slot="action" slot-scope="{ row, index }">
                <Button size="small" type="error" @click="removeEvt(index)">删除</Button>
            </template>
        </Table>
    </div>
</template>
<script>
export default {
    name: 'event-register',
    props: {
        flowConfig:{
            type: Object
        },
        eventTypeList:{
            type:Array,
            required:true
        },
        defaultEventType:{
            type:String
        },
        eventList:{
            type:Array
        },
    },
    data() {
        const validateAdapterName = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('适配器名称不能为空'));
            } else {
                callback();
            }
        };
        const validateURI = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('回调URI不能为空'));
            } else {
                callback();
            }
        };
        return {
            validateRule:{
                adapterName:[
                    { validator: validateAdapterName, trigger: 'blur' }
                ],
                callBackURI:[
                    { validator: validateURI, trigger: 'blur' }
                ]
            },
            evtColumn: [
                {
                    title: '事件类型',
                    key: 'type',
                    slot: 'type',
                    width:130,
                    fixed:'left',
                    tooltip:true
                },
                {
                    title: '触发方式',
                    key: 'callType',
                    slot: 'callType',
                    width:90,
                    fixed:'left'
                },
                {
                    title: '类型',
                    key: 'adaptertype',
                    slot: 'adapterType',
                    width:130,
                    fixed:'left'
                },
                {
                    title: '适配器名',
                    key: 'adaptername',
                    width:200,
                    tooltip:true
                },
                {
                    title: '回调URI',
                    key: 'callBackURI',
                    width:200,
                    tooltip:true
                },

                {
                    title: '回调参数',
                    key: 'callBackParam',
                    width:200,
                    tooltip:true
                },
                {
                    title: '操作',
                    key: 'action',
                    slot: 'action',
                    width:100,
                    fixed:'right'
                }
            ],
            callTypeList:[
                {
                    value:'1',
                    label:'本地'
                },
                {
                    value:'2',
                    label:'远程'
                }
            ],
            evt_: {
                eventType: '',
                callType:'',
                adapterType: '',
                adapterName: '',
                callBackURI:'',
                callBackParam:'',
            },
            errorMsg:''
        }
    },
    methods: {
        calEventName(eventType){
            for (let index = 0; index < this.eventTypeList.length; index++) {
                if(this.eventTypeList[index].value===eventType){
                    return this.eventTypeList[index].label
                }
            }
            return eventType;
        },
        addEvent(e) {
            let _t = this.evt_;
            this.$refs.evtForm.validate((validate)=>{
                if(validate){
                    this.eventList.push({
                        type: _t.eventType,
                        callType: _t.callType,
                        adaptertype: _t.adapterType,
                        adaptername: _t.adapterName,
                        callBackURI: _t.callBackURI,
                        callBackParam:_t.callBackParam
                    });
                    this.evt_.eventType = this.defaultEventType;
                    this.evt_.adapterType = this.flowConfig.eventAdapterType;
                    this.evt_.adapterName = '';
                    this.evt_.callType = this.flowConfig.defaultCallType;
                    this.evt_.callBackURI = '';
                    this.evt_.callBackParam = '';
                    this.resetEvtForm();
                }
            });
        },
        removeEvt(index) {
            this.eventList.splice(index, 1);
        },
        resetEvtForm(type){
            if(type==='callType'){
                if(this.evt_.callType==='1'&&this.evt_.adapterType==='3'){
                    this.evt_.adapterType ='1';
                }
            }
            this.$nextTick(()=>{
                this.$refs.evtForm.resetFields();
            })
        }
    },
    created(){
        this.evt_.adapterType = this.flowConfig.eventAdapterType
        this.evt_.eventType = this.defaultEventType;
        this.evt_.callType = this.flowConfig.defaultCallType;
    }
}
</script>
