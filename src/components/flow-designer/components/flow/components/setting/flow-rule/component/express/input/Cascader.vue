<template>
    <div style="float:left">
        <Cascader v-if="readOnly" :transfer="true" :data="casData" v-model="values[valueIndex]" :load-data="loadData" @on-change="setName"></Cascader>
        <span v-else style="font-size: 12px">{{valueNames.join("/")}}</span>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    export default {
        name: 'cascader-type',
        mixins: [ruiMixin],
        props: {
            atomKey:{
                type:String,
                require: true
            },
            values: {
                type: Array,
                require:true
            },
            valueNames: {
                type: Array,
                require:true
            },
            valueIndex:{
                type:Number,
                require: true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            inputConfig:{
                type:Object,
                require:true
            }
        },
        data() {
            return {
                casData:[]
            }
        },
        computed: {
        },
        mounted() {
            this.casData = this.getCascaderFirstLevelData(this.atomKey);
            this.valueChecks()
        },
        methods: {
            loadData(item, callback){
                item.loading = true;
                let self = this;
                let levelConfig = this.inputConfig.levelConfig[item._level+1];
                (this.getCascaderFun(this.atomKey))(levelConfig.url,levelConfig.method,levelConfig.params,levelConfig.superKey,item.value).then(data=>{
                    if(data.code=='0000'){
                        data.data.forEach(val=>{
                            let temp = {
                                value: val.value,
                                label: val.title,
                            };
                            if((item._level+1)<(self.inputConfig.level-1)){
                                temp._level = item._level+1;
                                temp.children=[];
                                temp.loading=false;
                            }
                            item.children.push(temp)
                        });
                        item.loading = false;
                        callback()
                    }else{
                        this.$Message.error(data.msg)
                        item.loading = false;
                    }
                }).catch(() => {
                    this.$Message.error('数据加载失败')
                    item.loading = false;
                });
            },
            valueChecks() {
                if(!this.values[this.valueIndex]||this.values[this.valueIndex].length==0){
                    this.$emit('checkError',true,'必须输入');
                    return
                }
                this.$emit('checkError',false,'');
            },
            setName(value,selectedData){
                if(selectedData&&selectedData.length>0) {
                    this.valueNames[this.valueIndex] = selectedData[selectedData.length - 1].__label;
                }else{
                    this.valueNames[this.valueIndex] = '';
                }
                this.$emit('calexp');
            }
        },
        watch:{
            values:{
                handler(){
                    this.valueChecks(this.values[this.valueIndex])
                },
                immediate:true
            }
        }
    }
</script>
