<template>
    <ul class="rule-tree-list" style="height: 350px;overflow-y: auto">
        <tree-item
            v-if="init"
            v-for="(item,index) in treeData"
            :ruleData="item"
            :depth="depth+1"
            :index="index"
            :key="item.uuid"
            :chineseLang="chineseLang"
            :ruleAtoms="ruleAtoms"
            :operations="operations"
            :read-only="readOnly"
            @calexp="handle"
        />
    </ul>
</template>

<script>
    import Item from './Item.vue'
    import '../style/rule-tree.css'
    import {fillUuid} from "../component/util";

    export default {
        components: { 'tree-item': Item },
        props: {
            treeData: {
                type: Array,
                default: function () {
                    return []
                }
            },
            ruleAtoms: {
                type: Array,
                require:true
            },
            operations: {
                type: Array,
                require:true
            },
            chineseLang:{
                type:Boolean,
                require:true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            depth: {
                type: Number,
                default: function () {
                    return 0
                }
            }
        },

        data () {
            return {
                init:false
            }
        },

        created () {
            this.treeData.forEach(node=>{
                fillUuid(node);
            })
            this.init = true;
        },
        computed: {
            hasIfNode() {
                return this.ruleData.filter(row=>{
                    return row.type==='condition'
                }).length>0
            },

        },
        watch: {

        },

        methods: {
            handle(expressValue){
                this.$emit('calexp',expressValue);
            }
        }
    }
</script>
