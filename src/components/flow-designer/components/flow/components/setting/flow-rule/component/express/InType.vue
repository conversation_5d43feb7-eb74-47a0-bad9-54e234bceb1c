<template>
    <div >
        <div v-if="(inputType==='custDic'||inputType==='dic')" >
            <CommonType :location="location" :emit-validate="true" @validateExpress="validateExpress" :is-in-opt="true" @calexp="handle"  :atomKey="atomKey" :inputConfig="inputConfig" :inputType="inputType" :valueIndex="0" :valueNames="valueNames" :values="values" :read-only="readOnly"/>
        </div>
        <div v-else-if="inputType==='component'">
            <CommonType :emit-validate="true" @validateExpress="validateExpress" :is-in-opt="true" @calexp="handle"  :atomKey="atomKey" :inputConfig="inputConfig" :inputType="inputType" :valueIndex="0" :valueNames="valueNames" :values="values" :read-only="readOnly"/>
        </div>
        <div v-else>
            <div  style="float: left;font-weight: bold">(&nbsp;</div>
                <div style="float: left;" v-for="(item,itemIndex) in values">
                    <CommonType :location="location" :emit-validate="true" @validateExpress="validateExpress" :is-in-opt="true" @calexp="handle"  :atomKey="atomKey" :inputConfig="inputConfig" :inputType="inputType" :valueIndex="itemIndex" :valueNames="valueNames" :values="values" :read-only="readOnly"/>
                    <Icon v-if="!readOnly"  type="ios-trash-outline" color="red" size="16" @click="deleteValue(itemIndex)"/>,&nbsp;
                </div>
            <div style="float: left;font-weight: bold">
                <Icon v-if="!readOnly" type="md-add" color="#2d8cf0 " @click="addValue" />
                &nbsp;)&nbsp;
            </div>
        </div>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "../../mixin/rule-mixin";
    import CommonType from "./CommonType";

    const findComponentDownwardByTagName = function (context,tagName){
        const childrens = context.$children;
        let children = [];
        if (childrens.length) {
            for (const child of childrens) {
                const name = child.$options._componentTag;
                if (name === tagName) {
                    children.push(child);
                }
                children.push(...findComponentDownwardByTagName(child, tagName));
            }
        }
        return children;
    }
    export default {
        name: 'in-type',
        components:{
            'CommonType':CommonType
        },
        mixins: [ruiMixin],
        props: {
            atomKey:{
                type:String,
                require: true
            },
            values: {
                type: Array,
                require:true
            },
            valueNames: {
                type: Array,
                require:true
            },
            inputType:{
                type:String,
                require: true
            },
            inputConfig:{
                type:Object,
                require:true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            location:{
                type:String,
                require:true
            }
        },
        data() {
            return {
                validate:false
            }
        },
        methods:{
            addValue(){
                if(this.inputType==='number'){
                    this.values.push(0);
                }else if(this.inputType==='cascader'){
                    this.values.push([]);
                }else{
                    this.values.push('');
                }
                this.valueNames.push('');
            },
            deleteValue(itemIndex){
                this.values.splice(itemIndex,1);
                this.valueNames.splice(itemIndex,1);
            },
            validateExpress(validate){
                const items = findComponentDownwardByTagName(this, 'CommonType');
                const validateResult =( items.filter(value => value.validateError).length===0);
                console.log(validateResult,"==========>in===>result")
                this.validate = validateResult;
                this.$emit('validateExpress',this.location,validateResult);
            },
            handle() {
                this.$emit('calexp');
            },
        }
    }
</script>
