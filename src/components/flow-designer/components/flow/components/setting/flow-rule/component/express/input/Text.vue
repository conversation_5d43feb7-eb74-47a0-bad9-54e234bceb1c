<template>
    <div style="float:left">
        <Input v-if="!readOnly" v-model="values[valueIndex]" :style=style size="small" :placeholder="inputConfig.placeholder" />
        <span v-else style="font-size: 12px">{{values[valueIndex]}}</span>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    export default {
        name: 'text-type',
        mixins: [ruiMixin],
        props: {
            values: {
                type: Array,
                require:true
            },
            valueIndex:{
                type:Number,
                require: true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            inputConfig:{
                type:Object,
                require:true
            }
        },
        data() {
            return {
            }
        },
        computed: {
            style(){
                let width=(this.inputConfig.placeholder?(this.inputConfig.placeholder===''?200:(this.inputConfig.placeholder.length*15)):200);
                if(width<150){
                    width=150;
                }
                return {
                    width:width+'px'
                }
            }
        },
        methods: {
            valueChecks(value) {
                if(value==''){
                    this.$emit('checkError',true,'不能为空');
                }
                if(this.inputConfig.minLength&&value.length<this.inputConfig.minLength){
                    this.$emit('checkError',true,'长度不能小于'+this.inputConfig.minLength);
                    return
                }
                if(this.inputConfig.maxLength&&value.length>this.inputConfig.maxLength){
                    this.$emit('checkError',true,'长度不能大于'+this.inputConfig.maxLength);
                    return
                }
                if(this.inputConfig.reg){
                    let re = new RegExp(this.inputConfig.reg);
                    if(!re.test(value)){
                        this.$emit('checkError',true,this.inputConfig.regError?this.inputConfig.regError:'表达式校验失败');
                        return
                    }
                }
                this.$emit('checkError',false,'');
            }
        },
        watch:{
            values:{
                handler(){
                    this.valueChecks(this.values[this.valueIndex])
                },
                immediate:true
            }
        }
    }
</script>
