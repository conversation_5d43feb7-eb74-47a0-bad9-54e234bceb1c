<template>
    <div style="float:left">
        <NumberInput :formatter="value => formatter.dataFormatter(value,(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)" :placeholder="inputConfig.placeholder"
                     :precision="(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2" :step="0"
                     :style=style
                     v-if="inputConfig.type=='number'&&!readOnly"
                     v-model="values[valueIndex]"></NumberInput>

        <span v-if="inputConfig.type=='number'&&readOnly">{{formatter.dataFormatter(values[valueIndex],(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)}}</span>

       <NumberInput :formatter="value => formatter.dataFormatter(value,(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)+'%'" :placeholder="inputConfig.placeholder"
                     :precision="(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2" :step="0"
                     :style=style
                    :parser="value => value.replace(/\%\s?|(,*)/g, '')"
                     v-if="inputConfig.type=='percentage'&&!readOnly"
                     v-model="values[valueIndex]">
       </NumberInput>
        <span v-if="inputConfig.type=='percentage'&&readOnly">{{formatter.dataFormatter(values[valueIndex],(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)+'%'}}</span>
        <Poptip trigger="focus" v-if="inputConfig.type=='currency'&&!readOnly">
            <NumberInput :active-change="true" :formatter="value =>  '￥'+formatter.currencyFormat(value,(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)"
                         :parser="value => value.replace(/\￥\s?|(,*)/g, '')"
                         :placeholder="inputConfig.placeholder"
                         :precision="(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2"
                         :step="0"
                         :style=style
                         v-model="values[valueIndex]"></NumberInput>
            <div
                slot="content">{{ convertCurrencyToChinese(values[valueIndex]) }}
            </div>
        </Poptip>
        <span v-if="inputConfig.type=='currency'&&readOnly" style="font-size: 12px">{{'￥'+formatter.currencyFormat(values[valueIndex],(inputConfig.scale||inputConfig.scale==0)?inputConfig.scale:2)}}</span>
    </div>
</template>
<style lang="less" scoped>
</style>
<script>
    import ruiMixin from "_c/rui-auto/rui-rules/src/mixin/rule-mixin";
    import NumberInput from '_c/rui-auto/rewrite/number-input'
    import {currencyFormat, numberFormat,percentageParse} from "@/libs/tools";

    export default {
        name: 'number-type',
        mixins: [ruiMixin],
        components: {
            'NumberInput': NumberInput
        },
        props: {
            values: {
                type: Array,
                require: true
            },
            valueIndex: {
                type: Number,
                require: true
            },
            inputConfig: {
                type: Object,
                require: true
            },valueNames: {
                type: Array,
                require:true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            formatter: {
                type: Object,
                default: () => {
                    return {
                        dataFormatter: numberFormat,
                        percentageParse: percentageParse,
                        currencyFormat: currencyFormat
                    };
                }
            }
        },
        data() {
            return {}
        },
        computed: {
            style(){
                let width=(this.inputConfig.placeholder?(this.inputConfig.placeholder===''?200:(this.inputConfig.placeholder.length*15)):200);
                if(width<150){
                    width=150;
                }
                return {
                    width:width+'px'
                }
            }
        },
        methods: {
            valueChecks(value) {
                if(this.inputConfig.min&&value<this.inputConfig.min){
                    this.$emit('checkError',true,'值不能小于'+this.inputConfig.min);
                    return
                }
                if(this.inputConfig.max&&value>this.inputConfig.max){
                    this.$emit('checkError',true,'值不能大于'+this.inputConfig.max);
                    return
                }
                this.$emit('checkError',false,'');
            }
        },
        watch:{
            values:{
                handler(){
                    this.valueChecks(this.values[this.valueIndex]);
                },
                immediate:true
            }
        }
    }
</script>
