<template>
    <div>
        <li class="rule-tree-item" style="width: 5000px">
            <div class="item-wrapper" onselectstart="return false;">
                <div @click="toggle" class="item-toggle" v-if="showFolder" style="float: left;height: 24px;line-height: 24px">
                    <Icon type="ios-arrow-forward" size="14" v-if="!open"/>
                    <Icon type="ios-arrow-down" size="14" v-if="open" />
                </div>
                <div v-else class="item-toggle">

                </div>
                <div style="vertical-align:bottom;font-size: 14px;line-height: 24px;float: left;height: 24px" >
                    <div v-if="showExpress" class="item-bold ">
                        {{strExpress}}
                    </div>
                    <div v-if="depth==1" class="item-bold">
                        {{ruleData.label}}
                    </div>
                    <Express @validate="validateRule"
                             v-if="(depth>1&&!showExpress)"
                             :index="index"
                             :showLabel="index>0||ruleData.type=='condition'"
                             :label="chineseLang?ruleData.label:ruleData.key"
                             :not="this.ruleData.not"
                             @calexp="handle"
                             :ruleAtoms="ruleAtoms"
                             :express="this.ruleData.express"
                             :operations="operations"
                             :read-only="readOnly"
                    />
                    <div  style="clear:both"></div>
                </div>
                <div v-if="showNot&&!readOnly" class="ivu-dropdown-rel rule-tree-dropdown"  @click="not">
                    <a href="javascript:void(0)" style="color: #9838dc">
                        取反
                    </a>
                </div>
                <div style="vertical-align:bottom;font-size: 14px;line-height: 24px;float: left;height: 24px" v-show="!readOnly">
                    <Dropdown :transfer="true" class='rule-tree-dropdown' v-if="showAdd" trigger="click" @on-click="ruleTypeDropDownSelected">
                        <a href="javascript:void(0)">
                            添加
                        </a>
                        <DropdownMenu slot="list" v-if="(hasIfNode||(this.ruleData.rows.length==0))&&depth==1" >
                            <DropdownItem name="choice">如果</DropdownItem>
                            <DropdownItem name="express" v-if="!hasIfNode">表达式</DropdownItem>
                        </DropdownMenu>
                        <DropdownMenu slot="list" v-if="(!hasIfNode&&(this.ruleData.rows.length>0))||depth>1" >
                            <DropdownItem name="and">并且</DropdownItem>
                            <DropdownItem name="or" v-if="this.ruleData.rows.length>0">或</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </div>
                <div v-if="canSwitch&&!readOnly" style="vertical-align:bottom;font-size: 14px;line-height: 24px;float: left;height: 24px">
                    <Dropdown :transfer="true" class='rule-tree-dropdown'  trigger="click" @on-click="typeSwitch">
                        <a href="javascript:void(0)">
                            切换类型
                        </a>
                        <DropdownMenu slot="list">
                            <DropdownItem name="and" v-if="this.ruleData.type!='and'&&this.index>0&&this.ruleData.type!='then'">并且</DropdownItem>
                            <DropdownItem name="or" v-if="this.ruleData.type!='or'&&this.index>0&&this.ruleData.type!='then'">或</DropdownItem>
                            <DropdownItem name="express" v-if="this.ruleData.type!='express'">表达式</DropdownItem>
                        </DropdownMenu>
                    </Dropdown>
                </div>
                <div v-if="showDelete&&!readOnly"  style=" margin-left:5px" class="ivu-dropdown-rel rule-tree-dropdown"  @click="emitDelete">
                    <a href="javascript:void(0)" style="color: #e74141">
                        删除
                    </a>
                </div>
                <div v-if="depth==1" style=" margin-left:5px;vertical-align:bottom;font-size: 14px;line-height: 24px;float: left;height: 24px">
                    <div  class="item-bold ">
<!--                        {{strExpress}}-->
                    </div>
                </div>
                <div style="clear:both"></div>
                <ul v-if="isFolder" v-show="open" class="rule-tree-list">
                    <tree-item
                        v-for="(item,itemIndex) in ruleData.rows"
                        :rule-data="item"
                        :ruleAtoms="ruleAtoms"
                        :operations="operations"
                        @delete="deleteItem"
                        @calexp="handle"
                        :key="item.uuid"
                        :chineseLang="chineseLang"
                        :index="itemIndex"
                        :depth="depth + 1"
                        :read-only="readOnly"
                    />
                </ul>
            </div>
            <div style="clear:both"></div>
        </li>
    </div>
</template>
<script>
    import Express from "../component/express/TernaryExpress";
    import ruiMixin from "../mixin/rule-mixin";
    import {calStrExpress} from "../component/util";
    import Constants from "../component/constant/constant";
    import { v4 as uuid } from 'uuid';
    export default {
        name: 'tree-item',
        mixins: [ruiMixin],
        components:{
            'Express':Express
        },
        props: {
            ruleData: {
                type: Object,
                default: function () {
                    return {}
                }
            },
            depth:{
              type:Number,
              require: true
            },
            ruleAtoms: {
                type: Array,
                require:true
            },
            operations: {
                type: Array,
                require:true
            },
            name: {
                type: String,
                require: true,
                default: () => {
                    return 'rule edit'
                }
            },
            index:{
                type:Number,
                require:true
            },
            readOnly:{
                type:Boolean,
                require:true,
                default: function () {
                    return false
                }
            },
            chineseLang:{
                type:Boolean,
                require:true
            }
        },

        data() {
            return {
                open:this.ruleData.rows&&this.ruleData.rows.length>0,
                strExpress:calStrExpress(this.ruleData,this.index,this.chineseLang),
                itemValidate:false
            }
        },

        computed: {
            isFolder(){
                return this.ruleData.rows&&this.ruleData.rows.length>0
            },

            hasIfNode() {
                return this.ruleData.rows.filter(row=>{
                    return row.type==='choice'
                }).length>0
            },
            showFolder(){
                return true
            },
            showAdd(){
                return this.ruleData.type == 'express'||this.depth==1
            },
            canSwitch(){
               return this.ruleData.type!='express'&&this.ruleData.type!='choice'&&this.depth>1;
            },
            showDelete(){
                if((this.ruleData.type=='express'&&(this.ruleData.key=='if'||this.ruleData.key=='then'))){
                    return false;
                }
                if((this.ruleData.type=='condition'||this.ruleData.type=='then')){
                    return false
                }
                if(this.depth==1){
                    return false
                }
               return true
            },
            showNot(){
                return this.ruleData.type=='and' ||this.ruleData.type=='condition' ||this.ruleData.type=='or'
            },
            showExpress(){
                return  this.ruleData.type == 'choice'||this.ruleData.type == 'express'
            }
        },

        created() {

        },

        watch: {
            'ruleData.rows':function () {
                if(this.ruleData.rows.length>0  &&this.ruleData.rows[0].type!=='express' &&this.ruleData.rows[0].type!=='condition' &&this.ruleData.rows[0].type!=='choice'){

                    this.ruleData.rows[0].type='and';
                    this.ruleData.rows[0].label='并且';
                    this.ruleData.rows[0].key='&&';
                }
                if(this.ruleData.rows.length>0&&this.ruleData.rows[0].type==='express'){
                    this.ruleData.rows[0].type='express';
                    this.ruleData.rows[0].label='并且';
                    this.ruleData.rows[0].key='&&';
                    if(this.ruleData.rows[0].rows.length>0){
                        this.ruleData.rows[0].rows[0].type='and';
                        this.ruleData.rows[0].rows[0].label='并且';
                        this.ruleData.rows[0].rows[0].key='&&';
                    }
                }
                this.handle();
            },
            'ruleData.type':function(){
                this.handle();
            },
            chineseLang(){
                this.handle();
            }
        },

        methods: {
            validateRule(validate){
                console.log(validate,this)
                this.itemValidate = validate;
            },
            toggle() {
                if (this.isFolder&&this.ruleData.rows.length>0) {
                    this.open = !this.open
                }
            },
            handle() {
                this.strExpress = calStrExpress(this.ruleData,this.index,this.chineseLang)
                this.$emit('calexp',this.strExpress);
            },
            deleteItem(deleteIndex){
                this.ruleData.rows.splice(deleteIndex,1);
                if(this.ruleData.rows.length===0){
                    this.open = false
                }
               this.handle()
            },
            emitDelete(){
                this.$Modal.confirm({
                    title: '确认删除该条配置?',
                    content: '',
                    onOk: () => {
                        this.$emit('delete',this.index);
                    },
                    onCancel: () => {
                    }
                });
            },
            not(){
                this.ruleData.not = !this.ruleData.not;
                this.handle();
            },
            ruleTypeDropDownSelected(type){
                if(type==='choice'){
                    this.addLogicItem(Constants.genChoiceItem());
                    this.open = true;
                }else if(type==='express'){
                    this.addLogicItem(Constants.genAndItem());
                    this.open = true;
                }else if(type==='and'){
                    this.addLogicItem(Constants.genAndItem());
                    this.open = true;
                }else if(type==='or'){
                    this.addLogicItem(Constants.genOrItem());
                    this.open = true;
                }
                this.handle()
            },
            addLogicItem(item){
                if(this.ruleData.type==='express'){
                    this.ruleData.rows.push(item.rows[0])
                }else{
                    this.ruleData.rows.push(item)
                }
            },
            typeSwitch(name){
                if(name==='express'){
                    if(this.ruleData.type==='express'){
                        return
                    }
                    let bak = this.clone(this.ruleData);
                    bak.label='并且 ';
                    bak.key='&&';
                    bak.type='and'
                    this.ruleData.type='express';
                    this.ruleData.not=false;
                    // this.ruleData.express=genTernaryExpression();
                    this.ruleData.uuid=uuid()
                    if(this.ruleData.rows.length===0){
                        delete  this.ruleData.express
                        this.ruleData.rows.push(bak)
                    }
                }else if(name==='and'){
                    this.ruleData.type = 'and';
                    this.ruleData.label = '并且 ';
                    this.ruleData.key = '&&';
                }else if(name==='or'){
                    this.ruleData.type = 'or';
                    this.ruleData.label = '或  ';
                    this.ruleData.key = '||';
                }
                this.handle();
            }
        }
    }
</script>
