import {genTernaryExpression} from "../../../flow-rule/component/util";
import {deepClone} from '@/libs/utils/ObjectClone'
import { v4 as uuid } from 'uuid';
const ifItem = {
    type: 'condition',
    label: '如果 ',
    key: 'if',
    not: false,
    express: genTernaryExpression(),
    rows: []
}

const thenItem = {
    type: 'then',
    label: '则 ',
    key: 'then',
    not: false,
    express: genTernaryExpression(),
    rows: []
}

const genChoiceItem = () => {
    let itemIf = deepClone(ifItem),itemThen = deepClone(thenItem);
    itemIf.uuid = uuid();
    itemThen.uuid = uuid();
    return {
        type: 'choice',
        label: 'choice',
        key: '',
        // express: genTernaryExpression(),
        rows: [itemIf,itemThen ]
    }
}
const genAndItem = () => {
    return {
        type: 'express',
        label: '并且 ',
        key: '&&',
        not: false,
        // express: genTernaryExpression(),
        rows: [
            {
                type: 'and',
                label: '并且 ',
                key: '&&',
                not: false,
                uuid:uuid(),
                express: genTernaryExpression(),
                rows: []
            }
        ]
    }
}

const genExpress = () => {
    return {
        type: 'express',
        label: '',
        key: '',
        not: false,
        uuid:uuid(),
        // express: genTernaryExpression(),
        rows: []
    }
}

const genOrItem = () => {
    return {
        type: 'express',
        label: '或 ',
        key: '||',
        not: false,
        uuid:uuid(),
        // express: genTernaryExpression(),
        rows: [
            {
                type: 'or',
                label: '或 ',
                key: '||',
                not: false,
                express: genTernaryExpression(),
                rows: []
            }
        ]
    }
}

const operators={
    //比较操作符
    specialCompare:[
        {
            key:'in',
            name:"in",
            value:'in'
        },
        {
            key: 'contain',
            name: '包含',
            value: 'contain'
        }
    ],
    compareOperator:[
        {
            key:'lt',
            name:"小于",
            value:'<'
        },
        {
            key:'gt',
            name:"大于",
            value:'>'
        },
        {
            key:'let',
            name:"小于等于",
            value:'<='
        },
        {
            key:'get',
            name:"大于等于",
            value:'>='
        },
        {
            key:'eq',
            name:"等于",
            value:'=='
        },
        {
            key:'neq',
            name:"不等于",
            value:'!='
        },
        {
            key:'like',
            name:"like",
            value:'like'
        },
        {
            key:'sw',
            name:"左匹配",
            value:'startWith'
        },
        {
            key:'ew',
            name:"右匹配",
            value:'endWith'
        }
    ],
    // 逻辑操作符
    logicOperator:[
        {
            key:'and',
            name:"并且",
            value:'&&'
        },
        {
            key:'or',
            name:"或",
            value:'||'
        },
        {
            key:'not',
            name:"非",
            value:'!'
        }
    ],
    // 算数操作符
    arithmeticOperator:[
        {
            key:'minus',
            name:"减",
            value:'-'
        },
        {
            key:'plus',
            name:"加",
            value:'+'
        },
        {
            key:'times',
            name:"乘",
            value:'*'
        },
        {
            key:'divided',
            name:"除",
            value:'/'
        }
    ]
}

const Constants = {
    genChoiceItem: genChoiceItem,
    genExpress: genExpress,
    genAndItem: genAndItem,
    genOrItem: genOrItem,
    operators:operators,
    uuid:uuid
}

export default Constants;
