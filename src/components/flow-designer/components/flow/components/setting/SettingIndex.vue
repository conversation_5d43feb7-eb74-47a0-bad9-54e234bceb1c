<style lang="less" rel="stylesheet/less">
.vertical-center-modal {
    display: flex;
    align-items: center;
    justify-content: center;

    .ivu-modal {
        top: 0;
    }
}
</style>
<template>
    <Modal v-model="config.open" :closable="false" :footerHide="false" :lock-scroll="true"
           :maskClosable="maskCloseable" :scrollable="true" :title="config.title"
           :width="width" :zIndex="zIndex" class-name="vertical-center-modal">
        <div slot="header" class="ivu-modal-header-inner">
            <span>{{ config.title }}</span>
            <a class="ivu-modal-close" @click="resetValue"><i class="ivu-icon ivu-icon-ios-close"></i></a>
        </div>
        <div class="flowSetting">
            <ProcessSetting :flow-config="flowConfig" v-if="config.type==='global'" :datas="configData"></ProcessSetting>
            <StartNodeSetting :flow-config="flowConfig" v-if="config.type==='start'" :datas="configData" :item="item"
                              :variables="variables"></StartNodeSetting>
            <EndNodeSetting :flow-config="flowConfig" v-if="config.type==='end'" :datas="configData" :item="item"
                            :variables="variables"></EndNodeSetting>
            <AutoNodeSetting :flow-config="flowConfig" v-if="config.type==='auto'" :datas="configData" :item="item"
                             :variables="variables"></AutoNodeSetting>
            <RuleNodeSetting :flow-config="flowConfig" v-if="config.type==='rule'" :datas="configData" :item="item"
                             :variables="variables"></RuleNodeSetting>
            <WaitNodeSetting :flow-config="flowConfig"  v-if="config.type==='wait'" :datas="configData" :item="item"
                             :variables="variables"></WaitNodeSetting>
            <VirtualNodeSetting :flow-config="flowConfig" v-if="config.type==='virtual'" :datas="configData" :item="item"
                                :variables="variables"></VirtualNodeSetting>
            <ForkNodeSetting  :flow-config="flowConfig" v-if="config.type==='fork'" :datas="configData" :item="item"
                             :variables="variables"></ForkNodeSetting>
            <JoinNodeSetting :flow-config="flowConfig" v-if="config.type==='join'" :datas="configData" :item="item"
                             :variables="variables"></JoinNodeSetting>
            <DecisionNodeSetting :flow-config="flowConfig" v-if="config.type==='decision'" :datas="configData" :item="item"
                                 :variables="variables"></DecisionNodeSetting>
            <ArtificNodeSetting :request-methods="requestMethods"  :task-node-list="taskNodeList" :flow-config="flowConfig" v-if="config.type==='artific'" :datas="configData" :item="item"
                                :variables="variables" :process-info="processInfo"></ArtificNodeSetting>
            <TimerNodeSetting :flow-config="flowConfig"  v-if="config.type==='timer'" :datas="configData" :item="item"
                              :variables="variables"></TimerNodeSetting>
            <EdgeSetting  ref="edgeSetting" :request-methods="requestMethods" :only-param="config.type==='edgeOnlyParam'" v-if="config.type==='edge'||config.type==='edgeOnlyParam'" :datas="configData" :item="item" :variables="variables"></EdgeSetting>
            <SubProcessSetting :flow-config="flowConfig"  v-if="config.type==='subprocess'" :datas="configData" :item="item"
                               :variables="variables"></SubProcessSetting>
        </div>
        <div slot="footer">
            <Button size="small" @click="resetValue">取消</Button>
            <Button size="small" type="success" v-show="templateStatus.isDeployed==='1'" @click="checkAndUpdate">更新配置</Button>
            <Button size="small" type="primary" @click="checkAndClose">确定</Button>
        </div>
    </Modal>
</template>
<style scoped="scoped">
 .flowSetting >>> .ivu-form-item{
     margin-bottom:5px !important;
 }
</style>
<script>
import ProcessSetting from './ProcessSetting'
import StartNodeSetting from './StartNodeSetting'
import EndNodeSetting from './EndNodeSetting'
import ArtificNodeSetting from './ArtificNodeSetting'
import AutoNodeSetting from './AutoNodeSetting'
import RuleNodeSetting from './RuleNodeSetting'
import WaitNodeSetting from './WaitNodeSetting'
import VirtualNodeSetting from './VirtualNodeSetting'
import ForkNodeSetting from './ForkNodeSetting'
import JoinNodeSetting from './JoinNodeSetting'
import DecisionNodeSetting from './DecisionNodeSetting'
import TimerNodeSetting from './TimerNodeSetting'
import EdgeSetting from './EdgeSetting'
import SubProcessSetting from './SubProcessSetting'
import validate from '../validate'
import FlowUtil from "../../../../global/utils/flowUtils";

export default {
    name: 'SettingIndex',
    components: {
        ProcessSetting,
        StartNodeSetting,
        EndNodeSetting,
        ArtificNodeSetting,
        AutoNodeSetting,
        RuleNodeSetting,
        WaitNodeSetting,
        VirtualNodeSetting,
        ForkNodeSetting,
        JoinNodeSetting,
        DecisionNodeSetting,
        TimerNodeSetting,
        EdgeSetting,
        SubProcessSetting,
    },
    props: {
        config: {
            type: Object
        },
        item: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        configData: {
            type: Object,
            required: true
        },
        editor: {
            type: Object,
            required: true
        },
        requestMethods:{
            type:Object
        },
        flowConfig:{
            type:Object
        },
        taskNodeList: {
            type:Array,
            default: ()=>{
                return [];
            }
        },
        templateStatus:{
            type:Object,
            required:true
        },
        processInfo:{
            type:Object,
            required:true
        }
    },
    data() {
        return {
            zIndex: 1000,
            footHide: true,
            maskCloseable: false,
            bakData: null
        }
    },
    methods: {
        resetValue() {
            let _t = this;
            this.$emit('resetValue', this.config.type, this.item, this.bakData)
            this.bakData = null
            setTimeout(function () {
                _t.config.type = '';
            }, 500)
            _t.config.open = false;
        },
        checkAndClose() {
            let _t = this;
            if (!validate.validate(this.config.type, this, this.configData, this.bakData)) {
                return
            }
            if (this.config.type !== 'global') {
                if (this.config.type === 'edge') {
                    this.$refs.edgeSetting._saveRule().then((result)=>{
                        if(result) {
                            let _content = _t.configData.descflag === 'true' ? (_t.configData.description !== '' ? _t.configData.description : _t.configData.content) : _t.configData.content;
                            if (!_t.configData.content || _t.configData.content === '' || _content === '') {
                                if (_t.configData.from.indexOf('Fork') < 0) {
                                    _content = '请输入条件';
                                }
                            }
                            this.editor.updateItem(this.item, {
                                label: _content,
                            });
                            this.bakData = {}
                            setTimeout(function () {
                                _t.config.type = '';
                            }, 500)
                            _t.config.open = false;
                        }
                    }).catch((e)=>{})
                }else if(this.config.type === 'edgeOnlyParam'){
                    this.bakData = {}
                    setTimeout(function () {
                        _t.config.type = '';
                    }, 500)
                    _t.config.open = false;
                } else {
                    this.editor.updateItem(this.item, {
                        label: _t.configData.base.name,
                    });
                    this.bakData = {}
                    setTimeout(function () {
                        _t.config.type = '';
                    }, 500)
                    _t.config.open = false;
                }
            } else {
                this.bakData = {}
                setTimeout(function () {
                    _t.config.type = '';
                }, 500)
                _t.config.open = false;
            }
        },
        checkAndUpdate() {
            let _t = this;
            if (!validate.validate(this.config.type, this, this.configData, this.bakData)) {
                return
            }
            let modifyItemInfo={
                type:'',
                discernInfo:{}
            }
            if (this.config.type !== 'global') {
                if (this.config.type === 'edge') {
                    this.$refs.edgeSetting._saveRule().then((result)=>{
                        if(result) {
                            let _content = _t.configData.descflag === 'true' ? (_t.configData.description !== '' ? _t.configData.description : _t.configData.content) : _t.configData.content;
                            if (!_t.configData.content || _t.configData.content === '' || _content === '') {
                                if (_t.configData.from.indexOf('Fork') < 0) {
                                    _content = '请输入条件';
                                }
                            }
                            this.editor.updateItem(this.item, {
                                label: _content,
                            });
                            this.bakData = {}
                            modifyItemInfo.type= "edge";
                            modifyItemInfo.discernInfo.source = this.item.getModel().source;
                            modifyItemInfo.discernInfo.target = this.item.getModel().target;
                            console.log(this.item)
                            this.$emit('updateConfig', modifyItemInfo)
                            setTimeout(function () {
                                _t.config.type = '';
                            }, 500)
                            _t.config.open = false;
                        }
                    }).catch((e)=>{})
                }else if(this.config.type === 'edgeOnlyParam'){
                    this.bakData = {}
                    modifyItemInfo.type= "edge";
                    modifyItemInfo.discernInfo.source = this.item.getModel().source;
                    modifyItemInfo.discernInfo.target = this.item.getModel().target;
                    console.log(this.item)
                    this.$emit('updateConfig', modifyItemInfo)
                    setTimeout(function () {
                        _t.config.type = '';
                    }, 500)
                    _t.config.open = false;
                } else {
                    this.editor.updateItem(this.item, {
                        label: _t.configData.base.name,
                    });
                    this.bakData = {}
                    modifyItemInfo.type=this.config.type;
                    modifyItemInfo.discernInfo.nodeId=this.item.getModel().id;
                    this.$emit('updateConfig', modifyItemInfo)
                    setTimeout(function () {
                        _t.config.type = '';
                    }, 500)
                    _t.config.open = false;
                }
            } else {
                this.bakData = {}
                modifyItemInfo.type="global";
                modifyItemInfo.discernInfo={};
                this.$emit('updateConfig', modifyItemInfo)
                setTimeout(function () {
                    _t.config.type = '';
                }, 500)
                _t.config.open = false;
            }
        }
    },
    computed:{
        width(){
            switch (this.config.type){
                case 'global' :
                    return 710;
                case 'subprocess' :
                    return 850;
                case 'edge' :
                    return 1000;
                default:
                    return 700;
            }
        }
    },
    watch: {
        'config.type': {
            handler(newValue, oldValue) {
                if (oldValue === '' && newValue !== '') {
                    this.bakData = FlowUtil.deepClone(this.configData)
                }
            },
            immediate: true,
            deep: true
        }
    }
}
</script>
