<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                节点属性
                            </span>
                            <FormItem :label-width="80" label="节点id:">
                                <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px"
                                       type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="节点索引:">
                                <Input v-model="datas.base.userDefinedIndex"  placeholder="节点索引"  style="width: 165px" type="text">
                                </Input>
                                <tooltip content="自定义索引,回调时同步回传,可用于业务端对任务归类处理" max-width="150" transfer>
                                    <Icon type="ios-alert" size="20" color="#ff8c49" />
                                </tooltip>
                            </FormItem>
                            <FormItem :label-width="80" label="节点名称:">
                                <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="并发数量:">
                                <InputNumber v-model="datas.base.quorum" :min="1" :step="1" placeholder="并发数量"
                                             style="width: 80px"/>
                                &nbsp; <span style="font-size: 12px;color: #cccccc">流程运转中并发产生子流程实例数量</span>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                跳转退回条件
                            </span>
                            <FormItem :label-width="200" label="其他节点能否跳转到本节点:">
                                <i-switch v-model="datas.base.skipauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                            <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                                <i-switch v-model="datas.base.rejectauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                        <FormItem :label-width="80" label="节点描述:">
                            <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="子流程选择" name="subProcessSelectInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                子流程选择
                            </span>
                            <FormItem :label-width="20" label="">
                                <Checkbox v-model="datas.persistnew" :false-value="'0'" :true-value="'1'"/>
                                <span>保持最新版本&nbsp;&nbsp;</span>
                                <Input v-model="datas.subflow.template" placeholder="子流程号" readonly style="width: 400px"
                                       type="text" @on-focus="openSubWin=true"/>
                                &nbsp;&nbsp;
                                <Button type="primary" @click="clearSubWin">清空</Button>
                            </FormItem>
                        </div>
                        <Modal v-model="openSubWin" :closable="false" :footerHide="footHide"
                               :maskClosable="maskCloseable" :width="650" :zIndex="zIndex" class-name="vertical-center-modal">
                            <div slot="header" class="ivu-modal-header-inner">
                                <span>子流程选择</span>
                                <a class="ivu-modal-close" @click="resetValue"><i
                                    class="ivu-icon ivu-icon-ios-close"></i></a>

                            </div>
                            <Row>
                                <Col span="24">
                                    <FormItem :label-width="0" label="">
                                        <Input v-model="flowName" placeholder="流程名" style="width: 200px" type="text"/>
                                        &nbsp;
                                        <Button type="primary" @click="queryFlow">查询</Button>
                                    </FormItem>
                                    <Table :columns="flowColumn" :data="flows" :max-height="250" border highlight-row
                                           size="small">
                                        <template slot="orgChoice" slot-scope="{ row, index }">
                                            <Checkbox v-model="row.chose" @on-change="flowSelect(row)"/>
                                        </template>
                                    </Table>
                                    <div style="height: 5px"></div>
                                    <Page :current="flowPage.index" :page-size="10" :total="flowPage.total"
                                          show-total size="small" @on-change="changeFlowIndex"/>
                                </Col>
                            </Row>
                            <div slot="footer">
                                <Button size="small" @click="resetValue">取消</Button>
                                <Button size="small" type="primary" @click="checkAndClose">确定</Button>
                            </div>
                        </Modal>
                        <Row :gutter="16">
                            <Col span="12">
                                <div class="properties_contain">
                                    <span class="title">
                                        父流程到子流程
                                    </span>
                                    <Form inline>
                                        <FormItem :label-width="60" label="父变量:">
                                            <Select v-model="parentSub.parentvar" style="width:90px">
                                                <Option key="''" value="''">--请选择--</Option>
                                                <Option v-for="item in variables" :key="item.name" :value="item.name">
                                                    {{ item.name }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                        <FormItem :label-width="60" label="子变量:">
                                            <Select v-model="parentSub.subvar" style="width:90px">
                                                <Option key="''" value="''">--请选择--</Option>
                                                <Option v-for="item in subFlowVars" :key="item.name" :value="item.name">
                                                    {{ item.name }}
                                                </Option>
                                            </Select>
                                            &nbsp;&nbsp;
                                            <Button size="small" type="primary"
                                                    v-on:click="addParentToSubEvent($event)">
                                                添加
                                            </Button>
                                        </FormItem>
                                    </Form>
                                    <Table :columns="parentToSub" :data="datas.subflow.parentToSub" border height="250"
                                           max-height="250" size="small">
                                        <template slot="action" slot-scope="{ row, index }">
                                            <Button size="small" type="error" @click="removeParentToSub(index)">删除
                                            </Button>
                                        </template>
                                    </Table>
                                </div>
                            </Col>
                            <Col span="12">
                                <div class="properties_contain">
                                    <span class="title">
                                        子流程到父流程
                                    </span>
                                    <Form inline>
                                        <FormItem :label-width="60" label="子变量:">
                                            <Select v-model="subParent.subvar" style="width:90px">
                                                <Option key="" value="''">--请选择--</Option>
                                                <Option v-for="item in subFlowVars" :key="item.name" :value="item.name">
                                                    {{ item.name }}
                                                </Option>
                                            </Select>
                                        </FormItem>
                                        <FormItem :label-width="60" label="父变量:">
                                            <Select v-model="subParent.parentvar" style="width:90px">
                                                <Option key="''" value="''">--请选择--</Option>
                                                <Option v-for="item in variables" :key="item.name" :value="item.name">
                                                    {{ item.name }}
                                                </Option>
                                            </Select>
                                            &nbsp;&nbsp;
                                            <Button size="small" type="primary"
                                                    v-on:click="addSubToParentEvent($event)">
                                                添加
                                            </Button>
                                        </FormItem>
                                    </Form>
                                    <Table :columns="subToParent" :data="datas.subflow.subToParent" border height="250"
                                           max-height="250" size="small">
                                        <template slot="action" slot-scope="{ row, index }">
                                            <Button size="small" type="error" @click="removeSubToParent(index)">删除
                                            </Button>
                                        </template>
                                    </Table>
                                </div>
                            </Col>
                        </Row>

                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
    </Tabs>
</template>

<script>
import EventRegister from "./EventRegister";
export default {
    name: 'subFlowNodeSetting',
    components:{
        EventRegister
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            openSubWin: false,
            zIndex: 2002,
            footHide: false,
            maskCloseable: false,
            flowName: '',
            flowChoiceTemp: {
                template: '',
                parentToSub: [],
                subToParent: []
            },
            flowColumn: [
                {
                    width: 60,
                    align: 'center',
                    slot: 'orgChoice'
                },
                {
                    title: '版本',
                    key: 'version',
                    width: 80,
                },
                {
                    title: '流程名',
                    key: 'name'
                }
            ],
            flows: [],
            subFlowVars: [],
            flowPage: {
                index: 1,
                total: 0,
            },
            eventTypeList: [{
                value: '1',
                label: '节点执行开始时'
            }, {
                value: '2',
                label: '节点执行结束时'
            }],
            parentToSub: [
                {
                    title: '父变量',
                    key: 'parentvar',
                },
                {
                    title: '子变量',
                    key: 'subvar',
                }
                ,
                {
                    title: '操作',
                    key: 'action',
                    slot: 'action',
                    width: 80,
                }
            ],
            parentSub: {
                parentvar: '',
                subvar: ''
            },
            subToParent: [

                {
                    title: '子变量',
                    key: 'subvar',
                },
                {
                    title: '父变量',
                    key: 'parentvar'
                }
                ,
                {
                    title: '操作',
                    key: 'action',
                    slot: 'action'
                }
            ],
            subParent: {
                parentvar: '',
                subvar: ''
            }
        }
    },
    methods: {
        queryFlow() {
            let _t = this;
            this.$http.post(baseUrl, {
                method: 'queryWfTemplateTree',
                name: _t.flowName,
                pageIndex: _t.flowPage.index,
                pageSize: 10
            }, {})
                .then(function (data) {
                    let response = data.body;
                    if (response.code && response.code == '000000') {
                        _t.flowPage.total = response.data.count;
                        _t.flows = [];
                        response.data.list.forEach(flow => {
                            flow.chose = false;
                            _t.flows.push(flow)
                        })
                    } else {
                        this.$Message.error('数据加载失败');
                    }
                }, function () {
                    this.$Message.error('数据加载失败');
                });
        },
        querySubFlowVars() {
            let _t = this;
            if (!_t.datas.subflow.template || _t.datas.subflow.template == '') {
                return
            }
            this.$http.post(baseUrl, {
                method: 'querySubProcessVariableSet',
                persistnew: _t.datas.persistnew,
                templateId: _t.datas.subflow.template,
            }, {})
                .then(function (data) {
                    let response = data.body;
                    if (response.code && response.code == '000000') {
                        _t.subFlowVars = response.data
                    } else {
                        this.$Message.error('数据加载失败');
                    }
                }, function () {
                    this.$Message.error('数据加载失败');
                });
        },
        changeFlowIndex(pageIndex) {
            this.flowPage.index = pageIndex;
            this.queryFlow();
        },
        flowSelect(selection) {
            this.flows.forEach(flow => {
                let flag = selection.chose;
                flow.chose = false;
                if (flow.deploymentId == selection.deploymentId) {
                    flow.chose = flag;
                    if (flag) {
                        // this.datas.subflow.template = flow.key;
                        this.flowChoiceTemp.template = flow.key
                        if (this.flowChoiceTemp.parentToSub.length) {
                            this.flowChoiceTemp.parentToSub.splice(0, this.flowChoiceTemp.parentToSub.length)
                        }
                        if (this.flowChoiceTemp.subToParent.length) {
                            this.flowChoiceTemp.subToParent.splice(0, this.flowChoiceTemp.subToParent.length)
                        }
                    }
                }
            })
        },
        clearSubWin() {
            this.datas.subflow.template = '';
            this.datas.subflow.parentToSub = [];
            this.datas.subflow.subToParent = [];
            this.datas.persistnew = '0';
        },
        resetValue() {
            this.openSubWin = false;
            let _t = this;
            setTimeout(function () {
                _t.flows = [];
                // this.subFlowVars = [];
                _t.flowPage.index = 1;
                _t.flowPage.total = 0;
            }, 200)
        },
        checkAndClose() {
            this.datas.subflow.template = this.flowChoiceTemp.template;
            if (this.datas.subflow.parentToSub.length) {
                this.datas.subflow.parentToSub.splice(0, this.datas.subflow.parentToSub.length)
            }
            if (this.datas.subflow.subToParent.length) {
                this.datas.subflow.subToParent.splice(0, this.datas.subflow.subToParent.length)
            }
            this.querySubFlowVars();
            this.openSubWin = false;
            let _t = this;
            setTimeout(function () {
                _t.flows = [];
                // this.subFlowVars = [];
                _t.flowPage.index = 1;
                _t.flowPage.total = 0;
            }, 200)
        },
        addParentToSubEvent() {
            if (!this.parentSub.parentvar ||
                !this.parentSub.subvar ||
                this.parentSub.parentvar == '' || this.parentSub.subvar == '') {
                this.$Message.error('父流程到子流程参数不全');
                return;
            }

            this.datas.subflow.parentToSub.push({
                parentvar: this.parentSub.parentvar,
                subvar: this.parentSub.subvar
            });
            this.parentSub.parentvar = '';
            this.parentSub.subvar = '';
        },
        removeParentToSub(index) {
            this.datas.subflow.parentToSub.splice(index, 1);
        }
        , addSubToParentEvent() {
            if (!this.subParent.parentvar ||
                !this.subParent.subvar || this.subParent.parentvar == '' || this.subParent.subvar == '') {
                this.$Message.error('子流程到付流程参数配置不全');
                return;
            }
            this.datas.subflow.subToParent.push({
                parentvar: this.subParent.parentvar,
                subvar: this.subParent.subvar
            });
            this.subParent.parentvar = '';
            this.subParent.subvar = '';
        },
        removeSubToParent(index) {
            this.datas.subflow.subToParent.splice(index, 1);
        }
    },
    created() {
        this.querySubFlowVars();
    }
}
</script>
