<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="80" label="模板id:">
                            <Input v-model="datas.base.id" disabled placeholder="模板id" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="模板名称:">
                            <Input v-model="datas.base.name" placeholder="模板名称" style="width: 200px" type="text">
                            </Input>
                        </FormItem>
                        <FormItem :label-width="80" label="优先级:">
                            <InputNumber v-model="datas.base.priority" placeholder="流程优先级"
                                         style="width: 500px"></InputNumber>
                        </FormItem>
                        <FormItem :label-width="150" label="首个人工节点自动提交:">
                            <i-switch v-model="datas.base.firsttaskcommit" :falseValue="'0'" :trueValue="'1'"
                                      size="large">
                                <span slot="open">是</span>
                                <span slot="close">否</span>
                            </i-switch>
                        </FormItem>
                        <FormItem :label-width="80" label="模板描述:">
                            <Input v-model="datas.base.remark" placeholder="模板描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
                <Card :bordered="false">
                    <Row :gutter="16">
                        <Col span="10">模板版本号:{{ datas.base.version }}</Col>
                        <Col offset="2" span="10">部署日期:{{ datas.base.deploydate }}</Col>
                    </Row>
                    <Row :gutter="16">
                        <Col span="10">创建人:{{ datas.base.creatorid }}-{{ datas.base.creatorname }}</Col>
                        <Col offset="2" span="10">创建时间:{{ datas.base.createdtime }}</Col>
                    </Row>
                    <Row :gutter="16">
                        <Col span="10">最后修改人:{{ datas.base.lastmodifierid }}-{{ datas.base.lastmodifiername }}</Col>
                        <Col offset="2" span="10">最后修改时间:{{ datas.base.lastmodifiedtime }}</Col>
                    </Row>
                </Card>
            </div>
        </TabPane>
        <TabPane label="流程变量" name="process_vars">
            <VariableSetting :variables="datas.variables"></VariableSetting>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
        <TabPane label="业务扩展属性" name="extends">
            <div class="properties_contain">
                        <span class="title">
                            业务扩展属性
                        </span>
                <Card :bordered="false">
                    <Form inline>
                        <FormItem :label-width="110" label="业务扩展字段1:">
                            <Select v-model="datas.userexpansions[0].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段2:">
                            <Select v-model="datas.userexpansions[1].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段3:">
                            <Select v-model="datas.userexpansions[2].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段4:">
                            <Select v-model="datas.userexpansions[3].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段5:">
                            <Select v-model="datas.userexpansions[4].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段6:">
                            <Select v-model="datas.userexpansions[5].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段7:">
                            <Select v-model="datas.userexpansions[6].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                        <FormItem :label-width="110" label="业务扩展字段8:">
                            <Select v-model="datas.userexpansions[7].variablename" style="width:195px">
                                <Option key="" value="">--请选择--</Option>
                                <Option v-for="item in datas.variables" :key="item.name" :value="item.name">{{
                                        item.name
                                    }}-{{ item.displayName }}
                                </Option>
                            </Select>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
    </Tabs>
</template>

<script>
import './setting.less'
import VariableSetting from './variables/variable'
import EventRegister from "./EventRegister";
export default {
    name: 'globalSetting',
    components:{
        VariableSetting,
        EventRegister
    },
    props: {
        datas: {
            type: Object,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            variableColumn: [
                {
                    title: '变量名',
                    key: 'name',
                    width: 200,
                    fixed: 'left'
                },
                {
                    title: '业务名称',
                    key: 'displayName',
                    width: 200,
                },
                {
                    title: '变量类型',
                    key: 'type',
                    slot: 'type',
                    width: 200,
                },
                {
                    title: '变量描述',
                    key: 'remark',
                    width: 200,
                }
                ,
                {
                    title: '操作',
                    key: 'action',
                    slot: 'action',
                    width: 100,
                    fixed: 'right'
                }
            ],
            eventTypeList: [{
                value: '1',
                label: '流程开始时'
            }, {
                value: '2',
                label: '流程结束时'
            }, {
                value: '3',
                label: '流程取消时'
            }]
        }
    },
    methods: {

    },
    created(){
        if(this.datas.base.firsttaskcommit===undefined||this.datas.base.firsttaskcommit==='') {
            this.datas.base.firsttaskcommit = '0';
        }
    }
}
</script>
