<style lang="less" rel="stylesheet/less" scoped>

</style>

<template>
    <Tabs type="line" value="baseInfo">
        <TabPane label="基本信息" name="baseInfo">
            <div style="background:#ececec;padding: 1px">
                <Card :bordered="false">
                    <Form inline>
                        <div class="properties_contain">
                            <span class="title">
                                属性编辑
                            </span>
                            <FormItem :label-width="80" label="节点id:">
                                <Input v-model="datas.base.id" disabled placeholder="节点id" style="width: 200px"
                                       type="text">
                                </Input>
                            </FormItem>
                            <FormItem :label-width="80" label="节点索引:">
                                <Input v-model="datas.base.userDefinedIndex"  placeholder="节点索引"  style="width: 165px" type="text">
                                </Input>
                                <tooltip content="自定义索引,回调时同步回传,可用于业务端对任务归类处理" max-width="150" transfer>
                                    <Icon type="ios-alert" size="20" color="#ff8c49" />
                                </tooltip>
                            </FormItem>
                            <FormItem :label-width="80" label="节点名称:">
                                <Input v-model="datas.base.name" placeholder="节点名称" style="width: 200px" type="text">
                                </Input>
                            </FormItem>
                        </div>
                        <div class="properties_contain">
                            <span class="title">
                                跳转回退属性
                            </span>
                            <FormItem :label-width="200" label="其他节点能否跳转到本节点:">
                                <i-switch v-model="datas.base.skipauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                            <FormItem :label-width="200" label="其他节点能否退回到本节点:">
                                <i-switch v-model="datas.base.rejectauth" :falseValue="'00'" :trueValue="'01'"
                                          size="large">
                                    <span slot="open">能</span>
                                    <span slot="close">不能</span>
                                </i-switch>
                            </FormItem>
                        </div>
                        <FormItem :label-width="80" label="节点描述:">
                            <Input v-model="datas.base.remark" placeholder="节点描述" style="width: 500px" type="textarea">
                            </Input>
                        </FormItem>
                    </Form>
                </Card>
            </div>
        </TabPane>
        <TabPane label="事件触发器" name="events">
            <EventRegister
                :default-event-type="'1'"
                :flow-config="flowConfig"
                :event-list="datas.events"
                :event-type-list="eventTypeList"
            />
        </TabPane>
    </Tabs>
</template>

<script>
import EventRegister from "./EventRegister";
export default {
    name: 'WaitNodeSetting',
    components:{
        EventRegister
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        datas: {
            type: Object,
            required: true
        },
        variables: {
            type: Array,
            required: true
        },
        flowConfig:{
            type: Object
        }
    },
    data() {
        return {
            eventTypeList: [{
                value: '1',
                label: '节点开始时'
            }, {
                value: '2',
                label: '节点结束时'
            }]
        }
    },
    methods: {
    }
}
</script>
