<style lang="less" rel="stylesheet/less" scoped>
.node-element {
    display: inline-block;
    border-top: 1px solid rgba(0, 0, 0, .1);
    border-left: 1px solid rgba(0, 0, 0, .1);
    border-right: 1px solid rgba(0, 0, 0, .1);
    padding: 2px;
    height: 40px;
    width: 118px;

    &:hover {
        border-color: rgba(255, 140, 73, 0.5);
        border-bottom: 1px solid rgba(255, 140, 73, 0.5);
        cursor: move;
    }

    .content {
        display: inline-block;
        width: 117px;
        height: 100%;

        .label-text {
            font-size: 12px;
            color: #222222;
            line-height: 36px;
            text-align: left;
            width: 100%;
            padding-left: 30px;
        }

        .start_node {
            background: no-repeat 10px 10px url("../../../assets/images/designer/start.gif");
        }

        .end_node {
            background: no-repeat 10px 10px url("../../../assets/images/designer/end.gif");
        }

        .artific_node {
            background: no-repeat 10px 10px url("../../../assets/images/designer/artific.gif");
        }

        .auto_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/auto.gif");
        }

        .rule_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/rule.gif");
        }

        .sub_pro_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/subprocess.gif");
        }

        .wait_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/wait.gif");
        }

        .timer_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/timer.gif");
        }

        .virtual_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/virtual.gif");
        }

        .parlbran_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/parlbran.gif");
        }

        .parljoin_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/parljoin.gif");
        }

        .decision_node {
            background: no-repeat 10px 9px url("../../../assets/images/designer/decision.gif");
        }
    }
}
</style>

<template>
    <div
        class="node-element"
        @mousedown="handleMouseDown"
    >
        <div :title="title" class="content">
            <div :class="info.icon" class="label-text ">{{ info.label }}</div>
        </div>
    </div>
</template>

<script>
import JpdlLUtil from "../../../global/utils/flowNodes";

export default {
    name: 'NodeElement',
    props: {
        title: {
            type: String,
            required: true
        },
        info: {
            type: Object,
            required: true,
            default() {
                return {
                    shape: 'circle',
                    label: 'circle',
                    enable: true,
                    width: 40,
                    height: 40,
                    anchorPoints: [[0, 0], [0, 1], [1, 0], [1, 1]],
                    icon: '',
                    img: '',
                    color: '',
                    prefix: 'Node_'
                }
            }
        },
        width: {
            type: Number,
            default: 40
        },
        height: {
            type: Number,
            default: 40
        }, editor: {
            type: Object,
            required: true
        }
    },
    computed: {},
    methods: {
        handleMouseDown(event) {
            console.log(this.editor)
            this.editor.emit('editor:addNode', JpdlLUtil.getDefaultJpdlNode(this.info))
        }
    }
}
</script>
