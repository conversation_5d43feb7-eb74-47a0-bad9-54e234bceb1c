<style lang="less" rel="stylesheet/less" scoped>
.card-box {
    display: inline-block;
    background: #ffffff;
    z-index: 200;
    transition: all .5s ease-in-out;
    min-height: 500px;

    .card-body {
        padding: 0;
        z-index: 2000;
        background: #fff;
        text-align: left;
        overflow-y: auto;
        height: 100%;
    }
}
</style>

<template>
    <div :style="boxStyle" class="card-box">
        <div class="card-body">
            <slot></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CardB<PERSON>',
    components: {},
    props: {
        width: {
            type: Number,
            default: 300
        },
        // 显示位置
        placement: {
            type: String,
            default: 'right'
        },
        // handler 模式
        mode: {
            type: String,
            validator(value) {
                return ['horizontal', 'vertical'].includes(value)
            },
            default: 'vertical'
        }
    },
    data() {
        return {}
    },
    computed: {
        boxStyle() {
            let _t = this
            let style = {}
            if (_t.placement) {
                style[_t.placement] = 0
            }
            if (_t.width) {
                style.width = _t.width + 'px'
            }
            style.height = '100%'
            return style
        }
    },
    methods: {},
    created() {
    }
}
</script>
