<style lang="less" rel="stylesheet/less" scoped>
.tool-item {
    display: inline-block;
    position: relative;
}
</style>

<template>
    <div :class="{ 'tool-item': true, 'active': active, 'disabled': disabled }">
        <slot class="label" name="label">{{ label }}</slot>
        <slot name="content"></slot>
    </div>
</template>

<script>
export default {
    name: 'ToolItem',
    props: {
        label: String,
        active: Boolean,
        disabled: Boolean
    }
}
</script>
