{"id": "", "name": "", "font_family": "iconfont", "css_prefix_text": "icon-", "description": "", "glyphs": [{"icon_id": "557418", "name": "zoom in", "font_class": "zoomin", "unicode": "e725", "unicode_decimal": 59173}, {"icon_id": "2255358", "name": "setting", "font_class": "mzicon-setting", "unicode": "e606", "unicode_decimal": 58886}, {"icon_id": "2449277", "name": "image", "font_class": "image", "unicode": "e6b2", "unicode_decimal": 59058}, {"icon_id": "2514915", "name": "copy", "font_class": "copy", "unicode": "e602", "unicode_decimal": 58882}, {"icon_id": "3015076", "name": "github", "font_class": "github", "unicode": "e502", "unicode_decimal": 58626}, {"icon_id": "3451332", "name": "feed_back_icon", "font_class": "feed_back_icon", "unicode": "e69a", "unicode_decimal": 59034}, {"icon_id": "3500271", "name": "ZOOM", "font_class": "zoom", "unicode": "e671", "unicode_decimal": 58993}, {"icon_id": "4058189", "name": "XML", "font_class": "XML", "unicode": "e601", "unicode_decimal": 58881}, {"icon_id": "4128664", "name": "fit", "font_class": "fit", "unicode": "e8a2", "unicode_decimal": 59554}, {"icon_id": "4180277", "name": "trash", "font_class": "trash", "unicode": "e772", "unicode_decimal": 59250}, {"icon_id": "4473044", "name": "play", "font_class": "play", "unicode": "e648", "unicode_decimal": 58952}, {"icon_id": "4821799", "name": "tick, check mark, ac", "font_class": "tickcheckmarkac", "unicode": "e910", "unicode_decimal": 59664}, {"icon_id": "5742529", "name": "paste", "font_class": "paste", "unicode": "e77a", "unicode_decimal": 59258}, {"icon_id": "6459420", "name": "clear_log", "font_class": "clear_log", "unicode": "e642", "unicode_decimal": 58946}, {"icon_id": "7203277", "name": "stop", "font_class": "stop", "unicode": "e6f1", "unicode_decimal": 59121}, {"icon_id": "7522404", "name": "actual-size-o", "font_class": "actual-size-o", "unicode": "e7cb", "unicode_decimal": 59339}, {"icon_id": "8426410", "name": "full screen", "font_class": "fullscreen", "unicode": "e649", "unicode_decimal": 58953}, {"icon_id": "8617278", "name": "zoom-out", "font_class": "zoom-out", "unicode": "e626", "unicode_decimal": 58918}, {"icon_id": "8894060", "name": "取消发布", "font_class": "quxia<PERSON><PERSON><PERSON>", "unicode": "e735", "unicode_decimal": 59189}, {"icon_id": "8921655", "name": "pause", "font_class": "pause", "unicode": "e65c", "unicode_decimal": 58972}, {"icon_id": "9447707", "name": "save", "font_class": "save", "unicode": "e60c", "unicode_decimal": 58892}, {"icon_id": "9484015", "name": "发布", "font_class": "fabu", "unicode": "e621", "unicode_decimal": 58913}, {"icon_id": "9770671", "name": "Enter delete-1", "font_class": "delete", "unicode": "e501", "unicode_decimal": 58625}, {"icon_id": "9783247", "name": "redo", "font_class": "redo", "unicode": "e712", "unicode_decimal": 59154}, {"icon_id": "10473634", "name": "undo", "font_class": "undo", "unicode": "f3d0", "unicode_decimal": 62416}, {"icon_id": "10479677", "name": "delete", "font_class": "delete1", "unicode": "e515", "unicode_decimal": 58645}, {"icon_id": "10661088", "name": "export", "font_class": "export", "unicode": "e650", "unicode_decimal": 58960}, {"icon_id": "10662051", "name": "import", "font_class": "import", "unicode": "e652", "unicode_decimal": 58962}]}