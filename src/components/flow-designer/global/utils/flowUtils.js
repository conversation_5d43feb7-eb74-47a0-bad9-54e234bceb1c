function objectClone(origin, target) {
    target = target || {};
    for (var prop in origin) {
        if (origin.hasOwnProperty(prop)) {
            if (origin[prop] !== "null" && typeof (origin[prop]) == "object") {
                target[prop] = (Object.prototype.toString.call(origin[prop]) == "[object Array]") ? [] : {};
                objectClone(origin[prop], target[prop]);
            } else {
                target[prop] = origin[prop]
            }
        }
    }
    return target;
}

export default {
    calPointDistence(start, end) {
        return Math.sqrt(Math.pow(start.x - end.x, 2) + Math.pow(start.y - end.y, 2));
    },

    calInsertIndex(point, points) {
        if (points.length === 2) {
            return 0
        }
        let rtnIndex = -1, temp = 9007199254740993;
        for (let index = 0; index < points.length - 1; index++) {
            let lineStart = points[index], lineEnd = points[index + 1]
            let disLine = this.calPointDistence(lineStart, lineEnd);
            let sumOfTwoLine = this.calPointDistence(lineStart, point) + this.calPointDistence(lineEnd, point);
            if (Math.abs(sumOfTwoLine - disLine) <= temp) {
                temp = Math.abs(sumOfTwoLine - disLine);
                rtnIndex = index;
            }
        }
        // console.log(rtnIndex)
        return rtnIndex;
    },
    getLinkPoint(point, anchorPoints) {
        let index = 1, rtn = 1
        let temp = 9007199254740993;
        for (; index <= anchorPoints.length; index++) {
            let tempPoint = anchorPoints[index - 1]
            let dis = this.calPointDistence(point, tempPoint);
            if (dis <= temp) {
                rtn = index
            }
        }
        return rtn
    },
    findDistanceSmaller(point, points, distance) {
        for (let index = 1; index < points.length - 1; index++) {
            if (this.calPointDistence(point, points[index]) <= distance) {
                return index;
            }
        }
        return -1;
    },
    deepClone: objectClone
}
