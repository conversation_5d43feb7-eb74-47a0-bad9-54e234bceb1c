import random from './random'
import flowUtils from './flowUtils'

const getNodeDefault = () => {
    let node = {}
    node.labelCfg = {
        'position': 'bottom',
        'offset': -12,
        'style': {'fontSize': 12, 'fontStyle': 'normal', 'fontWeight': 'normal', 'fill': '#000000'}
    }
    node.width = 32
    node.height = 46
    node.minWidth = 30
    node.minHeight = 46
    node.size = [32, 46]
    node.anchorPoints = [[0, 0], [0, 1], [1, 0], [1, 1], [0.5, 0], [0.5, 1], [0, 0.35], [1, 0.35]]
    node.style = {
        'fill': '#FFFFFF',
        'fillOpacity': 0,
        'stroke': '#FFFFFF',
        'strokeOpacity': 0,
        'lineWidth': 1,
        'lineDash': []
    }
    return node
}

const getEdgeDefault = () => {
    let edge = {}
    edge.id = 'edge_' + random(5)
    edge.startArrow = false
    edge.endArrow = false
    edge.type = 'base-line'
    edge.style = {'stroke': '#000000', 'lineWidth': 1, 'strokeOpacity': 1, 'lineDash': []}
    edge.labelCfg = {
        'position': 'center',
        'refY': 6,
        'style':
            {
                'fontSize': 12,
                'fontStyle': 'normal',
                'fontWeight': 'normal',
                'fill': '#f52816',
            },
        autoRotate: true,
    }
    return edge
}

const processNodeEdges = (edges, node) => {
    if (node.transition) {
        for (let index = 0; index < node.transition.length; index++) {
            // if(node.properties.base.id.indexOf('Start')<0) continue
            let _t = node.transition[index]
            let edge = getEdgeDefault()
            edge.data = _t
            edge.source = _t.from
            edge.target = _t.to
            edge.label = _t.descflag === 'true' ? _t.description : _t.content
            if (node.transition.length > 1 && (!edge.label || edge.label === '') && _t.from.indexOf('Fork') < 0) {
                edge.label = '请输入条件'
            }
            edge.sourceAnchor = 0
            edge.targetAnchor = 0
            edges.push(edge)
        }
    }
}

const updateEdgeInfo = (edges, anchorPointsInfo) => {
    for (let index = 0; index < edges.length; index++) {
        let edge = edges[index]
        let pointInfoStr = edge.data.g.split(';')
        edge.data.pointInfos = [];
        for (let i = 0; i < pointInfoStr.length; i++) {
            let _temp = pointInfoStr[i].split(',');
            edge.data.pointInfos.push({
                x: parseFloat(_temp[0]) + 30
                , y: parseFloat(_temp[1]) + 20
            });
        }
        edge.sourceAnchor = flowUtils.getLinkPoint(edge.data.pointInfos[0], anchorPointsInfo[edge.source])
        edge.targetAnchor = flowUtils.getLinkPoint(edge.data.pointInfos[1], anchorPointsInfo[edge.target])
        // console.log(edge.data.pointInfos, 'init edge points')
    }
}

const calAnchorPoints = (node) => {
    //[[0,0],[0,0.7],[1,0],[1,0.7],[0.5,0],[0.5,0.7],[0,0.35],[1,0.35]];
    let anchorPoints = []
    anchorPoints.push({x: node.x, y: node.y})
    anchorPoints.push({x: node.x, y: node.y + node.height * 0.7});
    anchorPoints.push({x: node.x + node.width, y: node.y});
    anchorPoints.push({x: node.x + node.width, y: node.y + node.height * 0.7});

    anchorPoints.push({x: node.x + node.width / 2, y: node.y})
    anchorPoints.push({x: node.x + node.width / 2, y: node.y + node.height * 0.7})
    anchorPoints.push({x: node.x, y: node.y + node.height * 0.35})
    anchorPoints.push({x: node.x + node.width, y: node.y + node.height * 0.35})
    return anchorPoints
}

export default {
    jpdlNodeToG6Nodes: (jsonNodes, nodes, edges, processInfo) => {
        let anchorPointsInfo = {}
        processInfo.base = jsonNodes.process.properties.base
        processInfo.minX = 999999;
        processInfo.maxX = 0;
        processInfo.minY = 999999;
        processInfo.maxY = 0;
        if (processInfo.base.priority === '') {
            processInfo.base.priority = 1
        } else {
            processInfo.base.priority = parseInt(processInfo.base.priority)
        }
        if (jsonNodes.templeteBeanOut.deployTime) {
            processInfo.base.deploydate = jsonNodes.templeteBeanOut.deployTime
        }

        if (processInfo.base.effecttype === '') {
            processInfo.base.effecttype = '1'
        }
        if (!processInfo.base.userexpansions) {
            processInfo.base.userexpansions = []
        }
        // if (processInfo.base.userexpansions.length < 8) {
        let userexpansions = [];
        for (let index = 1; index <= 8; index++) {
            userexpansions.push({
                userexpcolumn: 'userexpandstring' + index,
                variablename: ''
            })
        }
        if (processInfo.base.userexpansions) {
            userexpansions.forEach(userexpansion => {
                for (let index = 0; index < processInfo.base.userexpansions.length; index++) {
                    if (userexpansion.userexpcolumn === processInfo.base.userexpansions[index].userexpcolumn) {
                        userexpansion.variablename = processInfo.base.userexpansions[index].variablename;
                        break;
                    }
                }
            });
        }
        // }
        processInfo.base.userexpansions = userexpansions;
        processInfo.userexpansions = processInfo.base.userexpansions
        processInfo.variables = jsonNodes.process.properties.variables
        if (!processInfo.variables) {
            processInfo.variables = [];
        }

        processInfo.variables.forEach(variable=>{
            let _randomId = random(10);
            variable._varId = Date.now()+'_'+_randomId;
        });
        processInfo.events = jsonNodes.process.properties.events
        if (!processInfo.events) {
            processInfo.events = [];
        }
        if (jsonNodes.process.start) {
            for (let index = 0; index < jsonNodes.process.start.length; index++) {
                let _t = jsonNodes.process.start[index]
                let node = getNodeDefault()
                node.data = _t
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.type = 'jpdl_start'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'start'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                anchorPointsInfo[node.id] = calAnchorPoints(node)
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.end) {
            for (let index = 0; index < jsonNodes.process.end.length; index++) {
                let _t = jsonNodes.process.end[index]
                let node = getNodeDefault()
                node.data = _t
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.type = 'jpdl_end'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'end'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);
                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }

        if (jsonNodes.process.task) {
            for (let index = 0; index < jsonNodes.process.task.length; index++) {
                let _t = jsonNodes.process.task[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_artific'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'artific'
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                if (!node.data.properties.timelimits) {
                    node.data.properties.timelimits = [];
                }
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                node.assigners = _t.assigners
                node.reject = _t.reject
                node.timelimits = _t.timelimits
                let nodeUserexpansions = [];
                for (let index = 1; index <= 8; index++) {
                    nodeUserexpansions.push({
                        userexpcolumn: 'userexpandstring' + index,
                        variablename: ''
                    })
                }
                if (_t.properties.userexpansions) {
                    nodeUserexpansions.forEach(userexpansion => {
                        for (let index = 0; index < _t.properties.userexpansions.length; index++) {
                            if (userexpansion.userexpcolumn === _t.properties.userexpansions[index].userexpcolumn) {
                                userexpansion.variablename = _t.properties.userexpansions[index].variablename;
                                break;
                            }
                        }
                    });
                }
                _t.properties.userexpansions = nodeUserexpansions;
                node.userexpansions = _t.properties.userexpansions

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.auto) {
            for (let index = 0; index < jsonNodes.process.auto.length; index++) {
                let _t = jsonNodes.process.auto[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_auto'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'auto'
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.wait) {
            for (let index = 0; index < jsonNodes.process.wait.length; index++) {
                let _t = jsonNodes.process.wait[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_wait'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'wait'
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.join) {
            for (let index = 0; index < jsonNodes.process.join.length; index++) {
                let _t = jsonNodes.process.join[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_parljoin'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'join'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.fork) {
            for (let index = 0; index < jsonNodes.process.fork.length; index++) {
                let _t = jsonNodes.process.fork[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_parlbran'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'fork'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.virtual) {
            for (let index = 0; index < jsonNodes.process.virtual.length; index++) {
                let _t = jsonNodes.process.virtual[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_virtual'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'virtual'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.timer) {
            for (let index = 0; index < jsonNodes.process.timer.length; index++) {
                let _t = jsonNodes.process.timer[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_timer'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'timer'
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.subProcess) {
            for (let index = 0; index < jsonNodes.process.subProcess.length; index++) {
                let _t = jsonNodes.process.subProcess[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'sub_process'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'subprocess'
                if (!node.data.properties.events) {
                    node.data.properties.events = [];
                }
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        if (jsonNodes.process.decision) {
            for (let index = 0; index < jsonNodes.process.decision.length; index++) {
                let _t = jsonNodes.process.decision[index]
                let node = getNodeDefault()
                node.data = _t
                node.type = 'jpdl_decision'
                node.id = _t.properties.base.id
                node.label = _t.properties.base.name
                node.configKey = 'decision'
                node.x = parseInt(_t.g.split(',')[0]) + 30
                node.y = parseInt(_t.g.split(',')[1]) + 20
                // node.width = (parseInt(_t.g.split(',')[2])/2).toFixed(0);

                anchorPointsInfo[node.id] = calAnchorPoints(node)
                // node.height = (parseInt(_t.g.split(',')[3])/2).toFixed(0);
                nodes.push(node)
                processNodeEdges(edges, _t)
            }
        }
        updateEdgeInfo(edges, anchorPointsInfo);

        nodes.forEach(node => {
            if ((node.x - 60) < processInfo.minX) {
                processInfo.minX = (node.x - 60);
            }
            if ((node.x + 60) >= processInfo.maxX) {
                processInfo.maxX = (node.x + 60);
            }
            if ((node.y - 60) < processInfo.minY) {
                processInfo.minY = (node.y - 60);
            }
            if ((node.y + 60) >= processInfo.maxY) {
                processInfo.maxY = (node.y + 60);
            }
        })

        edges.forEach(edge => {
            edge.data.pointInfos.forEach(node => {
                if ((node.x - 60) < processInfo.minX) {
                    processInfo.minX = (node.x - 60);
                }
                if ((node.x + 60) >= processInfo.maxX) {
                    processInfo.maxX = (node.x + 60);
                }
                if ((node.y - 60) < processInfo.minY) {
                    processInfo.minY = (node.y - 60);
                }
                if ((node.y + 60) >= processInfo.maxY) {
                    processInfo.maxY = (node.y + 60);
                }
            })
        })
    },
    g6NodesToJpdlNode(nodes, edges, processInfo) {
        // console.log(processInfo)
        let process = {properties: {}};
        process.properties.base = processInfo.base;
        process.properties.base.userexpansions = processInfo.userexpansions;
        process.properties.base.userexpansions.forEach(userexpansion => {
            if (!userexpansion.variablename) {
                userexpansion.variablename = '';
            }
        })
        process.properties.events = processInfo.events;
        process.properties.variables = processInfo.variables;
        let edgeInfos = {};
        edges.forEach(edge => {
            let model = edge.getModel();
            // console.log(edge)
            if (!edgeInfos[edge.getSource().getID()]) {
                edgeInfos[edge.getSource().getID()] = [];
            }
            let transition = {};
            transition.content = model.data.content;
            transition.descflag = model.data.descflag;
            transition.description = model.data.description;
            transition.from = edge.getSource().getID();
            transition.to = edge.getTarget().getID();
            transition.name = model.data.name;
            transition.ruleData = model.data.ruleData;
            transition.priority = model.data.priority;
            transition.callBackParam = model.data.callBackParam;
            transition.callBackURI = model.data.callBackURI;
            let g = [];
            model.data.pointInfos.forEach(point => {
                g.push([point.x - 30, point.y - 20].join(','));
            })
            transition.g = g.join(";");
            edgeInfos[edge.getSource().getID()].push(transition);
        });
        nodes.forEach(node => {
            let model = node.getModel();
            if (model.type === 'jpdl_start') {
                // console.log(model)
                if (!process.start) {
                    process.start = [];
                }
                let startNode = {};
                flowUtils.deepClone(model.data, startNode)
                startNode.transition = edgeInfos[model.id];
                startNode.content = startNode.properties.base.name;
                startNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.start.push(startNode);
            } else if (model.type === 'jpdl_end') {
                // console.log(model)
                if (!process.end) {
                    process.end = [];
                }
                let endNode = {};
                flowUtils.deepClone(model.data, endNode)
                endNode.content = endNode.properties.base.name;
                endNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.end.push(endNode);
            } else if (model.type === 'jpdl_auto') {
                // console.log(model)
                if (!process.auto) {
                    process.auto = [];
                }
                let autoNode = {};
                flowUtils.deepClone(model.data, autoNode)
                autoNode.transition = edgeInfos[model.id];
                autoNode.content = autoNode.properties.base.name;
                autoNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.auto.push(autoNode);
            } else if (model.type === 'jpdl_wait') {
                // console.log(model)
                if (!process.wait) {
                    process.wait = [];
                }
                let waitNode = {};
                flowUtils.deepClone(model.data, waitNode)
                waitNode.transition = edgeInfos[model.id];
                waitNode.content = waitNode.properties.base.name;
                waitNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.wait.push(waitNode);
            } else if (model.type === 'jpdl_timer') {
                // console.log(model)
                if (!process.timer) {
                    process.timer = [];
                }
                let timerNode = {};
                flowUtils.deepClone(model.data, timerNode)
                timerNode.transition = edgeInfos[model.id];
                timerNode.content = timerNode.properties.base.name;
                timerNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.timer.push(timerNode);
            } else if (model.type === 'jpdl_virtual') {
                // console.log(model)
                if (!process.virtual) {
                    process.virtual = [];
                }
                let virtualNode = {};
                flowUtils.deepClone(model.data, virtualNode)
                virtualNode.transition = edgeInfos[model.id];
                virtualNode.content = virtualNode.properties.base.name;
                virtualNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.virtual.push(virtualNode);
            } else if (model.type === 'jpdl_parlbran') {
                // console.log(model)
                if (!process.fork) {
                    process.fork = [];
                }
                let forkNode = {};
                flowUtils.deepClone(model.data, forkNode)
                forkNode.content = forkNode.properties.base.name;
                forkNode.transition = edgeInfos[model.id];
                forkNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.fork.push(forkNode);
            } else if (model.type === 'jpdl_parljoin') {
                // console.log(model)
                if (!process.join) {
                    process.join = [];
                }
                let joinNode = {};
                flowUtils.deepClone(model.data, joinNode)
                joinNode.content = joinNode.properties.base.name;
                joinNode.transition = edgeInfos[model.id];
                joinNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.join.push(joinNode);
            } else if (model.type === 'jpdl_artific') {
                // console.log(model)
                if (!process.task) {
                    process.task = [];
                }
                let taskNode = {};
                flowUtils.deepClone(model.data, taskNode)
                taskNode.content = taskNode.properties.base.name;
                taskNode.transition = edgeInfos[model.id];
                taskNode.properties.userexpansions.forEach(userexpansion => {
                    if (!userexpansion.variablename) {
                        userexpansion.variablename = '';
                    }
                })
                taskNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.task.push(taskNode);
            } else if (model.type === 'sub_process') {
                // console.log(model)
                if (!process.subProcess) {
                    process.subProcess = [];
                }
                let taskNode = {};
                flowUtils.deepClone(model.data, taskNode)
                taskNode.content = taskNode.properties.base.name;
                taskNode.transition = edgeInfos[model.id];
                taskNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.subProcess.push(taskNode);
            }else if (model.type === 'jpdl_decision') {
                // console.log(model)
                if (!process.decision) {
                    process.decision = [];
                }
                let decisionNode = {};
                flowUtils.deepClone(model.data, decisionNode)
                decisionNode.transition = edgeInfos[model.id];
                decisionNode.content = decisionNode.properties.base.name;
                decisionNode.g = [model.x - 30, model.y - 20, 65, 65].join(',');
                process.decision.push(decisionNode);
            }
        })
        return process;
    },
    randomStr() {
        return random(5);
    },
    clone(source, target) {
        flowUtils.deepClone(source, target);
    },
    getDefaultJpdlNode(info) {
        let _randomId = random(5);
        // console.info(info)
        let node = {
            id: info.prefix + _randomId,
            type: info.type,
            label: info.label,
            labelCfg: {
                position: 'bottom',
                offset: -12,
                style: {
                    fontSize: 12,
                    fontStyle: 'normal',
                    fontWeight: 'normal',
                    fill: '#000000'
                }
            },
            width: info.width,
            height: info.height,
            minWidth: info.minWidth,
            minHeight: info.minHeight,
            anchorPoints: info.anchorPoints,
            configKey: info.configKey
        }
        let data = {};
        if (info.type === 'jpdl_start') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                },
                events: []
            };
        } else if (info.type === 'jpdl_end') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '11',
                },
                events: []
            };
        } else if (info.type === 'jpdl_artific') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '11',
                    url: '',
                    userDefinedIndex:'',
                    commonParam:'',
                    "callBackURI": "",
                    "autoCommitOnNextNodeSameUser": "0",
                },
                assigners: {
                    "assignmode": "1",
                    "exitcount": 1,
                    "exittype": "1",
                    "ptassign": "1",
                    "pthistory": "0",
                    "ptkind": "1",
                    "choiceType": "1",
                    "adaptertype": "",
                    "callBackURI": "",
                    "callBackParam": "",
                    "result": ""
                },
                reject: {
                    "rejectassigntype": "0",
                    "rejectauth": "11",
                    "rejectdcontinue": "2",
                    "rejectdefault": "2",
                    "rejectnodeid": ""
                },
                userexpansions: [
                    {
                        userexpcolumn: "userexpandstring1",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring2",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring3",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring4",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring5",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring6",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring7",
                        variablename: ""
                    },
                    {
                        userexpcolumn: "userexpandstring8",
                        variablename: ""
                    }
                ],
                timelimits: [],
                events: []
            };
        } else if (info.type === 'jpdl_auto') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    adaptername: '',
                    adaptertype: '2',
                    rejectauth: '01'
                },
                events: []
            };
        } else if (info.type === 'jpdl_wait') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    rejectauth: '01'
                },
                events: []
            };
        } else if (info.type === 'jpdl_timer') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    "adaptername": "",
                    "adaptertype": "2",
                    "continueday": 0,
                    "continuehour": 0,
                    "continueminute": 0,
                    "continuerefcalendar": "0",
                    "continuesecond": 0,
                    "cyclecount": 0,
                    "cycletype": "1",
                    "delayday": 0,
                    "delayhour": 0,
                    "delayminute": 0,
                    "delayrefcalendar": "0",
                    "delaysecond": 0,
                    "intervalday": 0,
                    "intervalhour": 0,
                    "intervalminute": 0,
                    "intervalrefcalendar": "0",
                    "intervalsecond": 0,
                    "rejectauth": "01",
                    "skipauth": "01"
                },
                events: []
            };
        } else if (info.type === 'jpdl_virtual') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    rejectauth: '01'
                }
            };
        } else if (info.type === 'jpdl_parlbran') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    rejectauth: '01'
                }
            };
        } else if (info.type === 'jpdl_parljoin') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    matchid: '',
                    skipauth: '01',
                    rejectauth: '01'
                }
            };
        } else if (info.type === 'sub_process') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    rejectauth: '01',
                    quorum: 1
                },
                persistnew: 0
                ,
                subflow: {
                    template: '',
                    parentToSub: [],
                    subToParent: []
                }
                ,
                events: []
            };
        }else if (info.type === 'jpdl_decision') {
            data.content = info.label;
            data.g = '';
            data.properties = {
                base: {
                    id: info.prefix + _randomId,
                    name: info.label,
                    remark: '',
                    skipauth: '01',
                    rejectauth: '01',
                    quorum: 1
                },
                persistnew: 0
                ,
                events: []
            };
        }
        node.data = data;
        return node;
    }
}
