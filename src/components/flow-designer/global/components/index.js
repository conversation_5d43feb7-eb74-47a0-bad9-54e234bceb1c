import XIcon from './Icon/icon'
import XTooltip from './Tooltip/toolTip'
import XDivider from './Divider/divider'

const obj = {
    // 自定义组件
    XIcon,
    XTooltip,
    XDivider,
}

const components = {}
components.install = function (Vue) {
    for (let name in obj) {
        if (name && obj[name]) {
            Vue.component(name, obj[name])
            if (['Message', 'Modal'].includes(name)) {
                Vue.prototype[`$${name}`] = obj[name]
            }
        }
    }
}

export default components
