/**
 * 历史查看时节点信息
 */

export default {
    name: 'jpdl-history-view',
    options: {
        getDefaultCfg() {
            return {
                config: {
                }
            }
        },
        getEvents() {
            return {
                'node:click': 'onNodeClick'
            }
        },
        onNodeClick(event){
            if(!event.item.getModel().historyInfo){
                return
            }
            this.graph.emit('view:showHistory', event.item)
        }
    }
}
