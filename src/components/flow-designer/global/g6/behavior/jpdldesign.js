/**
 * 综合节点控制交互
 */

import config from '../config'
import utils from '../utils'
import pointutil from '../../utils/flowUtils'

export default {
    name: 'jpdl-design',
    options: {
        getDefaultCfg() {
            return {
                config: {
                    // 是否在拖拽节点时更新所有与之相连的边
                    updateEdge: true,
                    // 是否支持在节点上添加文本
                    nodeLabel: true,
                    // 是否支持在边上添加文本
                    edgeLabel: true,
                    // tooltip 是否启用
                    tooltip: {
                        dragNode: true,
                        dragEdge: true
                    },
                    // 是否启用对齐线
                    alignLine: {
                        enable: true,
                        style: {
                            stroke: '#FA8C16',
                            lineWidth: 1
                        }
                    }
                }
            }
        },
        getEvents() {
            return {
                'editor:addNode': 'startAddNode',
                'node:mousedown': 'onNodeMousedown',
                'node:mouseup': 'onNodeMouseup',
                'node:contextmenu': 'onNodeContextmenu',

                'edge:dragstart': 'onEdgeDragStart',
                'edge:drag': 'onMousemove',
                'edge:dragend': 'onMouseup',
                'edge:contextmenu': 'onEdgeContextmenu',

                'canvas:mouseup': 'onCanvasMouseup',
                'canvas:mouseenter': 'onCanvasMouseenter',
                'canvas:mouseleave': 'onCanvasMouseleave',
                'canvas:contextmenu': 'onCanvasContextmenu',

                'mousemove': 'onMousemove',
                'mouseup': 'onMouseup'
            }
        },
        startAddNode(node) {
            let _t = this
            // 初始化数据
            _t.info = {
                type: 'dragNode',
                node: node,
                target: null
            }
            _t.dragNode.status = 'dragNodeToEditor'
        },
        onNodeMousedown(event) {
            if (event.originalEvent.button !== 0) { // 非左键忽略
                return
            }
            let _t = this
            let model = event.item.getModel()
            _t.graph.emit('editor:getItem', [
                {
                    type: 'node',
                    id: model.id,
                    model: model
                }
            ])
            // 初始化数据
            _t.info = {
                type: null,
                node: event.item,
                target: event.target
            }
            if (_t.info.target && _t.info.target.attr('name')) {
                switch (_t.info.target.attr('name')) {
                    case 'anchorPoint':
                        _t.info.type = 'drawLine'
                        break
                }
            } else {
                _t.info.type = 'dragNode'
            }
            if (_t.info && _t.info.type && _t[_t.info.type].start) {
                _t[_t.info.type].start.call(_t, event)
            }
        },
        onNodeMouseup(event) {
            let _t = this
            if (_t.info && _t.info.type && _t[_t.info.type].stop) {
                _t[_t.info.type].stop.call(_t, event)
            }
        },
        onNodeContextmenu(event) {
            let _t = this
            _t.graph.emit('editor:contextmenu', {
                type: 'node',
                x: event.clientX,
                y: event.clientY,
                canvasX: event.canvasX,
                canvasY: event.canvasY
            })
            event.preventDefault();
        },
        onEdgeDragStart(event) {
            if (event.originalEvent.button !== 0) { // 非左键忽略
                return
            }
            let _t = this
            // console.log(event.item)
            let item = event.item
            // 初始化数据
            let points = []
            points.push(item._cfg.originStyle['path-shape'].startPoint)
            if (item.getModel().data.pointInfos.length > 2) {
                for (let index = 1; index < item.getModel().data.pointInfos.length - 1; index++) {
                    points.push(item.getModel().data.pointInfos[index])
                }
            }
            points.push(item._cfg.originStyle['path-shape'].endPoint)
            let insertIndex = pointutil.calInsertIndex({x: event.x, y: event.y}, points)
            utils.edge.setState('active', false, event.item)
            if (insertIndex < 0) {
                return
            } else {
                //找出点是否在 折线点距离15范围内，如果是则拖动该点
                let dragPointIndex = pointutil.findDistanceSmaller({x: event.x, y: event.y}, points, 15)
                if (dragPointIndex > -1) {
                    item.getModel().data.dragByPoint = true
                    item.getModel().data.dragPointIndex = dragPointIndex
                } else {
                    item.getModel().data.insertIndex = insertIndex
                }
            }
            _t.info = {
                type: 'dragEdge',
                edge: event.item
            }
            console.log(_t.info)
            if (_t.info && _t.info.type && _t[_t.info.type].start) {
                _t[_t.info.type].start.call(_t, event)
            }
            console.log(event)
        },
        onEdgeContextmenu(event) {
            let _t = this
            _t.graph.emit('editor:contextmenu', {
                type: 'edge',
                x: event.clientX,
                y: event.clientY,
                canvasX: event.canvasX,
                canvasY: event.canvasY
            })
            event.preventDefault();
        },
        onCanvasMouseenter(event) {
            let _t = this
            if (_t.info && _t.info.type === 'dragNode') {
                _t[_t.info.type].createDottedNode.call(_t, event)
            }
        },
        onCanvasMouseleave(event) {
            let _t = this
            if (_t.info && _t.info.type === 'dragNode') {
                _t[_t.info.type].stop.call(_t, event)
            }
        },
        onCanvasContextmenu(event) {
            let _t = this
            _t.graph.emit('editor:contextmenu', {
                type: 'canvas',
                x: event.clientX,
                y: event.clientY,
                canvasX: event.canvasX,
                canvasY: event.canvasY
            })
            event.preventDefault();
        },
        onCanvasMouseup(event) {
            let _t = this
            if (_t.info && _t.info.type && _t[_t.info.type].stop) {
                _t[_t.info.type].stop.call(_t, event)
            }
        },
        onMousemove(event) {
            let _t = this
            if (_t.info && _t.info.type && _t[_t.info.type].move) {
                _t[_t.info.type].move.call(_t, event)
            }
        },
        onMouseup(event) {
            let _t = this
            if (_t.info) {
                if (_t.info.type === 'dragNode') {
                    if (_t.dragNode.status === 'dragNodeToEditor') {
                        _t[_t.info.type].createNode.call(_t, event)
                    }
                }
                if (_t.info.type && _t[_t.info.type].stop) {
                    _t[_t.info.type].stop.call(_t, event)
                }
            }
        },
        // 拖拽画线
        drawLine: {
            isMoving: false,
            currentLine: null,
            start(event) {
                let _t = this
                let sourceAnchor
                let startModel = _t.info.node.getModel()
                // 锚点数据
                let anchorPoints = _t.info.node.getAnchorPoints()
                // 处理线条目标点
                if (anchorPoints && anchorPoints.length) {
                    // 获取距离指定坐标最近的一个锚点
                    sourceAnchor = _t.info.node.getLinkPoint({x: event.x, y: event.y})
                }
                _t.drawLine.currentLine = _t.graph.addItem('edge', {
                    id: utils.uniqueId(),
                    // 起始节点
                    source: startModel.id,
                    sourceAnchor: sourceAnchor ? sourceAnchor.anchorIndex : '',
                    // 终止节点/位置
                    target: {
                        x: event.x,
                        y: event.y
                    },
                    // FIXME label 需支持双击编辑
                    label: '',
                    labelCfg: {
                        position: 'middle',
                        autoRotate: true,
                        style: {
                            fontSize: 12,
                            fill: '#f52816'
                        }
                    },
                    attrs: {},
                    style: {
                        stroke: _t.graph.jpdl.lineColor,
                        lineWidth: _t.graph.jpdl.lineWidth,
                        ...config.edge.style.default,
                        ...config.edge.type[_t.graph.jpdl.lineDash]
                    },
                    // FIXME 边的形式需要与工具栏联动
                    type: _t.graph.jpdl.lineType || 'line',
                    startArrow: _t.graph.jpdl.startArrow || false,
                    endArrow: _t.graph.jpdl.endArrow || false
                })
                if (_t.config.tooltip.dragEdge) {
                    _t.toolTip.create.call(_t, {
                        left: event.canvasX,
                        top: event.canvasY + 10
                    }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}`)
                }
                _t.drawLine.isMoving = true
            },
            move(event) {
                let _t = this
                if (_t.drawLine.isMoving && _t.drawLine.currentLine) {
                    _t.graph.updateItem(_t.drawLine.currentLine, {
                        target: {
                            x: event.x,
                            y: event.y
                        }
                    })
                    if (_t.config.tooltip.dragEdge) {
                        _t.toolTip.update.call(_t, {
                            left: event.canvasX,
                            top: event.canvasY + 10
                        }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}`)
                    }
                }
            },
            stop(event) {
                let _t = this
                if (_t.drawLine.isMoving) {
                    if (_t.drawLine.currentLine === event.item) {
                        // 画线过程中点击则移除当前画线
                        _t.graph.removeItem(event.item)
                    } else {
                        let endNode = event.item
                        let startModel = _t.info.node.getModel()
                        let endModel = endNode.getModel()
                        let edgeModel = _t.drawLine.currentLine.getModel()
                        let targetAnchor
                        // 锚点数据
                        let anchorPoints = endNode.getAnchorPoints()
                        // 处理线条目标点
                        if (anchorPoints && anchorPoints.length) {
                            // 获取距离指定坐标最近的一个锚点
                            targetAnchor = endNode.getLinkPoint({x: event.x, y: event.y})
                        }
                        let data = {
                            content: '',
                            g: '',
                            descflag: 'false',
                            description: '',
                            name: '',
                            from: startModel.id,
                            to: endModel.id,
                            pointInfos: [_t.info.node.getAnchorPoints()[edgeModel.sourceAnchor - 1], targetAnchor]
                        }, contentFalg = false;
                        _t.graph.getEdges().forEach(edge => {
                            let model = edge.getModel()
                            if (edge.getSource().getID() === startModel.id && model.id !== edgeModel.id && startModel.id.indexOf("Fork")<0) {
                                contentFalg = true;
                                if (model.data.content === '') {
                                    _t.graph.updateItem(edge, {
                                        label: '请输入条件'
                                    })
                                }
                            }
                        })
                        _t.graph.updateItem(_t.drawLine.currentLine, {
                            target: endModel.id,
                            label: contentFalg ? '请输入条件' : '',
                            targetAnchor: targetAnchor ? targetAnchor.anchorIndex : '',
                            // 存储起始点ID，用于拖拽节点时更新线条
                            // attrs: {
                            //     start: startModel.id,
                            //     end: endModel.id
                            // },
                            data: data
                        })
                        console.log(_t.drawLine.currentLine)
                        // 记录操作日志
                        _t.graph.emit('editor:record', 'drawLine stop')
                    }
                }
                if (_t.config.tooltip.dragEdge) {
                    _t.toolTip.destroy.call(_t)
                }
                _t.drawLine.currentLine = null
                _t.drawLine.isMoving = false
                _t.info = null
            }
        },
        // 拖拽节点
        dragNode: {
            dottedNode: null,
            status: null,
            // 虚线框节点样式
            dottedNodeStyle: {
                ...config.dottedNode.style.default
            },
            createDottedNode(event) {
                let _t = this
                if (!_t.dragNode.dottedNode && _t.info.node) {
                    let {width, height} = _t.info.node
                    height = height - 14
                    let group = _t.graph.get('group')
                    _t.dragNode.dottedNode = group.addShape('rect', {
                        attrs: {
                            ..._t.dragNode.dottedNodeStyle,
                            width,
                            height,
                            x: event.x - width / 2,
                            y: event.y - height / 2
                        }
                    })
                    _t.info.startTime = new Date().getTime()
                    _t.graph.paint()
                    if (_t.config.tooltip.dragNode) {
                        _t.toolTip.create.call(_t, {
                            left: event.canvasX,
                            top: event.canvasY + height / 2
                        }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}<br>W: ${width.toFixed(2)} H: ${height.toFixed(2)}`)
                    }
                }
            },
            createNode(event) {
                let _t = this
                console.log(this)
                if (_t.dragNode.dottedNode && _t.info.node) {
                    let {width, height, minWidth, minHeight} = _t.info.node
                    let node = {
                        ..._t.info.node,
                        id: _t.info.node.id || utils.uniqueId(),
                        x: event.x,
                        y: event.y,
                        size: [width, height],
                        minWidth: minWidth,
                        minHeight: minHeight,
                        style: {
                            fill: _t.graph.jpdl.fill,
                            fillOpacity: _t.graph.jpdl.fillOpacity,
                            stroke: _t.graph.jpdl.lineColor,
                            strokeOpacity: _t.graph.jpdl.strokeOpacity,
                            lineWidth: _t.graph.jpdl.lineWidth,
                            ...config.edge.type[_t.graph.jpdl.lineDash]
                        }
                    }
                    _t.graph.addItem('node', node)
                }
            },
            start(event) {
                let _t = this
                // _t.dragNode.createDottedNode.call(_t, event)
                if (_t.config.tooltip.dragNode) {
                    let {width, height} = _t.info.node.getModel()
                    _t.toolTip.create.call(_t, {
                        left: event.canvasX,
                        top: event.canvasY + height / 2
                    }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}<br>W: ${width.toFixed(2)} H: ${height.toFixed(2)}`)
                }
                _t.dragNode.status = 'dragNode'
                _t.info.startTime = new Date().getTime()
                _t.info.loged = false;
                if (_t.config.alignLine.enable) {
                    // 绘制对齐线
                    _t.alignLine.start.call(_t)
                }
            },
            move(event) {
                let _t = this
                if (_t.dragNode.status === 'dragNodeToEditor') {
                    if (_t.dragNode.dottedNode && _t.info.node) {
                        let {width, height} = _t.info.node
                        _t.dragNode.dottedNode.attr({
                            x: event.x - width / 2,
                            y: event.y - height / 2
                        })
                        _t.graph.paint()
                        if (_t.config.tooltip.dragNode) {
                            _t.toolTip.update.call(_t, {
                                left: event.canvasX,
                                top: event.canvasY + height / 2
                            }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}<br>W: ${width.toFixed(2)} H: ${height.toFixed(2)}`)
                        }
                        if (_t.config.alignLine.enable) {
                            // 绘制对齐线
                            _t.alignLine.move.call(_t, _t.dragNode.dottedNode)
                        }
                    }
                } else if (_t.dragNode.status === 'dragNode') {
                    if (_t.info.node) {
                        if ((new Date().getTime() - _t.info.startTime) < 100) {
                            return
                        }
                        let {id, groupId, x, y} = _t.info.node.getModel()
                        _t.graph.find('node', node => {
                            let model = node.getModel()
                            // 更新当节点
                            if (model.id === id) {
                                let attrs = {
                                    x: event.x,
                                    y: event.y
                                }
                                // 更新节点
                                _t.graph.updateItem(_t.info.node, attrs)
                                _t.graph.paint()
                                if (_t.config.updateEdge) {
                                    // 更新边
                                    utils.edge.update(_t.info.node, _t.graph)
                                }
                                if (_t.config.tooltip.dragNode) {
                                    let {width, height} = _t.info.node.getModel()
                                    _t.toolTip.update.call(_t, {
                                        left: event.canvasX,
                                        top: event.canvasY + height / 2
                                    }, `X: ${event.x.toFixed(2)} Y: ${event.y.toFixed(2)}<br>W: ${width.toFixed(2)} H: ${height.toFixed(2)}`)
                                }
                            } else {
                                if (groupId && model.groupId && model.groupId === groupId) {
                                    let model = node.getModel()
                                    // 更新同组节点
                                    _t.graph.updateItem(node, {
                                        x: model.x + event.x - x,
                                        y: model.y + event.y - y
                                    })
                                }
                            }
                        })
                        if (_t.config.alignLine.enable) {
                            // 绘制对齐线
                            _t.alignLine.move.call(_t, _t.info.node)
                        }
                    }
                }
            },
            stop(event) {
                let _t = this
                // 记录操作日志
                if ((new Date().getTime() - _t.info.startTime) > 100) {
                    //记录状态
                    if (!_t.info.loged) {
                        _t.graph.emit('editor:record', 'dragNode')
                        _t.info.loged = true;
                    }
                }
                // _t.graph.emit('editor:record', 'dragNode stop')
                _t.dragNode.clear.call(_t)
                if (_t.config.tooltip.dragNode) {
                    _t.toolTip.destroy.call(_t)
                }
                if (_t.config.alignLine.enable) {
                    _t.alignLine.stop.call(_t)
                }
                _t.graph.paint()
            },
            clear() {
                let _t = this
                if (_t.dragNode.dottedNode) {
                    _t.dragNode.dottedNode.remove()
                    _t.dragNode.dottedNode = null
                }
                _t.dragNode.status = null
                _t.info = null
            }
        },
        //边拖拽折线
        dragEdge: {
            start(event) {
                // console.log('start')
                let _t = this
                _t.info.edge._cfg.model.data.dragStatus = 'click'
                _t.info.edge._cfg.model.data.startTime = new Date().getTime()
                _t.info.loged = false;
            },
            move(event) {
                // console.log('move')
                let _t = this
                if ((new Date().getTime() - _t.info.edge._cfg.model.data.startTime) > 100) {
                    //记录状态
                    _t.info.edge._cfg.model.data.dragPoint = {x: event.x, y: event.y}
                    _t.info.edge._cfg.model.data.dragStatus = 'move'
                    _t.graph.updateItem(_t.info.edge, {})
                    console.log("dragEdge moving")
                }
            },
            stop(event) {
                // console.log('stop');
                let _t = this
                // console.log(_t.info.edge)
                _t.info.edge._cfg.model.data.dragStatus = 'end'
                if ((new Date().getTime() - _t.info.edge._cfg.model.data.startTime) > 100) {
                    _t.info.edge._cfg.model.data.dragPoint = {x: event.x, y: event.y}
                    _t.graph.updateItem(_t.info.edge, {})
                    _t.info.edge._cfg.model.data.dragStatus = undefined
                    _t.info.edge._cfg.model.data.insertIndex = -1
                    _t.info.edge._cfg.model.data.dragPoint = undefined
                    _t.info.edge._cfg.model.data.dragByPoint = undefined
                    _t.info.edge._cfg.model.data.dragPointIndex = undefined
                    utils.edge.updateEdgeAnchor(_t.graph, _t.info.edge)
                    _t.graph.emit('editor:record', 'dragline log')
                    utils.edge.setState('active', false, _t.info.edge)
                }
                _t.info.edge._cfg.model.data.startTime = undefined
                _t.info = null
                _t.graph.paint()

            }
        },
        // 提示
        toolTip: {
            currentTip: null,
            create(position, content) {
                let _t = this
                if (_t.toolTip.currentTip) {
                    console.warn('Editor Warn:: can\'t creat tootip when currentTip not null!')
                    return
                }
                let canvas = _t.graph.get('canvas')
                const el = canvas.get('el')
                _t.toolTip.currentTip = utils.createDom(`<div class="tooltip">${content}</div>`)
                if (_t.toolTip.currentTip) {
                    // 插入输入框dom
                    el.parentNode.appendChild(_t.toolTip.currentTip)
                    // 更新输入框样式
                    utils.modifyCSS(_t.toolTip.currentTip, {
                        display: 'inline-block',
                        position: 'absolute',
                        left: position.left + 'px',
                        top: position.top + 'px',
                        padding: '5px 10px',
                        width: '160px',
                        marginTop: '10px',
                        marginLeft: '-80px',
                        background: '#F2F2F2',
                        color: '#444444',
                        border: '1px solid #D1D1D1',
                        textAlign: 'center',
                        overflow: 'hidden',
                        fontSize: '14px'
                    })
                }
            },
            update(position, content) {
                let _t = this
                if (_t.toolTip.currentTip) {
                    // 更新文本
                    _t.toolTip.currentTip.innerHTML = content
                    // 更新输入框样式
                    utils.modifyCSS(_t.toolTip.currentTip, {
                        left: position.left + 'px',
                        top: position.top + 'px'
                    })
                }
            },
            destroy() {
                let _t = this
                if (_t.toolTip.currentTip) {
                    let canvas = _t.graph.get('canvas')
                    const el = canvas.get('el')
                    // 删除输入框dom
                    el.parentNode.removeChild(_t.toolTip.currentTip)
                    _t.toolTip.currentTip = null
                }
            }
        },
        alignLine: {
            // 对齐线列表
            lineList: [],
            // 最大距离
            maxDistance: 2,
            start() {
                let _t = this
                _t.alignLine._clear.call(_t)
            },
            _clear() {
                let _t = this
                _t.alignLine.lineList.forEach(line => {
                    line.remove()
                })
                _t.alignLine.lineList = []
                _t.graph.paint()
            },
            move(item) {
                let _t = this
                // 先清空已有对齐线
                _t.alignLine._clear.call(_t)
                const bbox = item.getBBox()
                // FIXME bbox 中x、y坐标为图形左上角坐标
                // 中上
                const ct = {x: bbox.x + bbox.width / 2, y: bbox.y}
                // 中心
                const cc = {x: bbox.x + bbox.width / 2, y: bbox.y + bbox.height / 2}
                // 中下
                const cb = {x: bbox.x + bbox.width / 2, y: bbox.y + bbox.height}
                // 左中
                const lc = {x: bbox.x, y: bbox.y + bbox.height / 2}
                // 右中
                const rc = {x: bbox.x + bbox.width, y: bbox.y + bbox.height / 2}
                // 计算距离
                const getDistance = function (line, point) {
                    // 归一向量
                    function normalize(out, a) {
                        let x = a[0]
                        let y = a[1]
                        let len = x * x + y * y
                        if (len > 0) {
                            // TODO: evaluate use of glm_invsqrt here?
                            len = 1 / Math.sqrt(len)
                            out[0] = a[0] * len
                            out[1] = a[1] * len
                        }
                        return out
                    }

                    function dot(a, b) {
                        return a[0] * b[0] + a[1] * b[1]
                    }

                    const pointLineDistance = function (lineX1, lineY1, lineX2, lineY2, pointX, pointY) {
                        const lineLength = [lineX2 - lineX1, lineY2 - lineY1]
                        if (lineLength[0] === 0 && lineLength[1] === 0) {
                            return NaN
                        }
                        let s = [-lineLength[1], lineLength[0]]
                        normalize(s, s)
                        return Math.abs(dot([pointX - lineX1, pointY - lineY1], s))
                    }
                    return {
                        line,
                        point,
                        dis: pointLineDistance(line[0], line[1], line[2], line[3], point.x, point.y)
                    }
                }
                // 遍历节点
                const nodes = _t.graph.getNodes()
                nodes.forEach(node => {
                    let horizontalLines = []
                    let verticalLines = []
                    // 对齐线信息
                    let info = {
                        horizontals: [],
                        verticals: []
                    }
                    const bbox1 = node.getBBox()
                    // 水平线
                    let horizontalInfo = [
                        // 左上 右上 tltr
                        [bbox1.minX, bbox1.minY, bbox1.maxX, bbox1.minY],
                        // 左中 右中 lcrc
                        [bbox1.minX, bbox1.centerY, bbox1.maxX, bbox1.centerY],
                        // 左下 右下 blbr
                        [bbox1.minX, bbox1.maxY, bbox1.maxX, bbox1.maxY]
                    ]
                    // 垂直线
                    let verticalInfo = [
                        // 左上 左下 tlbl
                        [bbox1.minX, bbox1.minY, bbox1.minX, bbox1.maxY],
                        // 上中 下中 tcbc
                        [bbox1.centerX, bbox1.minY, bbox1.centerX, bbox1.maxY],
                        // 上右 下右 trbr
                        [bbox1.maxX, bbox1.minY, bbox1.maxX, bbox1.maxY]
                    ]
                    horizontalInfo.forEach(line => {
                        horizontalLines.push(getDistance(line, ct))
                        horizontalLines.push(getDistance(line, cc))
                        horizontalLines.push(getDistance(line, cb))
                    })
                    verticalInfo.forEach(line => {
                        verticalLines.push(getDistance(line, lc))
                        verticalLines.push(getDistance(line, cc))
                        verticalLines.push(getDistance(line, rc))
                    })
                    horizontalLines.sort((a, b) => a.dis - b.dis)
                    verticalLines.sort((a, b) => a.dis - b.dis)
                    // 过滤掉距离为0的线条
                    horizontalLines = horizontalLines.filter(item => item.dis !== 0)
                    if (horizontalLines.length && horizontalLines[0].dis < _t.alignLine.maxDistance) {
                        // 取前3个距离相等的线条
                        for (let i = 0; i < 3; i++) {
                            if (horizontalLines[0].dis === horizontalLines[i].dis) {
                                info.horizontals.push(horizontalLines[i])
                            }
                        }
                    }
                    // 过滤掉距离为0的线条
                    verticalLines = verticalLines.filter(item => item.dis !== 0)
                    if (verticalLines.length && verticalLines[0].dis < _t.alignLine.maxDistance) {
                        // 取前3个距离相等的线条
                        for (let i = 0; i < 3; i++) {
                            if (verticalLines[0].dis === verticalLines[i].dis) {
                                info.verticals.push(verticalLines[i])
                            }
                        }
                    }
                    // 添加对齐线
                    const group = _t.graph.get('group')
                    // 对齐线样式
                    const lineStyle = _t.config.alignLine.style
                    // 处理水平线
                    if (info.horizontals.length) {
                        info.horizontals.forEach(lineObj => {
                            let line = lineObj.line
                            let point = lineObj.point
                            let lineHalf = (line[0] + line[2]) / 2
                            let x1
                            let x2
                            if (point.x < lineHalf) {
                                x1 = point.x - bbox.width / 2
                                x2 = Math.max(line[0], line[2])
                            } else {
                                x1 = point.x + bbox.width / 2
                                x2 = Math.min(line[0], line[2])
                            }
                            let shape = group.addShape('line', {
                                attrs: {
                                    x1,
                                    y1: line[1],
                                    x2,
                                    y2: line[1],
                                    ...lineStyle
                                },
                                // 是否拾取及触发该元素的交互事件
                                capture: false
                            })
                            _t.alignLine.lineList.push(shape)
                        })
                    }
                    // 处理垂直线
                    if (info.verticals.length) {
                        info.verticals.forEach(lineObj => {
                            let line = lineObj.line
                            let point = lineObj.point
                            let lineHalf = (line[1] + line[3]) / 2
                            let y1
                            let y2
                            if (point.y < lineHalf) {
                                y1 = point.y - bbox.height / 2
                                y2 = Math.max(line[1], line[3])
                            } else {
                                y1 = point.y + bbox.height / 2
                                y2 = Math.min(line[1], line[3])
                            }
                            let shape = group.addShape('line', {
                                attrs: {
                                    x1: line[0],
                                    y1,
                                    x2: line[0],
                                    y2,
                                    ...lineStyle
                                },
                                capture: false
                            })
                            _t.alignLine.lineList.push(shape)
                        })
                    }
                })
            },
            stop() {
                let _t = this
                _t.alignLine._clear.call(_t)
            }
        }
    }
}
