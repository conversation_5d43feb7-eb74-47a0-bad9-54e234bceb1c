import {Global} from '@antv/g6'
import base from '../base'

export default {
    name: 'jpdl_artific',
    extendName: 'single-shape',
    options: {
        ...base,
        shapeType: 'path',
        getShapeStyle(cfg) {
            const size = this.getSize(cfg)
            const width = size[0]
            const height = size[1]
            const x = 0 - width / 2
            const y = 0 - height / 2
            const path = [
                // 左顶点
                ['M', -width / 2, 0],
                // 左上顶点
                ['L', -width / 2, -height / 2],
                // 右上顶点
                ['L', width / 2, -height / 2],
                // 右下顶点
                ['L', width / 2, height / 2],
                // 左下顶点
                ['L', -width / 2, height / 2],
                ['Z']
            ]
            const color = cfg.color || Global.defaultNode.color
            const style = Object.assign({}, Global.defaultNode.style, {
                // 节点的位置在上层确定，所以这里仅使用相对位置即可
                x,
                y,
                width,
                height,
                path,
                stroke: color
            }, cfg.style)
            style.strokeOpacity = 0
            style.fillOpacity = 0

            return style
        }
    }
}
