import jpdlStart from './jpdl/jpdl_start'
import jpdlEnd from './jpdl/jpdl_end'
import jpdlArtific from './jpdl/jpdl_artific'
import jpdlAuto from './jpdl/jpdl_auto'
import jpdlRule from './jpdl/jpdl_rule'
import jpdlSubPro from './jpdl/jpdl_sub_pro'
import jpdlWait from './jpdl/jpdl_wait'
import jpdlTimer from './jpdl/jpdl_timer'
import jpdlVirtual from './jpdl/jpdl_virtual'
import jpdlParlbran from './jpdl/jpdl_parlbran'
import jpdlParljoin from './jpdl/jpdl_parljoin'
import jpdlDecision from './jpdl/jpdl_decision'

const obj = {
    jpdlStart,
    jpdlEnd,
    jpdlArtific,
    jpdlAuto,
    jpdlRule,
    jpdlSubPro,
    jpdlWait,
    jpdlTimer,
    jpdlVirtual,
    jpdlParlbran,
    jpdlParljoin,
    jpdlDecision
}

export default function (G6) {
    Object.values(obj).map(item => {
        G6.registerNode(item.name, item.options, item.extendName)
    })
}
