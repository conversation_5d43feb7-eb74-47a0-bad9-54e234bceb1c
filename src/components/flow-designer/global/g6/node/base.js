/**
 * 节点基础方法
 */
import utils from '../utils'

export default {
    shape: null,
    drawShape(cfg, group) {
        const shapeType = this.shapeType
        const style = this.getShapeStyle(cfg)
        const shape = group.addShape(shapeType, {
            attrs: style,
            draggable: true,
            capture: true,
            name: 'jpdl-shape'
        })
        this.shape = shape
        return shape
    },
    setState(name, value, item) {
        //设置节点stroke
        if (name === 'selected') {
            let group = item.getContainer()
            let node = group.get('children')[0]
            if (value) {
                node.attr('stroke', '#29B6F2');
                node.attr('strokeOpacity', 1);
                node.attr('lineWidth', 3);
                node.attr('lineDash', [2]);
            } else {
                node.attr('stroke', '#29B6F2');
                node.attr('strokeOpacity', 0);
                node.attr('lineWidth', 0);
                node.attr('lineDash', []);
            }

        }
        // 设置锚点状态
        utils.anchor.setState(name, value, item)
        utils.nodeback.setState(name, value, item);
    },
    // 绘制后附加,图片锚点及
    afterDraw(cfg, group) {
        utils.drawImage(cfg, group)
        // 绘制锚点
        utils.anchor.draw(cfg, group)

        utils.nodeback.draw(cfg, group);
    }
}
