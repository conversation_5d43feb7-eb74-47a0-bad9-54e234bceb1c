/**
 * 折线
 */
import base from './base'

export default {
    name: 'base-line',
    extendName: 'single-edge',
    options: {
        ...base,
        drawShape(cfg, group) {
            const {startPoint, endPoint} = cfg
            const controlPoints = this.getControlPoints(cfg)
            let points = [startPoint]
            if (controlPoints) {
                points.push(...controlPoints)
            }
            points.push(endPoint)
            let path = this.getPath(points)
            const keyShape = group.addShape('path', {
                className: 'edge-shape',
                sourceAnchor: 1,
                targetAnchor: 2,
                capture: true,
                attrs: {
                    ...cfg,
                    endArrow: {
                        path: 'M 0,0 L 6,3 L 6,-3 Z',  // 自定义箭头为中心点在(0, 0)，指向 x 轴正方向的path
                        d: 0,
                        fill: '#111111',
                        stroke: '#100f0f',
                        opacity: 1,
                    },
                    cursor: 'pointer',
                    stroke: '#000000',
                    strokeOpacity: 1,
                    draggable: true,
                    // 扩展响应范围
                    lineAppendWidth: 10,
                    path: path,
                },
                name: 'path-shape',
            })
            // console.log(group)
            return keyShape
        },
        getPath(points) {
            const path = []
            for (let i = 0; i < points.length; i++) {
                const point = points[i]
                if (!point || !point.x || !point.y) {
                    continue
                }
                if (i === 0) {
                    path.push(['M', point.x, point.y])
                } else {
                    path.push(['L', point.x, point.y])
                }
            }
            return path
        },
        getControlPoints(cfg) {
            let points = [];
            // console.log(cfg.data)
            if (cfg.data) {
                const {startPoint, endPoint} = cfg
                cfg.data.pointInfos[0] = {x: startPoint.x, y: startPoint.y};
                cfg.data.pointInfos[cfg.data.pointInfos.length - 1] = {x: endPoint.x, y: endPoint.y};
                if (cfg.data.dragStatus && cfg.data.dragStatus === 'move') {
                    if (cfg.data.dragByPoint) {
                        if (cfg.data.pointInfos.length > 2) {
                            for (let index = 1; index < cfg.data.pointInfos.length - 1; index++) {
                                if (index === cfg.data.dragPointIndex) {
                                    points.push(cfg.data.dragPoint);
                                } else {
                                    points.push(cfg.data.pointInfos[index]);
                                }
                            }
                        }
                    } else {
                        if (cfg.data.pointInfos.length > 2) {
                            // this.calIndex(cfg);
                            if (cfg.data.insertIndex < 0) return;
                            for (let index = 1; index < cfg.data.pointInfos.length - 1; index++) {
                                points.push(cfg.data.pointInfos[index]);
                            }
                            points.splice(cfg.data.insertIndex, 0, cfg.data.dragPoint);
                        } else {
                            points.push(cfg.data.dragPoint);
                        }
                    }
                } else if (cfg.data.dragStatus && cfg.data.dragStatus === 'end') {
                    console.log('end')
                    if (cfg.data.dragByPoint) {
                        if (cfg.data.pointInfos.length > 2) {
                            for (let index = 1; index < cfg.data.pointInfos.length - 1; index++) {
                                if (index === cfg.data.dragPointIndex) {
                                    points.push(cfg.data.dragPoint);
                                } else {
                                    points.push(cfg.data.pointInfos[index]);
                                }
                            }
                            cfg.data.pointInfos[cfg.data.dragPointIndex] = cfg.data.dragPoint;
                        }
                    } else {
                        if (cfg.data.pointInfos.length > 2) {
                            // this.calIndex(cfg);
                            if (cfg.data.insertIndex < 0) return;
                            if (cfg.data.dragPoint) {
                                cfg.data.pointInfos.splice(cfg.data.insertIndex + 1, 0, cfg.data.dragPoint);
                            }
                            for (let index = 1; index < cfg.data.pointInfos.length - 1; index++) {
                                points.push(cfg.data.pointInfos[index]);
                            }
                        } else {
                            if (cfg.data.dragPoint) {
                                points.push(cfg.data.dragPoint);
                                cfg.data.pointInfos.splice(1, 0, cfg.data.dragPoint);
                            }
                        }
                    }
                } else if (cfg.data.pointInfos) {
                    if (cfg.data.pointInfos.length > 2) {
                        for (let index = 1; index < cfg.data.pointInfos.length - 1; index++) {
                            let _t = cfg.data.pointInfos[index];
                            points.push(_t);
                        }
                    }
                }
            }
            // console.log(points)
            return points;
        }
    }
}
