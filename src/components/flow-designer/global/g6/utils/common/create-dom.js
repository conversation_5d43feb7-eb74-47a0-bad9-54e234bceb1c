/**
 * 创建DOM 节点
 * @param  {String} str Dom 字符串
 * @return {HTMLElement}  DOM 节点
 */
let TABLE = document.createElement('table');
let TABLE_TR = document.createElement('tr');
let FRAGMENT_REG = /^\s*<(\w+|!)[^>]*>/;
let CONTAINERS = {
    tr: document.createElement('tbody'),
    tbody: TABLE,
    thead: TABLE,
    tfoot: TABLE,
    td: TABLE_TR,
    th: TABLE_TR,
    '*': document.createElement('div')
};

export default function createDom(str) {
    let name = FRAGMENT_REG.test(str) && RegExp.$1;
    if (!(name in CONTAINERS)) {
        name = '*';
    }
    let container = CONTAINERS[name];
    str = str.replace(/(^\s*)|(\s*$)/g, '');
    container.innerHTML = '' + str;
    let dom = container.childNodes[0];
    container.removeChild(dom);
    return dom;
};
