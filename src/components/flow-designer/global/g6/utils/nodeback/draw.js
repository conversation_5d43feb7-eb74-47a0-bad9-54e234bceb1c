/**
 *
 *
 * 绘制锚点
 */
export default function (cfg, group) {
    let r = cfg.size[0];
    // group.addShape('circle',{
    //     zIndex: -1,
    //     size:r,
    //     attrs: {
    //         name:'circle_back',
    //         x: 0,
    //         y: -6,
    //         r:r*0.7+2,
    //         stroke: "#1a9dfb",
    //         lineWidth: 5,
    //         opacity: 0,
    //         strokeOpacity: 0.9,
    //     }
    // }).hide();
    group.addShape('rect', {
        zIndex: -2,
        name: 'rect_back',
        attrs: {
            name: 'rect_back',
            x: -18,
            y: -25,
            width: 36,
            height: 36,
            stroke: "#fb411a",
            lineWidth: 2,
            opacity: 0,
            strokeOpacity: 0.9,
        }
    }).hide();
    // backCircle.animate({
    //     onFrame(ratio) {
    //         // 先变大、再变小
    //         const diff = ratio <=0.5 ? ratio * 5 : (1 - ratio) * 5;
    //         const strokeOpacity = ratio <=0.6 ? ratio : (1 - ratio);
    //         let radius = cfg.size;
    //         if (isNaN(radius)) radius = radius[0];
    //         // 返回这一帧需要变化的参数集，这里只包含了半径
    //         return {
    //             r: radius + diff,
    //             strokeOpacity:strokeOpacity ,
    //         }
    //     },
    //     repeat: true // 循环
    // }, 2000, 'easeCubic', null, 0) // 无延迟
}
