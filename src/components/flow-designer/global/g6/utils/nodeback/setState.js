/**
 * 设置背景状态
 */
export default function (name, value, item) {
    if (name === 'history') {
        let group = item.getContainer()
        let children = group.get('children')
        for (let i = 0, len = children.length; i < len; i++) {
            let child = children[i]
            if (child && child._attrs && child.attr('name') && child.attr('name') == 'rect_back') {
                if (value) {
                    child.show();
                } else {
                    child.hide();
                }
            }
        }
        // group.addShape('rect',{
        //   zIndex: -2,
        //   attrs: {
        //     name:'rect_back',
        //     x: -18,
        //     y: -25,
        //     width: 36,
        //     height: 36,
        //     stroke: "#fb411a",
        //     lineWidth: 2,
        //     opacity: 0,
        //     strokeOpacity: 0.9,
        //   }
        // })

    } else if (name === 'history_avtive') {
        let group = item.getContainer()
        let child =  group.addShape('circle',{
            zIndex: -1,
            size:32,
            attrs: {
                name:'circle_back',
                x: 0,
                y: -6,
                r:25,
                stroke: "#1a9dfb",
                lineWidth: 5,
                opacity: 0,
                strokeOpacity: 0.7,
            }
        });
        child.animate({
            onFrame(ratio) {
                // 先变大、再变小
                const diff = ratio <=0.5 ? ratio * 5 : (1 - ratio) * 5;
                const strokeOpacity = ratio <=0.7 ? ratio : ((1 - ratio)>0?((1 - ratio)):0.2);
                let radius = 25;
                if (isNaN(radius)) radius = radius[0];
                // 返回这一帧需要变化的参数集，这里只包含了半径
                return {
                    r: radius + diff,
                    strokeOpacity:strokeOpacity ,
                }
            },
            repeat: true // 循环
        }, 2500, 'easeCubic', null, 0) // 无延迟
    }
}
