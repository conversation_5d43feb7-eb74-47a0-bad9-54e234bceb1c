/**
 *
 *
 * 绘制锚点
 */

import config from '../../config'

export default function (cfg, group) {
    let {anchorPoints, width, height, id} = cfg
    // console.log(cfg)
    let shape = group.getFirst()
    // console.log('getAnchorPoints', id, shape)
    if (anchorPoints && anchorPoints.length) {
        for (let i = 0, len = anchorPoints.length; i < len; i++) {
            let anchorX
            let anchorY
            // if (shape && shape.get('type') === 'path') {
            //     let point = shape.getPoint((i + 1) / len)
            //     anchorX = point.x
            //     anchorY = point.y
            // } else {
            let [x, y] = anchorPoints[i]
            // 计算Marker中心点坐标
            let originX = -width / 2
            let originY = -height / 2
            anchorX = x * width + originX
            anchorY = y * height + originY
            // }
            // let flag = shape.isPointInPath(anchorX, anchorY)
            let flag = true
            // console.log('isPointInPath', anchorPoints[i], anchorX, anchorY, flag)
            // 添加锚点背景
            let anchorBgShape = group.addShape('circle', {
                id: id + '_anchor_bg_' + i,
                name: 'anchorBg',
                capture: false,
                attrs: {
                    boxName: 'anchor',
                    name: 'anchorBg',
                    x: anchorX,
                    y: anchorY,
                    // 锚点默认样式
                    ...config.anchorBg.style.default
                }
            })
            // 添加锚点Marker形状
            let anchorShape = group.addShape('circle', {
                id: id + '_anchor_' + i,
                name: 'anchorBg',
                capture: true,
                attrs: {
                    boxName: 'anchor',
                    name: 'anchorPoint',
                    x: anchorX,
                    y: anchorY,
                    // 锚点默认样式
                    ...config.anchor.style.default,
                    fill: flag ? '#1aa0ff' : config.anchor.style.default.fill
                }
            })
            //
            anchorShape.on('mouseenter', function () {
                if (anchorBgShape.attrs.history) {
                    return
                } else {
                    anchorBgShape.attr({
                        ...config.anchorBg.style.active
                    })
                }
            })
            anchorShape.on('mouseleave', function () {
                anchorBgShape.attr({
                    ...config.anchorBg.style.inactive
                })
            })
        }
    }
}
