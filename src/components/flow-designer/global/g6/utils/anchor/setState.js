/**
 * 设置锚点状态
 */

import config from '../../config'

export default function (name, value, item) {
    if (name === 'hover') {
        let group = item.getContainer()
        let children = group.get('children')
        for (let i = 0, len = children.length; i < len; i++) {
            let child = children[i]
            if (child.attrs && child.attr('name')) {
                switch (child.attr('name')) {
                    case 'anchorPoint':
                        if (!child.attrs.history) {
                            if (value) {
                                // console.log("show",child)
                                child.show()
                                child.attr(config.anchor.style.hover)
                            } else {
                                // console.log("hide",child)
                                child.attr(config.anchor.style.unhover)
                                child.hide()
                            }
                        }
                        break
                }
            }
        }
    } else if (name === 'hideAnvhor') {
        let group = item.getContainer()
        let children = group.get('children')
        for (let i = 0, len = children.length; i < len; i++) {
            let child = children[i]
            if (child.attrs && child.attr('name') && (child.attr('name') === 'anchorBg' || child.attr('name') === 'anchorPoint')) {
                child.attrs.history = true;
            }
        }
    }
}
