/**
 *
 * 设置边状态
 */

import config from '../../config'
// import drawAnimate from './drawAnimate'
// import destroyAnimate from './destroyAnimate'

export default function (name, value, item) {
    if (name === 'active') {
        let group = item.getContainer()
        let children = group.get('children')
        let edge = children[0]
        // 处理线条状态
        if (edge) {
            if (value) {
                edge.attr(config.edge.style.active)
            } else {
                edge.attr(config.edge.style.inactive)
            }
        }
    } else if (name === 'history') {
        let group = item.getContainer()
        let children = group.get('children')
        let edge = children[0]
        // 处理线条状态
        if (edge) {
            if (value) {
                edge.attr(config.edge.style.history)
            } else {
                edge.attr(config.edge.style.default)
            }
        }
    } else if (name === 'runActiveHis') {
        if (!value) {
            return
        }
        let group = item.getContainer()
        const shape = group.get('children')[0];
        // 边 path 的起点位置
        const startPoint = shape.getPoint(0);

        // console.log(item);
        let startId = item.getModel().source;
        // console.log('run')
        const circle = group.addShape('circle', {
            attrs: {
                x: startPoint.x,
                y: startPoint.y,
                fill: '#1fb5ff',
                r: 7
            }
        });
        value.editor.setItemState(value.editor.findById(startId), 'history', true)
        circle.animate({
            // 动画重复
            repeat: false,
            // 每一帧的操作，入参 ratio：这一帧的比例值（Number）。返回值：这一帧需要变化的参数集（Object）。
            onFrame(ratio) {
                const tmpPoint = shape.getPoint(ratio);
                // 返回需要变化的参数集，这里返回了位置 x 和 y
                if (value.hisRunStatus.status === 'pause' || value.hisRunStatus.status === 'stop') {
                    value.hisRunStatus.running = true;
                    return {
                        x: tmpPoint.x,
                        y: tmpPoint.y,
                        opacity: 0,
                    };
                } else {
                    return {
                        x: tmpPoint.x,
                        y: tmpPoint.y
                    };
                }
            }
        }, 2500, function () {
            circle.remove();
            item.clearStates('runActiveHis')
            if (value.hisRunStatus.status === 'pause') {
                console.log("pause run index",value.hisRunStatus.index)
                value.hisRunStatus.running = false;
                return
            }
            if (value.hisRunStatus.status === 'stop') {
                value.hisRunStatus.index = 0;
                value.hisRunStatus.running = false;
                return
            }
            value.hisRunStatus.index++;
            value.editor.setItemState(item, 'history', true);
            if (value.hisRunStatus.index < value.edges.length) {
                value.editor.setItemState(value.edges[value.hisRunStatus.index], 'runActiveHis', {
                    hisRunStatus: value.hisRunStatus,
                    editor: value.editor,
                    edges: value.edges,
                    self: value.self
                });
            } else {
                value.hisRunStatus.index = 0;
                value.hisRunStatus.status = 'finished'
                value.self.$Message.info('播放结束');
            }
        }, 0);
    }
}
