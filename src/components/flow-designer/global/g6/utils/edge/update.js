/**
 *
 * 更新边
 */
import updateEdgeAnchor from './updateEdgeAnchor'

export default function (node, graph) {
    let edges = graph.getEdges()
    if (!edges || !edges.length) {
        return
    }
    let {id} = node.getModel()
    // 遍历边
    edges.forEach(edge => {
        if (id === edge.getSource().getID() || id === edge.getTarget().getID()) {
            updateEdgeAnchor(graph, edge);
        }
    })
}
