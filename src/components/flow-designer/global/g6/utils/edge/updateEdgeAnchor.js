export default function (graph, edge) {
    let source = edge.getSource();
    let target = edge.getTarget();
    let points = edge.getModel().data.pointInfos;
    if (!source._cfg || !target._cfg || !points) {
        return
    }
    let startAnchor = source.getLinkPoint(points[1]);
    let endAnchor = target.getLinkPoint(points[points.length - 2]);
    console.log("edge start["+source.getID()+"] end["+target.getID()+"] startAnchor ["+startAnchor.anchorIndex+"] endAnchor [ "+endAnchor.anchorIndex+"]")
    graph.update(edge, {
        sourceAnchor: startAnchor.anchorIndex,
        targetAnchor: endAnchor.anchorIndex,
        // source: startAnchor,
        // target: endAnchor
    });
}
