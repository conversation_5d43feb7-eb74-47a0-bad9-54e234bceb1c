const images = {
    jpdl_start: require('../../../../assets/images/designer/start_model.png'),
    jpdl_end: require('../../../../assets/images/designer/end_model.png'),
    jpdl_artific: require('../../../../assets/images/designer/artific_model.png'),
    jpdl_auto: 'data:image/png;base64,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',
    jpdl_rule: require('../../../../assets/images/designer/rule_model.png'),
    sub_process: require('../../../../assets/images/designer/sub_model.png'),
    jpdl_wait: require('../../../../assets/images/designer/wait_model.png'),
    jpdl_timer: require('../../../../assets/images/designer/timer_model.png'),
    jpdl_virtual: require('../../../../assets/images/designer/virtual_model.png'),
    jpdl_parlbran: require('../../../../assets/images/designer/parlbran_model.png'),
    jpdl_parljoin: require('../../../../assets/images/designer/parljoin_model.png'),
    jpdl_decision: require('../../../../assets/images/designer/decision_model.png'),

}

export default function (cfg, group) {
    const size = cfg.size
    const width = size[0]
    const height = size[1]
    // 处理边框
    group.addShape('image', {
        attrs: {
            x: -(width) / 2,
            y: -height / 2,
            width: width,
            height: height - 14,
            img: images[cfg.type]
        }
    })
}
