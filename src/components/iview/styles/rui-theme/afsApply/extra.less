// 覆盖全局样式

.ivu-form-item {
    margin-bottom: 20px;
}
.ivu-form-item-error-tip {
    padding-top: 4px;
    // font-size: 14px;
}
// .ivu-select-small.ivu-select-single .ivu-select-selection{
//     height: 30px;
// }
// .ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-placeholder,
//  .ivu-select-small.ivu-select-single .ivu-select-selection .ivu-select-selected-value{
//     height: 30px;
//     line-height: 30px;
//     font-size: 14px;
//  }
// .ivu-form-item-content{
//      font-size: 14px;
//  }
//  .ivu-form .ivu-form-item-label{
//      font-size: 14px;
//  }
//  .ivu-radio-wrapper{
//     font-size: 14px;
//  }
 .ivu-input-wrapper,.ivu-select,.ivu-cascader{
     width: 200px;
 }
//  .ivu-cascader .ivu-cascader-menu-item{
//     font-size: 14px !important;
//  }
//  .ivu-select-item,.ivu-select{
//      font-size:14px !important;
//  }
//  .ivu-select-input{
//      font-size: 14px;
//  }
//  .ivu-select-small .ivu-select-input{
//     height: 24px;
//     line-height: 24px;
//  }
//  .ivu-input-small{
//     height: 30px;
//     font-size: 14px;
// }
 .ivu-input-number-small{
    //  height: 30px;
     width: 200px;
    //  font-size: 14px;
 }
//  .ivu-input-number-input-wrap{
//      height: 30px;
//  }
//  .ivu-input-number-small input{
//      height: 30px;
//      line-height: 30px;
//  }
 .ivu-input[disabled],.ivu-select-input[disabled]{
    color:#515a6e;
}
.ivu-select-input[disabled]{
    -webkit-text-fill-color:#515a6e;
}
// 兼容火狐增加的全局样式
@-moz-document url-prefix() {
    .ivu-cascader .ivu-cascader-menu-item {
       white-space: nowrap; 
    }
  }

body,html {
  background: #FCFCFC !important;
}

div.ivu-card {
  border-radius: 0;
}

div.ivu-card-bordered {
  border: none;
  background-color: #fff;
  box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.04);

  .ivu-card-body {
    padding: 16px 24px;
  }

  &:hover {
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.04);
    border-color: transparent;
  }
}
