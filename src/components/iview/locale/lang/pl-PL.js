import setLang from '../lang';

const lang = {
    i: {
        locale: 'pl-PL',
        select: {
            placeholder: '<PERSON><PERSON><PERSON><PERSON>',
            noMatch: '<PERSON>rak pasujących wyników',
            loading: '<PERSON><PERSON>wanie'
        },
        table: {
            noDataText: '<PERSON>rak danych',
            noFilteredDataText: '<PERSON>rak danych',
            confirmFilter: 'Potwierdź',
            resetFilter: 'Resetuj',
            clearFilter: 'W<PERSON><PERSON>t<PERSON>',
            sumText: 'Razem'
        },
        datepicker: {
            selectDate: 'Wybierz datę',
            selectTime: 'Wybierz godzinę',
            startTime: '<PERSON><PERSON>a początkowa',
            endTime: 'God<PERSON>a końcowa',
            clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
            ok: 'OK',
            datePanelLabel: '[mmmm] [yyyy]',
            month: 'Miesiąc',
            month1: 'Stycze<PERSON>',
            month2: '<PERSON>ty',
            month3: '<PERSON><PERSON><PERSON>',
            month4: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            month5: 'Maj',
            month6: '<PERSON><PERSON><PERSON><PERSON>',
            month7: '<PERSON>piec',
            month8: '<PERSON><PERSON><PERSON><PERSON>',
            month9: 'Wrzesie<PERSON>',
            month10: 'Pa<PERSON><PERSON><PERSON><PERSON>',
            month11: 'Listopad',
            month12: 'Grudzień',
            year: 'Rok',
            weekStartDay: '1',
            weeks: {
                sun: 'Ndz',
                mon: 'Pon',
                tue: 'Wto',
                wed: 'Śro',
                thu: 'Czw',
                fri: 'Pią',
                sat: 'Sob'
            },
            months: {
                m1: 'Sty',
                m2: 'Lut',
                m3: 'Mar',
                m4: 'Kwi',
                m5: 'Maj',
                m6: 'Cze',
                m7: 'Lip',
                m8: 'Sie',
                m9: 'Wrz',
                m10: 'Paź',
                m11: 'Lis',
                m12: 'Gru'
            }
        },
        transfer: {
            titles: {
                source: 'Źródłowy',
                target: 'Docelowy'
            },
            filterPlaceholder: 'Szukaj tutaj',
            notFoundText: 'Nie znaleziono'
        },
        modal: {
            okText: 'OK',
            cancelText: 'Anuluj'
        },
        poptip: {
            okText: 'OK',
            cancelText: 'Anuluj'
        },
        page: {
            prev: 'Poprzednia Strona',
            next: 'Następna Strona',
            total: 'Łącznie',
            item: 'element',
            items: 'elementów',
            prev5: 'Poprzednie 5 Stron',
            next5: 'Następne 5 Stron',
            page: '/stronę',
            goto: 'Idź do',
            p: ''
        },
        rate: {
            star: 'Gwiazdka',
            stars: 'Gwiazdek'
        },
        time: {
            before: ' temu',
            after: ' po',
            just: 'dopiero co',
            seconds: ' sekund',
            minutes: ' minut',
            hours: ' godzin',
            days: ' dni'
        },
        tree: {
            emptyText: 'Brak danych'
        }
    }
};

setLang(lang);

export default lang;
