<template>
    <div :class="itemClasses">
        <div :class="headerClasses" @click="toggle">
            <Icon type="ios-arrow-forward" v-if="!hideArrow"></Icon>
            <slot></slot>
            <div :class="extraClass" v-if="$slots.extra">
                <slot name="extra"></slot>
            </div>
        </div>
        <collapse-transition v-if="mounted">
            <div :class="contentClasses" v-show="isActive">
                <div :class="boxClasses"><slot name="content"></slot></div>
            </div>
        </collapse-transition>
    </div>
</template>
<script>
    import Icon from '../icon/icon.vue';
    import CollapseTransition from '../base/collapse-transition';
    const prefixCls = 'ivu-collapse';

    export default {
        name: 'Panel',
        components: { Icon, CollapseTransition },
        props: {
            name: {
                type: String
            },
            hideArrow: {
                type: Boolean,
                default: false
            },
            collapsible: {
                type: Boolean,
                default: true
            }
        },
        data () {
            return {
                index: 0, // use index for default when name is null
                isActive: false,
                mounted: false
            };
        },
        computed: {
            itemClasses () {
                return [
                    `${prefixCls}-item`,
                    {
                        [`${prefixCls}-item-active`]: this.isActive
                    }
                ];
            },
            headerClasses () {
                return [
                    `${prefixCls}-header`,
                    {
                        [`${prefixCls}-header-collapse-disabled`]: !this.collapsible
                    }
                ]
            },
            extraClass () {
                return `${prefixCls}-extra`;
            },
            contentClasses () {
                return `${prefixCls}-content`;
            },
            boxClasses () {
                return `${prefixCls}-content-box`;
            }
        },
        methods: {
            toggle () {
                if (!this.collapsible) return;
                this.$parent.toggle({
                    name: this.name || this.index,
                    isActive: this.isActive
                });
            }
        },
        mounted () {
            this.mounted = true;
            this.$parent.setActive();
        }
    };
</script>
