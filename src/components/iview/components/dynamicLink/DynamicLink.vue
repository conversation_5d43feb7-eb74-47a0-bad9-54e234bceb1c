<template>
    <span >
        <Button v-if="typeDefine.button" :size="typeDefine.buttonSize" :type="typeDefine.buttonType" @click="clickLink">
            {{name}}
        </Button>
        <span v-else :class="classes">
            <a class="afsDynamicLink" :href="toUri" target="_blank" @click="clickLink">
                {{name}}
            </a>
        </span>
    </span>
</template>

<script>
    import {Base64} from 'js-base64'
    import getUuid from "@/libs/afs-uuid";
    export default {
        name:'dynamic-link',
        props: {
            name: {
                type: String,
                require: true
            },
            params:{
                type:Object,
                default:()=>{ return {} }
            },
            pageTitle:{
                type: String,
                require: true
            },
            component:{
                type:String,
                require: true
            },
            isFull:{
                type:Boolean,
                default:()=>{ return false }
            },
            uri:{
                type:String,
                default:()=>{ return '' }
            },
            disableClick:{
                type:Boolean,
                default:()=>{ return false }
            },
            typeDefine:{
                type:Object,
                default:()=> {
                    return{
                        button: false,
                        buttonSize: 'small',
                        buttonType: ''
                    }
                }
            },
            withSsoToken:{
                type:Boolean,
                default:()=>{ return false }
            },
            classes:{
                type:String,
                default:()=>{ return '' }
            }
        },
        computed:{
            toUri(){
                if(this.uri!==''){
                    if(this.withSsoToken&&this.$store.getters.sso_access_token!=='') {
                        if (this.uri.indexOf("?") > 0) {
                            return this.uri+"&token="+this.$store.getters.sso_access_token;
                        }else {
                            return this.uri+"?token="+this.$store.getters.sso_access_token;
                        }
                    }
                    return this.uri;
                }else {
                    // console.log(token)
                    let _afsDynamicLinkKey = md5(this.$route.name + this.$store.state.app.sessionKey)
                    let data = {
                        sessionKey:this.$store.state.app.sessionKey,
                        pageData: {
                            params: Object.assign({_afsDynamicLinkKey:'key_'+getUuid()},this.params),
                            component: this.component,
                            isFull: this.isFull,
                            pageTitle: this.pageTitle,
                            eventHashKey: _afsDynamicLinkKey
                        }
                    };
                    // console.log("aaa6"+Base64.encode(JSON.stringify(data), true));
                    // console.log("[aaab6]"+Base64.decode(Base64.encode(JSON.stringify(data), true)));
                    return this.$router.resolve('/d/afsLink').href + '?_link=' + Base64.encode(JSON.stringify(data), true)


                }
           }
        },
        data(){
            return {}
        },
        methods:{
            clickLink(){
                if(this.disableClick){
                    const event = event || window.event;
                    if (event.preventDefault) {
                        event.preventDefault();
                    } else {
                        event.returnValue = false;
                    }
                    return false;
                }
                if(this.typeDefine.button){
                    window.open(this.toUri,"_blank")
                }
            },
            getToken(){
                return  {
                    accessToken:this.$store.getters.access_token,
                    refreshToken:this.$store.getters.refresh_token,
                    expiresIn:(this.$store.getters.expires_in-Date.now())/1000
                }
            }
        }
    }
</script>

<style>

</style>
