<template>
    <div style="height:100%">
        <!-- <Tabs :value="tabName" type="card" :animated="true">
             <TabPane label="影像资料" icon="md-photos"  name="name2"> -->
        <fileAudit :auditParam="this.auditParam" :operate="this.operate" :businessDic="[{value:this.abc,title:'货后上传'}]" ></fileAudit>
        <!-- </TabPane>
    </Tabs> -->
    </div>
</template>

<script>
    import fileAudit from "@/projects/basic/pages/image/file-audit/fileAudit.vue";
    export default {
        name: "asset-file",
        components: {fileAudit},
        watch:{},
        data:function () {
            return {
                auditParam:this.afs.getPageParams(this).auditParam,
                operate:this.afs.getPageParams(this).operate,
                abc:'postLoan',
            }

        },
        mounted(){
            console.log('this.auditParam',this.auditParam)
            ////判断是不是货后上传
            if(this.auditParam.busiNode.indexOf("postLoan") !== -1){
                this.abc ='postLoan'
            }
        },
        methods:{}
    }
</script>
