<template>
    <input
        :id="elementId"
        ref="pw-input"
        :class="inputClasses"
        :name="name"
        :placeholder="placeholder"
        :value="showValue"
        autocomplete="off"
        style="ime-mode:disabled"
        type="text"
        @dragenter="_dragenterhandlder"
        @dragleave="_dragleaveHandler"
        @input="cinput"
        @mousedown="_mousedownHandler"
        @copy.prevent=""
        @cut.prevent=""
        @compositionstart.prevent="_compositionstartHandler"
        @compositionend.prevent="_compositionendHandler"
        @keyup.enter="handleEnter"
        @keyup="handleKeyup"
        @keypress="handleKeypress"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
        @change="handleChange"
    />
</template>
<script>
export default {
    name: 'afs-password',
    props: {
        value: {
            type: String,
            default: ""
        },
        symbol: {
            type: String,
            default: "*"
        },
        name: {
            type: String
        },
        placeholder: {
            type: String,
            default: ''
        },
        inputClasses: {
            type: Array
        },
        elementId: {
            type: String
        },
        showPassword:{
            type:Boolean,
            default(){
                return false;
            }
        }
    },
    data() {
        return {
            valueProxy: "",
            type:'password'
        };
    },
    computed: {
        showValue() {
            if (this.type !== "password") {
                return this.valueProxy;
            }
            return this.fillSymbol();
        }
    },
    methods: {
        handleEnter(e){
            this.$emit("passEnter",e);
        },
        handleKeyup(e){
            this.$emit("passKeyUp",e);
        },
        handleKeypress(e){
            this.$emit("passKeyPress",e);
        },
        handleKeydown(e){
            this.$emit("passKeyDown",e);
        },
        handleFocus(e){
            this.$emit("passKeyFocus",e);
        },
        handleBlur(e){
            this.$emit("passKeyBlur",e);
        },
        handleChange(e){
            this.$emit("passChange",e);
        },
        _compositionstartHandler() {
            this.lock = true;
            console.log("start")
        },
        _compositionendHandler(e) {
            this.lock = false;
            console.log("end")
            if (e.target.composing === undefined) this.cinput();// 兼容Vue版本 有composing属性的版本会自动触发一次cinput
        },
        _mousedownHandler(e) {
            if (e.button === 0 && (this.$refs['pw-input'].selectionStart !== this.$refs['pw-input'].selectionEnd)) {
                this.$refs['pw-input'].selectionStart = this.$refs['pw-input'].selectionEnd = this.valueProxy.length;
            }
            return false
        },
        _dragleaveHandler(e) {
            e.preventDefault();
            this.$refs['pw-input'].removeAttribute('readonly')
            return false
        },
        _dragenterhandlder(e) {
            e.preventDefault();
            this.$refs['pw-input'].setAttribute('readonly', true)
            return false
        },
        _initValue() {
            let value = this.value
            if (value === undefined || value === null) value = "";
            this.$emit('input', this.valueProxy);
            this.$emit('passInput', this.valueProxy);
        },
        _inputHandler(event) {
            let cvalueArr = this.$refs['pw-input'].value.split(""),
                ovalueArr = this.valueProxy.split(""),
                clen = cvalueArr.length - ovalueArr.length,
                cursor = this.$refs['pw-input'].selectionStart,
                isSymbol = false;

            console.log(ovalueArr)
            if (clen > 0) {
                let inArr = cvalueArr.join("").replace(/\*/g, "").split("");
                if(inArr.length===0){
                    isSymbol = true;
                    for(let index=0;index<clen;index++){
                        inArr.push('*');
                    }
                }
                let right = cvalueArr.length - cursor > 0 ? ovalueArr.slice(-(cvalueArr.length - cursor)) : [];
                ovalueArr = [].concat(ovalueArr.slice(0, cursor - inArr.length), inArr, right);
            }
            console.log(ovalueArr)
            if (clen < 0) {
                ovalueArr.splice(cursor, Math.abs(clen));
            }
            if(!isSymbol) {
                cvalueArr.forEach(function (value, index) {
                    if (value !== "*") {
                        ovalueArr[index] = value;
                    }
                });
            }
            if(isSymbol&&cvalueArr.length>ovalueArr.length){
                let fillCount = 0;
                while(cvalueArr.length>ovalueArr.length){
                    ovalueArr[cursor+fillCount] = '*';
                    fillCount+=1;
                }
            }
            if (this.valueProxy === ovalueArr.join("")) {
                this.$forceUpdate();
            } else {
                this.valueProxy = ovalueArr.join("");
            }
        },
        cinput(e) {
            if (this.lock) return;
            if (this.type !== 'password') {
                this.valueProxy = this.$refs['pw-input'].value;
                this.$forceUpdate();
            } else {
                this._inputHandler(e);
            }
            console.log(this.valueProxy)
            this.$emit('passInput', this.valueProxy);
        },
        fillSymbol() {
            let symbolStr = "";
            for (let i = 0, len = this.valueProxy.length; i < len; i++) {
                symbolStr += this.symbol;
            }
            return symbolStr;
        }
    },
    watch: {
        value(val) {
            if (val !== this.valueProxy) this._initValue();
        },
        showPassword(){
            if(this.showPassword){
                this.type='text';
            }else{
                this.type = 'password'
            }
        }
    },
    mounted() {
        this._initValue();
    }
};
</script>
