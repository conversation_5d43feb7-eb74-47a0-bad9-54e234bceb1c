<template>
    <div :class="[prefixCls]">
        <div
            :class="classes"
            @click="handleClick">
            <input
                ref="input"
                type="file"
                :class="[prefixCls + '-input']"
                @change="handleChange"
                :multiple="false"
                :webkitdirectory="false"
                :accept="accept">
            <slot></slot>
        </div>
        <slot name="tip"></slot>
    </div>
</template>
<script>
    import Emitter from '../../mixins/emitter';
    import mixinsForm from '../../mixins/form';

    const prefixCls = 'ivu-upload';

    export default {
        name: 'file-choice',
        mixins: [ Emitter, mixinsForm ],
        props: {
            multiple: {
                type: Boolean,
                default: false
            },
            format: {
                type: Array,
                default () {
                    return [];
                }
            },
            accept: {
                type: String
            },
            maxSize: {
                type: Number
            },
            disabled: {
                type: Boolean,
                default: false
            }, onExceededSize: {
                type: Function,
                default () {
                    return {};
                }
            },
            onFormatError: {
                type: Function,
                default () {
                    return {};
                }
            },
            onSuccess: {
                type: Function,
                default () {
                    return {};
                }
            },
            excelImport: {
                type: Function,
                default () {
                    return {};
                }
            },
        },
        data () {
            return {
                prefixCls: prefixCls,
                fileList: [],
                tempIndex: 1
            };
        },
        computed: {
            classes () {
                return [
                    `${prefixCls}`,
                    {
                        [`${prefixCls}-select`]: true
                    }
                ];
            },

        },
        methods: {
            handleClick () {
                if (this.itemDisabled) return;
                this.$refs.input.click();
            },
            handleChange (e) {
                const files = e.target.files;

                if (!files) {
                    return;
                }
                this.doFileChoice(files);
                this.$refs.input.value = null;
            },
            doFileChoice (files) {
                let postFiles = Array.prototype.slice.call(files);
                if (postFiles.length === 0) return;
                postFiles.forEach(file => {
                    this.choice(file);
                });
            },
            choice (file) {
                // check format
                if (this.format.length) {
                    const _file_format = file.name.split('.').pop().toLocaleLowerCase();
                    const checked = this.format.some(item => item.toLocaleLowerCase() === _file_format);
                    if (!checked) {
                        this.onFormatError(file, this.fileList);
                        return false;
                    }
                }
                // check maxSize
                if (this.maxSize) {
                    if (file.size > this.maxSize * 1024) {
                        this.onExceededSize(file, this.fileList);
                        return false;
                    }
                }
                const reader = new FileReader();
                //将文件读取为 DataURL 以data:开头的字符串
                reader.readAsDataURL(file);
                reader.onload = e => {
                    // 读取到的文件base64 数据编码 将此编码字符串传给后台即可
                    console.log(file)
                    const code = e.target.result;
                    this.onSuccess(code,file.name)
                    this.excelImport(file)
                }
                return false;
            }
        }
    };
</script>
