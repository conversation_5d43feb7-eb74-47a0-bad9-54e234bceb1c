<template>
    <div :class="prefixCls"  v-show="show" :style="contentStyle">
        <div v-if="isInit">
            <slot></slot>
        </div>
    </div>
</template>
<script>
    const prefixCls = 'ivu-tabs-tabpane';

    export default {
        name: 'TabPane',
        inject: ['TabsInstance'],
        props: {
            name: {
                type: String
            },
            label: {
                type: [String, Function],
                default: ''
            },
            icon: {
                type: String
            },
            disabled: {
                type: Boolean,
                default: false
            },
            closable: {
                type: Boolean,
                default: null
            },
            // Tabs 嵌套时，用 tab 区分层级，指向对应的 Tabs 的 name
            tab: {
                type: String
            },
            // 在 TabPane 使用 v-if 时，并不会按照预先的顺序渲染，这时可设置 index，并从小到大排序
            // 数值需大于 0
            index: {
                type: Number
            },
            linkOption:{
                type:Object,
                default:undefined
            },
            // 4.3.0
            contextMenu: {
                type: <PERSON>olean,
                default: false
            },
        },
        data () {
            return {
                prefixCls: prefixCls,
                show: false,//fzero 默认为false，切换tab时，才进行初始化
                currentName: this.name,
                isInit:false//fzero 默认为false，切换tab时，才进行初始化
            };
        },
        computed: {
            contentStyle () {
                return {
                    visibility: this.TabsInstance.activeKey !== this.currentName ? 'hidden' : 'visible'
                };
            }
        },
        methods: {
            updateNav () {
                this.TabsInstance.updateNav();
            },
            //fzero 默认为false，切换tab时，才进行初始化
            setInit(){
                if(this.show&&this.TabsInstance.activeKey===this.currentName){
                    this.isInit = true;
                }
            }
        },
        watch: {
            name (val) {
                this.currentName = val;
                this.updateNav();
            },
            label () {
                this.updateNav();
            },
            icon () {
                this.updateNav();
            },
            disabled () {
                this.updateNav();
            },
            //fzero 默认为false，切换tab时，才进行初始化
            show:{
                handler:'setInit',
                immediate:true
            }
        },
        mounted () {
            this.updateNav();
        },
        destroyed () {
            this.updateNav();
        }
    };
</script>
