<template>
    <div style="display: inline-block;">
        <Icon class="spin-icon-load" :color="color" :size="size" type="ios-loading"></Icon>
    </div>
</template>

<script>
    export default {
        name: "circleLoading",
        props: {
            color: {
                type: String,
                default: "#2d8cf0",
            },
            size: {
                type: Number,
                default: 18,
            }
        }
    };
</script>

<style lang="less">
    .spin-icon-load {
        margin-left: 5px;
        animation: ani-demo-spin 1s linear infinite;
    }
</style>
