import {getUri} from "@/libs/tools";
export const ACCEPT_CONFIG = {
    image: ['.png', '.jpg', '.jpeg'],
    video: ['.mp4', '.rmvb', '.mkv', '.wmv', '.flv'],
    document: ['.doc', '.docx','.pdf'],
    compress:[],
    excel: ['.xlsx', '.xls'],
    getAll(){
        return [...this.image, ...this.video, ...this.document,...this.compress]
    },
    getExcel(){
        return [...this.excel]
    },
}

export const FILE_SIZE_CONFIG={
    maxFileSize:1024000000,
    chunkedSize:1048576
}

export const UPLOAD_URIS={
    commonUri:`${_AFS_PROJECT_CONFIG.apiUri}/filecenter/filestore/upload`,
    chunkedUri:`${_AFS_PROJECT_CONFIG.apiUri}/filecenter/filestore/store`
}
