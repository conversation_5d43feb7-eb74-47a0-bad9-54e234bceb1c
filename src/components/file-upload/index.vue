<style scoped lang="less" rel="stylesheet/less">
    .ruicar-afs-upload {
        .uploader-drop {
            padding: 0;
        }

        .ruicar-afs-upd-btn {
            width: 100%;
            padding: 0;
            border: 0px;
        }

        .upload-list{
            padding-top: 5px;
            padding-bottom: 5px;
        }
    }
</style>
<template>
    <div v-if="modal">
        <Button  @click="showModal=!showModal">
            <Icon type="md-cloud-upload" />
            上传文件
        </Button>
        <Modal :scrollable="false" class-name="vertical-center-modal"  v-model="showModal"  :closable="true" :maskClosable="false" :width="850"  title="文件上传" :footerHide="true" >
            <div class="ruicar-afs-upload">
                <uploader ref="uploaderDom" :isExcel="isExcel" :root-node="rootNode"  :file-selected="fileSelected"  :extInfo="extInfo" @uploadSuccess="postMessage" @uploadError="uploadFailed" :chunkEnable="chunkEnable" :requestPath="requestPath" @deleteFile="deleteFile" :tableMaxHeight="tableMaxHeight" :uploadQuery="query"/>
            </div>
        </Modal>
    </div>
    <div v-else class="ruicar-afs-upload">
        <uploader ref="uploaderDom" :isExcel="isExcel" :root-node="rootNode"  :file-selected="fileSelected" :extInfo="extInfo"  @uploadSuccess="postMessage" @uploadError="uploadFailed" :chunkEnable="chunkEnable" :requestPath="requestPath" @deleteFile="deleteFile" :tableMaxHeight="tableMaxHeight" :uploadQuery="query"/>
    </div>
</template>

<script>
    import uploader from './uploader'
    export default{
        name: 'fileUpload',
        data(){
            return {
                showModal:false
            }
        },
        props: {
            query:{
                type:Object,
                required:false
            },
            modal:{
                type:Boolean,
                required: false,
                default:true
            },
            requestPath:{
                type:String,
                required:false,
                default: ''
            },
            // 文件上传列表最大高度
            tableMaxHeight:{
                type:[String,Number],
                required:false,
                default: 0
            },
            chunkEnable:{
                type:Boolean,
                required:false,
                default:true
            },
            extInfo:{
               type:Object,
               default:()=>{
                   return {};
               }
            }
            ,
            rootNode:{
                type:Object,
                default:()=>{
                    return null;
                }
            },
            fileSelected:{
                type:Function,
                default:(guid)=>{
                    return  ()=>{return {}}
                }
            },
            isExcel: {
                type: Boolean,
                default: false
            },

        },
        methods:{
            cancelFiles(arr){
                this.$refs.uploaderDom.cancelFiles(arr)
            },
            postMessage(uploadInfo,guid,extInfo,params){
                this.$emit('afterUpload',uploadInfo,guid,extInfo,params);
            },
            uploadFailed(uploadInfo,guid,extInfo,params){
                this.$emit('uploadFailed',uploadInfo,guid,extInfo,params);
            },
            deleteFile(fileInfo){
                this.$emit('deleteFile',fileInfo);
            },
        },
        components: {
            uploader
        }
    }
</script>
