<style lang="less">
    @import "placeCascade.less";
</style>
<template>
    <div class="cascade">
        <div v-if="!isDetailAddress">
            <FormItem :label="label" :prop="value" style="position: relative;">
                <Cascader :load-data="loadData"
                          :data="location"
                          v-model="form[value]"
                          style="padding-top: 4px;"
                          :disabled="isShowDetails||isNetLocation||isAssertChange||isReconsider||isLicense"
                >
                </Cascader>
            </FormItem>
        </div>
        <div v-else>
            <Row style="padding-top: 4px;">
                <Col :span="isPerFlag?'24':'16'" >
                    <FormItem :label="label" :prop="value" style="position: relative;width:340px" class="inline-block">
                        <Cascader
                            :load-data="loadData" :data="location" v-model="form[value]" placeholder="下拉选择省市"
                            class="show_cascader_input" :clearable="false" :disabled="isShowDetails||companyAffi||isReconsider">
                        </Cascader>
                    </FormItem>
                    <FormItem :prop="detailValue" :label-width="0" class="inline-block"
                              style="width:50%;margin-left: -4px;">
                        <Input v-model="form[detailValue]" placeholder="请输入详细地址" :readonly="false"
                               style="vertical-align: top;" class="detail_cascader_input" :disabled="isShowDetails||companyAffi||isReconsider"/>
                    </FormItem>
                </Col>
            </Row>
        </div>
    </div>
</template>
<script>
    import {getLocation} from "_p/basic/api/common/common.js"
    export default {
        name: "cascade",
        data() {
            return {
                // location: [],
            }
        },
        props: {
            isAssertChange: {
                type: Boolean,
            },
            companyAffi:{
                type:Boolean
            },
            form: {
                type: Object,
                require: true
            },
            value: {
                // type:String,
                require: true,
            },
            detailValue: {
                type: String,
            },
            label: {
                type: String,
                require: true,
            },
            isDetailAddress: {
                type: Boolean,
            },
            location: {
                type: Array,
            },
            isShowDetails: {
                type: Boolean,
            },
            isReconsider: {
                type: Boolean,
            },
            isNetLocation: {
                type: Boolean
            },
            isPerFlag:{
                type:Boolean
            },
            isLicense:{
                type:Boolean,
                default:false,
                require:false,
            }
        },
        created() {
        },
        mounted() {
        },
        methods: {
            loadData(item, callback) {
                console.log("是否调用新车")
                item.loading = true;
                getLocation({upperCode: item.value}).then(res => {
                    if (res.code === "0000") {
                        res.data.forEach(function (item) {
                            if (item.isParent) {
                                item.loading = false;
                                item.children = [];
                            }
                        });
                        item.children = res.data;
                        item.loading = false;
                        callback();
                    }
                })
            },
        }
    }
</script>


