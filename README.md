# afs-ui 汽车金融前端系统
## 名词说明
1. 工程指整个汽车金融前端
2. 项目指子系统
## 项目结构
    ├── public
    │   ├── favicon.ico  # ico图标
    │   └── index.html   # 首页模板
    ├── src 
        ├─assets 公共资源文件
        │  ├─css
        │  ├─img
        │  └─js
        ├─components 公共组件
        ├─config 公共组件
        ├─const  前端常量配置
        ├─libs   工程公用库
        ├─projects 项目目录
        │  ├─projectA  A项目
        │  │  ├─api 项目专属api
        │  │  ├─assets 本项目或者本模块资源文件
        │  │  │  ├─css
        │  │  │  ├─img
        │  │  │  └─js
        │  │  ├─components 组件目录
        │  │  ├─util 项目专属工具js
        │  │  ├─pages 页面目录
        │  │  │  ├─home 项目主页
        │  │  │  ├─login 项目登录页
        │  │  ├─styles 项目专属供样式
## 说明     
1. src 下的config 目录中的afs_projectConfig.js为子项目配置，afs_proxy_config为开发时后端代理配置,其他为打包用配置不用修改  
2. 项目目录在 src/projects目录，各项目按照项目名分子目录存放
3. 各项目按照 功能模块建立页面及api 独立目录进行开发  
4. 工程通用性组件必须放在components 目录中  

## npm 仓库地址
 https://ci.51rcar.com/repository/npm/  
 用户名：anonymous  
 密码：51rcarNexus
### 配置步骤 
 1. 在cmd 执行 npm config set registry https://ci.51rcar.com/repository/npm/
 2. 在用户目录找到.npmrc 文件，用文本编辑器打开
 3. 在.npmrc 文件后边添加，已下三行数据  
    email=<EMAIL>  
    always-auth=true  
    _auth="YW5vbnltb3VzOjUxcmNhck5leHVz"
 4. 执行npm install 或者 yarn  
    
 

 #