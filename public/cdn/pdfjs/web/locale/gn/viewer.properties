# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Kuatiarogue mboyvegua
previous_label=Mboyvegua
next.title=Kuatiarogue upeigua
next_label=Upeigua

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Kuatiarogue
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} gui
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=Momichĩ
zoom_out_label=Momichĩ
zoom_in.title=Mbotuicha
zoom_in_label=Mbotuicha
zoom.title=Tuichakue
presentation_mode.title=Jehechauka reko moambue
presentation_mode_label=Jehechauka reko
open_file.title=Marandurendápe jeike
open_file_label=Jeike
print.title=Monguatia
print_label=Monguatia
download.title=Mboguejy
download_label=Mboguejy
bookmark.title=Ag̃agua jehecha (mbohasarã térã eike peteĩ ovetã pyahúpe)
bookmark_label=Ag̃agua jehecha

# Secondary toolbar and context menu
tools.title=Tembipuru
tools_label=Tembipuru
first_page.title=Kuatiarogue ñepyrũme jeho
first_page.label=Kuatiarogue ñepyrũme jeho
first_page_label=Kuatiarogue ñepyrũme jeho
last_page.title=Kuatiarogue pahápe jeho
last_page.label=Kuatiarogue pahápe jeho
last_page_label=Kuatiarogue pahápe jeho
page_rotate_cw.title=Aravóicha mbojere
page_rotate_cw.label=Aravóicha mbojere
page_rotate_cw_label=Aravóicha mbojere
page_rotate_ccw.title=Aravo rapykue gotyo mbojere
page_rotate_ccw.label=Aravo rapykue gotyo mbojere
page_rotate_ccw_label=Aravo rapykue gotyo mbojere

cursor_text_select_tool.title=Emyandy moñe'ẽrã jeporavo rembipuru
cursor_text_select_tool_label=Moñe'ẽrã jeporavo rembipuru
cursor_hand_tool.title=Tembipuru po pegua myandy
cursor_hand_tool_label=Tembipuru po pegua

scroll_vertical.title=Eipuru jeku’e ykeguáva
scroll_vertical_label=Jeku’e ykeguáva
scroll_horizontal.title=Eipuru jeku’e yvate gotyo
scroll_horizontal_label=Jeku’e yvate gotyo
scroll_wrapped.title=Eipuru jeku’e mbohyrupyre
scroll_wrapped_label=Jeku’e mbohyrupyre

spread_none.title=Ani ejuaju spreads kuatiarogue ndive
spread_none_label=Spreads ỹre
spread_odd.title=Embojuaju kuatiarogue jepysokue eñepyrũvo kuatiarogue impar-vagui
spread_odd_label=Spreads impar
spread_even.title=Embojuaju kuatiarogue jepysokue eñepyrũvo kuatiarogue par-vagui
spread_even_label=Ipukuve uvei

# Document properties dialog box
document_properties.title=Kuatia mba'etee…
document_properties_label=Kuatia mba'etee…
document_properties_file_name=Marandurenda réra:
document_properties_file_size=Marandurenda tuichakue:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Teratee:
document_properties_author=Apohára:
document_properties_subject=Mba'egua:
document_properties_keywords=Jehero:
document_properties_creation_date=Teñoihague arange:
document_properties_modification_date=Iñambue hague arange:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Apo'ypyha:
document_properties_producer=PDF mbosako'iha:
document_properties_version=PDF mbojuehegua:
document_properties_page_count=Kuatiarogue papapy:
document_properties_page_size=Kuatiarogue tuichakue:
document_properties_page_size_unit_inches=Amo
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=Oĩháicha
document_properties_page_size_orientation_landscape=apaisado
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Kuatiañe'ẽ
document_properties_page_size_name_legal=Tee
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ñanduti jahecha pya’e:
document_properties_linearized_yes=Añete
document_properties_linearized_no=Ahániri
document_properties_close=Mboty

print_progress_message=Embosako'i kuatia emonguatia hag̃ua…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Heja

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Tenda yke moambue
toggle_sidebar_notification.title=Embojopyru tenda ykegua (kuatia oguereko kora/marandurenda moirũha)
toggle_sidebar_label=Tenda yke moambue
document_outline.title=Ehechauka kuatia rape (eikutu mokõi jey embotuicha/emomichĩ hag̃ua opavavete mba'epuru)
document_outline_label=Kuatia apopyre
attachments.title=Moirũha jehechauka
attachments_label=Moirũha
thumbs.title=Mba'emirĩ jehechauka
thumbs_label=Mba'emirĩ
findbar.title=Kuatiápe jeheka
findbar_label=Juhu

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Kuatiarogue {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Kuatiarogue {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Kuatiarogue mba'emirĩ {{page}}

# Find panel button title and messages
find_input.title=Juhu
find_input.placeholder=Kuatiápe jejuhu…
find_previous.title=Ejuhu ñe'ẽrysýi osẽ'ypy hague
find_previous_label=Mboyvegua
find_next.title=Eho ñe'ẽ juhupyre upeiguávape
find_next_label=Upeigua
find_highlight=Embojekuaavepa
find_match_case_label=Ejesareko taiguasu/taimichĩre
find_entire_word_label=Ñe’ẽ oĩmbáva 
find_reached_top=Ojehupyty kuatia ñepyrũ, oku'ejeýta kuatia paha guive
find_reached_bottom=Ojehupyty kuatia paha, oku'ejeýta kuatia ñepyrũ guive
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} {{total}} ojojoguáva
find_match_count[two]={{current}} {{total}} ojojoguáva
find_match_count[few]={{current}} {{total}} ojojoguáva
find_match_count[many]={{current}} {{total}} ojojoguáva
find_match_count[other]={{current}} {{total}} ojojoguáva
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Hetave {{limit}} ojojoguáva
find_match_count_limit[one]=Hetave {{limit}} ojojogua
find_match_count_limit[two]=Hetave {{limit}} ojojoguáva
find_match_count_limit[few]=Hetave {{limit}} ojojoguáva
find_match_count_limit[many]=Hetave {{limit}} ojojoguáva
find_match_count_limit[other]=Hetave {{limit}} ojojoguáva
find_not_found=Ñe'ẽrysýi ojejuhu'ỹva

# Error panel labels
error_more_info=Maranduve
error_less_info=Sa'ive marandu
error_close=Mboty
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ñe'ẽmondo: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Mbojo'apy: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Marandurenda: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Tairenda: {{line}}
rendering_error=Oiko jejavy ehechaukasévo kuatiarogue.

# Predefined zoom values
page_scale_width=Kuatiarogue pekue
page_scale_fit=Kuatiarogue ñemoĩporã
page_scale_auto=Tuichakue ijeheguíva
page_scale_actual=Tuichakue ag̃agua
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Oĩvaíva
loading_error=Oiko jejavy PDF oñemyeñyhẽnguévo.
invalid_file_error=PDF marandurenda ndoikóiva térã ivaipyréva.
missing_file_error=Ndaipóri PDF marandurenda
unexpected_response_error=Mohendahavusu mbohovái ñeha'arõ'ỹva.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Jehaipy {{type}}]
password_label=Emoinge ñe'ẽñemi eipe'a hag̃ua ko marandurenda PDF.
password_invalid=Ñe'ẽñemi ndoikóiva. Eha'ã jey.
password_ok=MONEĨ
password_cancel=Heja

printing_not_supported=Kyhyjerã: Ñembokuatia ndojokupytypái ko kundahára ndive.
printing_not_ready=Kyhyjerã: Ko PDF nahenyhẽmbái oñembokuatia hag̃uáicha.
web_fonts_disabled=Ñanduti taity oñemongéma: ndaikatumo'ãi eipuru PDF jehai'íva taity.
document_colors_not_allowed=Kuatiakuéra PDF ndaikatúi oipuru isa'ykuéra tee: “Emoneĩ kuatiaroguépe toiporavo isa'ykuéra tee” oñemongehína kundahárape.
