module.exports = {
  root: true,
  env: {
    browser: true
  },
  extends: [
      'plugin:vue/essential',
      'eslint:recommended'
  ],
  rules: {
    // allow async-await
    'generator-star-spacing': 'off',
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
    'indent': 'off',
    'vue/no-parsing-error': [2, { 'x-invalid-end-tag': false }],
    "no-irregular-whitespace":"off",
    "key-spacing": ["off", { "beforeColon": false, "afterColon": true }],
    "keyword-spacing": ["off", { }],
    "space-before-blocks": ["off", { }],
    "space-before-function-paren": ["off", { }],
    "comma-spacing": ["off", { }],
    "space-infix-ops": ["off", { }],
    "semi": ["off", { }],
    "semi-spacing": ["off", { }],
    "arrow-spacing": ["off", { }],
    "no-multi-spaces": ["off", { }],
    "array-bracket-spacing": ["off", { }],
    "standard/array-bracket-even-spacing": ["off", { }],
    "object-curly-spacing": ["off", { }],
    "padded-blocks": ["off", { }],
    "spaced-comment": ["off", { }],
    "quotes": ["off", { }],
    "comma-dangle": ["off", { }],
    "comma-style": ["off", { }],
    "object-property-newline": ["off", { }],
    "no-undef": ["off", { }],
    "one-var": ["off", { }],
    "curly": ["off", { }],
    "no-unused-vars": ["off", { }],
    "no-trailing-spaces": ["off", { }],
    "no-tabs": ["off", { }],
    "eqeqeq": ["off", { }],
    "handle-callback-err": ["off", { }],
    "space-in-parens": ["off", { }],
    "no-mixed-spaces-and-tabs": ['off'],
    "vue/no-unused-vars": ['off'],
    // "indent": ['error', 4 ],
  },
  overrides: [
    {
      'files': ['*.vue'],
      'rules': {
        'indent': 'off'
      }
    }
  ],
  parserOptions: {
    parser: 'babel-eslint'
  }
}
