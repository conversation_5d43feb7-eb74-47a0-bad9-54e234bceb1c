server:
  port: @application.port@
spring:
  cloud:
    nacos:
      server-addr: not_config
      discovery:
        enabled: true
        namespace: not_config
      config:
        file-extension: yml
        namespace: not_config
        shared-configs:
          - data-id: global-basic-config.yml
            refresh: true
          - data-id: global-redis-config.yml
            refresh: true
          - data-id: global-datasource-config.yml
            refresh: true
          - data-id: global-service-name-config.yml
            refresh: true
          - data-id: global-api-config.yml
            refresh: true
          - data-id: global-mq-config.yml
  main:
    allow-bean-definition-overriding: true