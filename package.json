{"name": "afs-ui", "version": "1.0.0", "description": "汽车金融前台", "private": true, "scripts": {"dev": "vue-cli-service serve --project afsCoreBusiness", "build": "vue-cli-service build --project afsCoreBusiness --env test ", "build:sit": "vue-cli-service build --project afsCoreBusiness --env sitImage ", "build:uat": "vue-cli-service build --project afsCoreBusiness --env uatImage ", "build:prod": "vue-cli-service build --project afsCoreBusiness --env prodImage ", "build:docker": "vue-cli-service build --project afsCoreBusiness --env devImage "}, "dependencies": {"@antv/g6": "^4.0.3", "@ztree/ztree_v3": "^3.5.44", "async-validator": "^3.4.0", "axios": "^0.18.0", "clipboard": "^2.0.1", "core-js": "^3.6.5", "countup": "^1.8.2", "cron-parser": "^2.15.0", "cropperjs": "^1.4.1", "debounce": "^1.2.0", "deepmerge": "^2.2.1", "dplayer": "^1.25.0", "echarts": "^4.1.0", "element-resize-detector": "^1.2.0", "jquery": "^3.5.1", "js-calendar": "^1.2.3", "js-cookie": "^2.2.0", "lodash.throttle": "^4.1.1", "moment": "^2.24.0", "mousetrap": "^1.6.3", "popper.js": "^1.14.6", "print-js": "^1.4.0", "screenfull": "^4.2.1", "spark-md5": "^3.0.0", "tinycolor2": "^1.4.1", "uuid": "^8.0.0", "v-click-outside-x": "^3.7.1", "vue": "^2.6.11", "vue-awesome": "^3.1.0", "vue-baidu-map": "^0.21.22", "vue-i18n": "^7.8.0", "vue-json-viewer": "^2.2.15", "vue-router": "^3.2.0", "vue-stomp": "^0.0.5", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "xlsx": "^0.13.4", "fuse.js": "6.4.3", "crypto-js": "^4.2.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^4.0.0", "autoprefixer-loader": "^3.2.0", "babel-cli": "^6.26.0", "babel-core": "^6.26.0", "babel-eslint": "^10.1.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.5", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-import": "^1.7.0", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-es5-property-mutators": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-remove-strict-mode": "0.0.2", "babel-plugin-transform-runtime": "^6.23.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.6.1", "babel-preset-stage-3": "^6.24.1", "babel-runtime": "^6.26.0", "chai": "^4.2.0", "compression-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^4.6.0", "cross-env": "^5.2.0", "css-loader": "^0.28.10", "eslint": "^6.7.2", "eslint-config-prettier": "^6.5.0", "eslint-plugin-vue": "^6.2.2", "file-loader": "^1.1.11", "friendly-errors-webpack-plugin": "^1.6.1", "highlight.js": "^9.17.1", "html-loader": "^0.5.5", "html-webpack-plugin": "^3.0.6", "increase-memory-limit": "^1.0.3", "less": "^3.0.4", "less-loader": "^5.0.0", "lolex": "^2.7.5", "mocha": "^5.0.4", "node-sass": "npm:sass@^1.79.4", "prettier-eslint-cli": "^5.0.0", "sass-loader": "^10.0.0", "sinon": "^4.4.2", "sinon-chai": "^3.3.0", "speed-measure-webpack-plugin": "1.5.0", "style-loader": "^0.20.2", "url-loader": "^1.1.2", "vue-hot-reload-api": "^2.3.1", "vue-html-loader": "^1.2.4", "vue-lazyload": "^1.2.6", "vue-loader": "^14.2.1", "vue-style-loader": "^4.1.2", "vue-template-compiler": "^2.6.11", "webpack": "~4.46.0"}}