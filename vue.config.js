const CompressionWebpackPlugin = require('compression-webpack-plugin')
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')
const afs_proxy_config = require('./src/config/afs_proxy_config')
const afs_projectConfig = require('./src/config/afs_project_config')
const path = require('path')
const fs = require('fs')
const resolve = dir => {
    return path.join(__dirname, dir)
}
const productionGzipExtensions = ['js', 'css']
const smp = new SpeedMeasurePlugin({
    outputFormat: 'human'
})
const Timestamp = new Date().getTime();
let afsInfo = {
    version:afs_projectConfig[process.argv[4]].version,
    name:process.argv[4],
    env:process.argv.length>=6?process.argv[6]:'dev'
}
let afsProjectInfo = {
    title: afs_projectConfig[afsInfo.name].title,
    webKey: afs_projectConfig[afsInfo.name].webKey,
    useI18n: afs_projectConfig[afsInfo.name].useI18n,
    apiUri:afsInfo.env==='dev'?'':(afs_projectConfig[afsInfo.name].apiUri[afsInfo.env]),
    webSocketUri:(afs_projectConfig[afsInfo.name].webSocketUri[afsInfo.env]),
    softPhone:(afs_projectConfig[afsInfo.name].softPhone)?afs_projectConfig[afsInfo.name].softPhone[afsInfo.env]:{api:'no-define',ws:'no-define'},
    topHeaderComponent:(afs_projectConfig[afsInfo.name].topHeaderComponent)?(afs_projectConfig[afsInfo.name].topHeaderComponent):[],
    homeName: afs_projectConfig[afsInfo.name].homeName,
    path:afs_projectConfig[afsInfo.name].path,
    name:afsInfo.name,
    menuLocation:afs_projectConfig[afsInfo.name].menuLocation,
    theme:afs_projectConfig[afsInfo.name].theme,
    componentRequestUri:afs_projectConfig[afsInfo.name].componentRequestUri,
    sysDate:afs_projectConfig[afsInfo.name].sysDate,
    dataDicSource:afs_projectConfig[afsInfo.name].dataDicSource,
    clientInfo: afs_projectConfig[afsInfo.name].clientInfo,
    dynamicServiceScope:(afs_projectConfig[afsInfo.name].dynamicServiceScope)?afs_projectConfig[afsInfo.name].dynamicServiceScope:'default',
    appVersion:`${afsInfo.version}.${Timestamp}`,
    publicPath:afsInfo.env==='dev'?'':(afs_projectConfig[afsInfo.name].publicPath[afsInfo.env])
};

const projectPaths = [];

fs.readdirSync('./src/projects').forEach(file=>{
    if(!file.endsWith('basic')) {
        projectPaths.push('./src/projects/' + file)
    }
})
process.env.VUE_APP_PACKAGE_VERSION = `${afsInfo.version}.${Timestamp}`;
process.env.VUE_APP_BASE_URL = afsProjectInfo.publicPath;
if(afsProjectInfo.publicPath!=='' && afsProjectInfo.publicPath!=='/'){
    afsProjectInfo.publicPath = '/'+afsProjectInfo.publicPath+'/';
}
process.env.VUE_APP_ENV = afsInfo.env;
const buildConfig = {};

if (process.env.NODE_ENV !== 'development') {
    buildConfig.plugins=[
        new CompressionWebpackPlugin({
            filename: '[path].gz[query]',
            algorithm: 'gzip',
            test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
            threshold: 10240,
            minRatio: 0.8
        })
    ];
    buildConfig.output={
        filename:`js/[name].${afsInfo.version}.${Timestamp}.js`,
        chunkFilename:`js/[name].${afsInfo.version}.${Timestamp}.js`
    };
    buildConfig.optimization={
        // minimizer:[
        //     new TerserPlugin({
        //         sourceMap:false,
        //         terserOptions:{
        //             compress:{
        //                 drop_debugger: true,
        //                 drop_console :true,
        //             }
        //         }
        //     })
        // ],
        splitChunks:{
            chunks: 'all',
                minSize: 300,
                maxSize: 512000 * 2,
                maxAsyncRequests: 5,
                maxInitialRequests: 3,
                cacheGroups: {
                    vendors: {
                        name: "chunk-vendors",
                            test: /[\\/]node_modules[\\/]/,
                            chunks: "all",
                            priority: 5,
                            reuseExistingChunk: true,
                    },
                    common: {
                        name: "chunk-commons",
                            test: /[\\/]src[\\/]compoments[\\/]/,
                            minChunks: 2,
                            chunks: 'all',
                            priority: 3,
                            reuseExistingChunk: true,
                    },
                    default: {
                        minChunks: 2,
                        priority: -26,
                        reuseExistingChunk: true
                    }
            },
        }
    };

}else{
    buildConfig.plugins = [];
}

module.exports = {
    css: {
        loaderOptions: {
            less: {
                javascriptEnabled: true
            },
            css: {
                modules: {
                    localIdentName: '[name]-[hash]'
                },
                localsConvention: 'camelCaseOnly'
            },
        },
        extract:false,
        sourceMap: false,
        requireModuleExtension: true
    },
    publicPath: afsProjectInfo.publicPath,
    lintOnSave: process.env.NODE_ENV==='development',
    outputDir: 'dist/approve',
    chainWebpack: config => {
        // key,value自行定义，比如.set('@@', resolve('src/components'))
        config.resolve.alias
            .set('@', resolve('src'))
            .set('_c', resolve('src/components'))
            .set('_p', resolve('src/projects'));
        config
            .plugin('define')
            .tap(args => {
                args[0]._AFS_CONFIG = JSON.stringify(afsInfo)
                args[0]._AFS_PROJECT_CONFIG = JSON.stringify(afsProjectInfo)
                return args
            });
        if(process.env.NODE_ENV!=='development'){
            config.module.rule('vue').exclude.add(resolve("src/demo")).end();
            config.optimization.minimizer('terser').tap((args)=>{
                args[0].terserOptions.compress.drop_console = true;
                args[0].terserOptions.compress.drop_debugger = true;
                return args;
            })
        }
        config.plugins.delete('prefetch')
        config.plugins.delete('preload')
    },
    // 设为false打包时不生成.map文件
    productionSourceMap: (afsInfo.env === 'dev'),
    configureWebpack: smp.wrap(buildConfig),
    parallel: require('os').cpus().length > 1,
    devServer: {
        proxy: {...afs_proxy_config.getProxyInfo()},
        disableHostCheck: true,
        overlay: {
            warning: false,
            errors: false
        },
        headers: {
            'Access-Control-Allow-Origin': '*',
            "Access-Control-Allow-Headers":"*",
            "Access-Control-Allow-Methods":"PUT,POST,GET,DELETE,OPTIONS",
            "Cross-Origin-Resource-Policy":"cross-origin"
        },
        allowedHosts: [
            '*'
        ]
    }
}
