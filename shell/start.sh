#!/bin/bash

if [ "$LOGER_HOME" ]; then
  export APP_LOG_HOME="$LOGER_HOME"
else
  export APP_LOG_HOME="/app/logs"
fi
export SKYWALKING_LOG="$APP_LOG_HOME/agent/"
export SENTINEL_LOG="$APP_LOG_HOME/csp/"
export JM_SNAPSHOT_PATH="$APP_LOG_HOME/nacos/"
export JM_LOG_PATH="$APP_LOG_HOME/nacos/"
export NACOS_CACHE_DIR="$APP_LOG_HOME/nacos/cache/"
export NACOS_LOG_PATH="$APP_LOG_HOME/nacos/config/"
export AFS_CLASS_PATH="${docker.apphome}/${package.name}/resources:${docker.apphome}/${package.name}/classes:${docker.apphome}/${package.name}/libs/*"
export MAIN_CLASS="${MAIN_CLASS}"

export AFS_LOG_FORDER="/app/logs"
export AFS_LOG_HOME="$APP_LOG_HOME"

export START_COMMAND="java -server --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.rmi/sun.rmi.transport=ALL-UNNAMED  -DAFS_LICENSE=$AFS_LICENSE"
if [ "$SKYWALKING_AGENT" ]; then
    export SKYWALKING="${SKYWALKING_AGENT}"
fi
if [ "$JAVA_OPTS" ]; then
    START_COMMAND="$START_COMMAND $JAVA_OPTS"
else
    START_COMMAND="$START_COMMAND -Xmx${run.jvm.size} -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=340M -XX:MaxDirectMemorySize=64M  -Xss256k "
fi
if [ "$SKYWALKING" ]; then
    START_COMMAND="$START_COMMAND -javaagent:$SKYWALKING"
fi

if [ "$SKYWALKING_LOG" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.logging.dir=$SKYWALKING_LOG"
fi


if [ "$SW_AGENT_NAMESPACE" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.agent.namespace=$SW_AGENT_NAMESPACE"
fi

if [ "$SW_AGENT_NAME" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.agent.service_name=$SW_AGENT_NAME"
fi

if [ "$SW_AGENT_COLLECTOR_BACKEND_SERVICES" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.collector.backend_service=$SW_AGENT_COLLECTOR_BACKEND_SERVICES"
fi

if [ "$SW_AGENT_AUTHENTICATION"]; then
    START_COMMAND="$START_COMMAND -Dskywalking.agent.authentication=$SW_AGENT_AUTHENTICATION"
fi
if [ "$SW_GRPC_REPORTER_SERVER_HOST" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.plugin.toolkit.log.grpc.reporter.server_host=$SW_GRPC_REPORTER_SERVER_HOST"
fi


if [ "$SW_GRPC_REPORTER_SERVER_PORT" ]; then
    START_COMMAND="$START_COMMAND -Dskywalking.plugin.toolkit.log.grpc.reporter.server_port=$SW_GRPC_REPORTER_SERVER_PORT"
fi


if [ "$LOG_STASH" ]; then
    START_COMMAND="$START_COMMAND -Dlogstash.address=$LOG_STASH"
fi


if [ "$LOG_FORMAT" ]; then
    START_COMMAND="$START_COMMAND -Dafs.logFormat=$LOG_FORMAT"
fi


if [ "$SKYWALKING_LOG_COLLECTOR" ]; then
    START_COMMAND="$START_COMMAND -Dafs.log.skywalking=$SKYWALKING_LOG_COLLECTOR"
fi


START_COMMAND="$START_COMMAND -Dcsp.sentinel.log.use.pid=true -Djava.awt.headless=true "
START_COMMAND="$START_COMMAND -DJM.SNAPSHOT.PATH=$JM_SNAPSHOT_PATH"
START_COMMAND="$START_COMMAND -DJM.LOG.PATH=$JM_LOG_PATH"
START_COMMAND="$START_COMMAND -Dcom.alibaba.nacos.naming.cache.dir=$NACOS_CACHE_DIR"
START_COMMAND="$START_COMMAND -Dnacos.logging.path=$NACOS_LOG_PATH"
START_COMMAND="$START_COMMAND -cp $AFS_CLASS_PATH"
START_COMMAND="$START_COMMAND $MAIN_CLASS"
START_COMMAND="$START_COMMAND --com.ruicar.afs.cloud.log.folder=$AFS_LOG_FORDER"
START_COMMAND="$START_COMMAND --com.ruicar.afs.cloud.log.home=$AFS_LOG_HOME"

if [ "$NACOS_SERVER" ]; then
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.serverAddr=$NACOS_SERVER"
fi


if [ "$NACOS_NAMESPACE" ]; then
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.config.namespace=$NACOS_NAMESPACE"
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.discovery.namespace=$NACOS_NAMESPACE"
fi

if [ "$PARAM_NAMESPACE" ]; then
    START_COMMAND="$START_COMMAND --com.ruicar.afs.cloud.param.namespace=$PARAM_NAMESPACE"
fi

if [ "$NACOS_USERNAME" ]; then
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.discovery.username=$NACOS_USERNAME"
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.config.username=$NACOS_USERNAME"
fi


if [ "$NACOS_PASSWORD" ]; then
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.discovery.password=$NACOS_PASSWORD"
    START_COMMAND="$START_COMMAND --spring.cloud.nacos.config.password=$NACOS_PASSWORD"
fi

if [ "$SPRING_PROFILES_ACTIVE" ]; then
    START_COMMAND="$START_COMMAND --spring.profiles.active=$SPRING_PROFILES_ACTIVE"
fi
if [ "$SERVER_PORT" ]; then
    START_COMMAND="$START_COMMAND --server.port=$SERVER_PORT"
fi

echo "$START_COMMAND"
$START_COMMAND
exec "$@"
