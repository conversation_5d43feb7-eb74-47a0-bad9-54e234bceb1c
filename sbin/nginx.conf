upstream gateway_app {
   server gateway-service:80;
}
server {
    listen       80;
    access_log  /app/logs/access.log;
    error_log  /app/logs/error.log  error;
    large_client_header_buffers 4 64k;
    client_header_buffer_size 64k;
    location  /approve/api/ {
        proxy_pass http://gateway_app/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        add_header ui-version package_version;
    }
    location /approve {
        root   /app/web;
        add_header ui-version package_version;
        try_files $uri $uri/ /approve/index.html;
        index index.html;
    }
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   html;
    }
}
