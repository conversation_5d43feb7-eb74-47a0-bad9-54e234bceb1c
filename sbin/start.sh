export PACKAGE_VERSION=`cat /app/web/approve/index.html | grep -oE "<meta name=package-version content=([0-9]+|\.)+>" | grep -oE "([0-9]+|\.)+" -o`
if [ -z "$PACKAGE_VERSION" ]; then
  export PACKAGE_VERSION=`cat /app/web/approve/index.html | grep -oE "<meta name=package-version content=([0-9]+|\.)+>" | grep -oE "([0-9]+|\.)+" -o`
fi

if [ -n "$PACKAGE_VERSION" ] ; then
  sed -i "s|package_version|$PACKAGE_VERSION|g" /opt/nginx/conf.d/approve.conf
fi

if [ -z "$PACKAGE_VERSION" ]; then
  sed -i "s|package_version|1.0.0.0.0|g" /opt/nginx/conf.d/approve.conf
fi
nginx -g 'daemon off;'
